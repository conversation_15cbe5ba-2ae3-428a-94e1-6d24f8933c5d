package com.optimaitalia.controller;

import com.optimaitalia.model.wrappers.dilazione.DilazioneWrapper;
import com.optimaitalia.model.wrappers.dilazione.RichiediDilazione;
import com.optimaitalia.model.wrappers.dilazione.RichiediDilazioneRequest;
import com.optimaitalia.service.DilazioneClienteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;

@RestController
@RequestMapping(path = "/api")
public class DilazioneClienteController {

    private DilazioneClienteService dilazioneClienteService;

    @Autowired
    public DilazioneClienteController(DilazioneClienteService dilazioneClienteService) {
        this.dilazioneClienteService = dilazioneClienteService;
    }


    @GetMapping(path = "/get-dilazione-cliente")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public DilazioneWrapper getDiazolineClienteDetail(@RequestHeader("clientid") String clientId) {

        return dilazioneClienteService.getDilazioneClienteDetail(clientId);
    }

    @PostMapping(path = "/get-richiedi-dilazione", produces = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public RichiediDilazione isRichiedaDilazioneEnabled(@RequestParam("clientId") String clientId) {
        return dilazioneClienteService.getRichiediDilazioneDetail(clientId);
    }

    @PostMapping(path = "/save-richiedi-dilazione", produces = MediaType.APPLICATION_JSON_VALUE)
    public LinkedHashMap saveRichiediDilazione(@RequestBody RichiediDilazioneRequest request,
                                               @RequestParam("clientId") String clientId) {
        return dilazioneClienteService.saveRichiediDilazione(request, clientId);
    }
}
