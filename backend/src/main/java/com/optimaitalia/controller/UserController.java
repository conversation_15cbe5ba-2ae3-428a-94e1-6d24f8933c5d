package com.optimaitalia.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.optima.security.model.userData.UserData;
import com.optimaitalia.model.Chart;
import com.optimaitalia.model.Contracts;
import com.optimaitalia.model.services.Service;
import com.optimaitalia.model.wrappers.contoRelux.ControReluxRequest;
import com.optimaitalia.model.wrappers.invoice.Invoice;
import com.optimaitalia.model.wrappers.offer.Offer;
import com.optimaitalia.model.wrappers.publicAdministrator.BillingCenterInformation;
import com.optimaitalia.model.wrappers.user.response.GetLatestCreditPolicyStatusResponse;
import com.optimaitalia.model.wrappers.user.response.UserClusterInfo;
import com.optimaitalia.service.InformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(path = "/api")
public class UserController {

    private final InformationService informationService;

    @Autowired
    public UserController(InformationService informationService) {
        this.informationService = informationService;
    }

    @GetMapping(path = "/chartPie")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<Chart> chart(@RequestHeader("clientid") String clientId) {
        return informationService.getChartInfo(clientId);
    }

    @GetMapping(path = "/userData")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public UserData userData(@RequestHeader("clientid") String clientId) {
        return informationService.getUserData(clientId);
    }

    @GetMapping(path = "/creditCardStatus")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public Boolean isCreditCardEnabled(@RequestHeader("clientid") String clientId) {
        return informationService.checkIfCreditCardEnabled(clientId);
    }

    @GetMapping(path = "/userContracts")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<Contracts> userContracts(@RequestHeader("clientid") String clientId) {
        return informationService.getContractsData(clientId);
    }

    @GetMapping(path = "/services/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<Service> getServices(@PathVariable("clientId") String clientId) {
        return informationService.getServices(clientId);
    }

    @GetMapping(path = "/invoices")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<List<Invoice>> getInvoices(@RequestHeader("clientid") String clientId) {
        return new ResponseEntity<>(informationService.getInvoicesData(clientId), HttpStatus.OK);
    }

    @GetMapping("/invoices/billing-center/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public BillingCenterInformation getBillingCenterInformation(@PathVariable("clientId") String clientId) {
        return informationService.getBillingCenterInformation(clientId);
    }

    @GetMapping(path = "/saldo")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<Object> getSaldo(@RequestHeader("clientid") String clientId) {
        return new ResponseEntity<>(informationService.getSaldo(clientId), HttpStatus.OK);
    }

    @GetMapping(path = "/saldoInScadenza")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<Object> getSaldoInScadenza(@RequestHeader("clientid") String clientId) {
        return new ResponseEntity<>(informationService.getSaldoInScadenza(clientId), HttpStatus.OK);
    }

    @GetMapping(path = "/shipment")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity getShipment(@RequestHeader("clientid") String clientId) {
        return new ResponseEntity<>(informationService.getShipment(clientId), HttpStatus.OK);
    }

    @GetMapping(path = "/accounting")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity getContabile(@RequestHeader("clientid") String clientId) {
        return new ResponseEntity<>(informationService.getContabileData(clientId), HttpStatus.OK);
    }

    @PostMapping("/offer/data")
    @PreAuthorize("#request.clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#request.clientId, authentication.principal.uid)")
    public List<Offer> getOffersData(@RequestBody ControReluxRequest request) {
        return this.informationService.getOffersData(request.getClientId());
    }

    @GetMapping("/user/cluster/info")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<UserClusterInfo> userClusterInfos(@RequestParam("clientId") String clientId) {
        return informationService.findUserClusterInfo(clientId);
    }

    @PostMapping(value = "/user/getUserCodeFromIncidentEvent/{clientId}", produces = {"text/plain"})
    @ResponseBody
    @PreAuthorize("#clientId==authentication.principal.uid")
    public String getUserCodeFromIncidentEvent(@PathVariable String clientId) throws JsonProcessingException {
        return informationService.getUserCodeFromIncidentEvent(clientId);
    }

    @GetMapping("/user/getLatestCreditPolicyStatus/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public GetLatestCreditPolicyStatusResponse getLatestCreditPolicyStatus(@PathVariable("clientId") String clientId) {
        return informationService.getLatestCreditPolicyStatus(clientId);
    }
}
