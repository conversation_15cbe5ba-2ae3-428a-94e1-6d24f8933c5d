package com.optimaitalia.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.optima.common.exceptions.ValidateException;
import com.optimaitalia.model.wrappers.support.RecontactRequest;
import com.optimaitalia.service.SupportService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/support")
public class SupportController {

    private final SupportService supportService;

    public SupportController(SupportService supportService) {
        this.supportService = supportService;
    }

    @PostMapping("/recontact")
    public ResponseEntity recontactRequest(@RequestBody RecontactRequest request) throws ValidateException, JsonProcessingException {
        return supportService.recontactRequest(request);
    }

    @GetMapping("/checkIncident/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public Boolean recontactRequest(@PathVariable String clientId) throws JsonProcessingException {
        return supportService.checkIncidentEvent(clientId);
    }

    @GetMapping("/checkIncidentChangePromo/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public Boolean checkIncidentChangePromo(@PathVariable String clientId) throws JsonProcessingException {
        return supportService.checkIncidentEventChangePromoMese(clientId);
    }

    @GetMapping("/checkIncidentRichiestaPortabilita/{clientId}/{msisdnId}")
    @PreAuthorize("#clientId==authentication.principal.uid")
    public Boolean checkIncidentRichiestaPortabilita(@PathVariable String clientId, @PathVariable String msisdnId) throws JsonProcessingException {
        return supportService.checkIncidentRichiestaPortabilita(clientId, msisdnId);
    }

    @GetMapping("/checkOpenIncidentEventPagamentoFlessibile/{clientId}")
    @PreAuthorize("#clientId==authentication.principal.uid")
    public Boolean checkOpenIncidentEventPagamentoFlessibile(@PathVariable String clientId) throws JsonProcessingException {
        return supportService.checkOpenIncidentEventPagamentoFlessibile(clientId);
    }

    @GetMapping("/checkOpenIncidentEventCrossSelling/{clientId}")
    @PreAuthorize("#clientId==authentication.principal.uid")
    public Boolean checkOpenIncidentEventCrossSelling(@PathVariable String clientId) throws JsonProcessingException {
        return supportService.checkOpenIncidentEventCrossSelling(clientId);
    }
}
