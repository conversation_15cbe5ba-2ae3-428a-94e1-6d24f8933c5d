package com.optimaitalia.controller;

import com.optima.security.model.UserEntity;
import com.optimaitalia.model.forgotPassword.ForgotPasswordFormRequest;
import com.optimaitalia.model.forgotPassword.ForgotPasswordRequest;
import com.optimaitalia.model.forgotPassword.ForgotPasswordResponse;
import com.optimaitalia.service.RestorePasswordService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/psw")
public class ForgotPasswordController {

    private final RestorePasswordService restorePasswordService;

    public ForgotPasswordController(RestorePasswordService restorePasswordService) {
        this.restorePasswordService = restorePasswordService;

    }

    @PostMapping("/forgotPassword")
    public ForgotPasswordResponse restorePassword(@RequestBody ForgotPasswordRequest request) {
        return this.restorePasswordService.restorePassword(request);
    }

    @GetMapping("/isExpired")
    public List<UserEntity> resetPassword(@RequestParam(value = "access_token", required = false) String token) {
        return this.restorePasswordService.findByToken(token);
    }

    @PostMapping("/setPassword")
    public ForgotPasswordResponse setPassword(@RequestBody ForgotPasswordFormRequest requestBody, HttpServletRequest request) {
        return restorePasswordService.resetPassword(requestBody, request.getHeader("Auth"));
    }
}
