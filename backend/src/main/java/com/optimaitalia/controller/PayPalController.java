package com.optimaitalia.controller;

import com.optimaitalia.model.wrappers.paypal.PayPalActivation;
import com.optimaitalia.model.wrappers.paypal.PayPalActivationDilazione;
import com.optimaitalia.model.wrappers.paypal.PayPalActivationRicarica;
import com.optimaitalia.service.PayPalService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController()
@RequestMapping("/api/paypal")
public class PayPalController {

    private final PayPalService payPalService;

    public PayPalController(PayPalService payPalService) {
        this.payPalService = payPalService;
    }

    @PostMapping
    @PreAuthorize("#payPal.CodiceCliente.toString()==authentication.principal.uid")
    public ResponseEntity postPayPalActivation(@RequestBody PayPalActivation payPal){
        return payPalService.postPayPalActivation(payPal);
    }

    @PostMapping("/ricarica")
    @PreAuthorize("#payPal.CodiceCliente.toString()==authentication.principal.uid")
    public ResponseEntity postPayPalActivationRicarica(@RequestBody PayPalActivationRicarica payPal){
        return payPalService.postPayPalActivationRicarica(payPal);
    }
    @PostMapping("/dilazioni")
    @PreAuthorize("#payPal.CodiceCliente.toString()==authentication.principal.uid")
    public ResponseEntity postPayPalActivationDilazione(@RequestBody PayPalActivationDilazione payPal){
        return payPalService.postPayPalActivationDilazione(payPal);
    }

    @GetMapping("/isOptimaNumber")
    @PreAuthorize("#clientId.toString()==authentication.principal.uid")
    public Boolean isOptimaNumber(@RequestParam Long simNumber, @RequestParam Long clientId){
        return payPalService.isOptimaNumber(simNumber);
    }
}