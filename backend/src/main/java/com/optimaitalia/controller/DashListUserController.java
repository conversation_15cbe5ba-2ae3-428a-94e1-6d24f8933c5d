package com.optimaitalia.controller;

import com.optimaitalia.service.DashListUserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/dashlist")
public class DashListUserController {

    private DashListUserService dashListUserService;

    public DashListUserController(DashListUserService dashListUserService) {
        this.dashListUserService = dashListUserService;
    }

    @GetMapping("/user/exist")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public boolean isDashListUserExist(@RequestParam Long clientId) {
        return dashListUserService.isUserExist(clientId);
    }


}
