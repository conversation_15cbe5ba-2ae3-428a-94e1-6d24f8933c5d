package com.optimaitalia.controller;

import com.optimaitalia.model.IPInfo;
import com.optimaitalia.service.IPService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(path = "/api/ip")
public class IPController {

    private final IPService ipService;

    public IPController(IPService ipService) {
        this.ipService = ipService;
    }

    @GetMapping("/get")
    public ResponseEntity<IPInfo> getIP() {
        return ipService.getIPInfo();
    }
}
