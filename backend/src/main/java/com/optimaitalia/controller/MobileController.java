package com.optimaitalia.controller;

import com.optima.common.exceptions.ValidateException;
import com.optimaitalia.exception.PaymentException;
import com.optimaitalia.model.wrappers.mobile.*;
import com.optimaitalia.model.wrappers.mobile.conracts.CheckContractResponse;
import com.optimaitalia.model.wrappers.mobile.conracts.ContractRecord;
import com.optimaitalia.model.wrappers.mobile.products.Product;
import com.optimaitalia.model.wrappers.mobile.products.ProductDescription;
import com.optimaitalia.model.wrappers.mobile.products.ProductRecord;
import com.optimaitalia.model.wrappers.mobile.products.ProductRecordsRequest;
import com.optimaitalia.model.wrappers.mobile.subscriptions.SubscriptionDetailsAggregation;
import com.optimaitalia.service.MobileService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/mobile")
public class MobileController {

    private final MobileService mobileService;

    public MobileController(MobileService mobileService) {
        this.mobileService = mobileService;
    }

    @GetMapping(value = "/contract/records" )
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<ContractRecord> contractRecords(
            @RequestParam Long clientId,
            @RequestParam(value = "simNumber", required = false) Long simNumber) {
        return simNumber == null
                ? mobileService.findMobileContracts(clientId, null)
                : mobileService.findMobileContracts(null, simNumber);
    }

    @GetMapping(value = "/contrattiData/check")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public CheckContractResponse checkContract(@RequestParam Long clientId, @RequestParam Long prId) {
        return mobileService.getProdPurchPromo(clientId, prId);
    }

    @PostMapping("/product/records/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<ProductRecord> productRecords(@RequestBody ProductRecordsRequest productRecordsRequest, @PathVariable String clientId) {
        return mobileService.findProductRecords(productRecordsRequest, clientId);
    }

    @RequestMapping(value = "/tarifferInternazionali", method = RequestMethod.GET, produces = "application/json")
    public Map<String, Object> getTarifferInternazionali() {
        return mobileService.getInternationalRates();
    }

    @RequestMapping(value = "/getTariffeNazionali", method = RequestMethod.GET, produces = "application/json")
    public Map<String, String> getTariffeNazionali() {
        return mobileService.getTariffeNazionali();
    }

    @PostMapping("/product/records/offers")
//    @PreAuthorize("@permissionServiceImpl.mobileNumberBelongToClient(#productRecordsRequest.subscriptionId, authentication.principal.uid)")
    public List<Product> productOffersRecords(@RequestBody ProductRecordsRequest productRecordsRequest) {
        return mobileService.findProductOffersRecords(productRecordsRequest);
    }

    @GetMapping("/sim-detail/aggregation")
    @PreAuthorize("@permissionServiceImpl.mobileNumberBelongToClient(#msisdn, authentication.principal.uid)")
    public List<SubscriptionDetailsAggregation> subscriptionDetailsAggregations(@RequestParam Long msisdn) {
        return mobileService.findSubscriptionDetailsAggregation(msisdn);
    }

    @GetMapping("/sim-detail/balance")
    @PreAuthorize("@permissionServiceImpl.mobileNumberBelongToClient(#msisdn, authentication.principal.uid)")
    public String getSimBalance(@RequestParam Long msisdn) {
        return mobileService.getSimBalance(msisdn);
    }

//    @GetMapping("/product-activation/records")
//    public List<ProductActivationRecord> productActivationRecord(@RequestParam Long id) {
//        return mobileService.findProductActivationRecord(id);
//    }

    @GetMapping("/product/description")
    public ProductDescription productDescription(@RequestParam Long productId) {
        return mobileService.findProductDescription(productId);
    }

    @PostMapping("/traffic/details")
    @PreAuthorize("@permissionServiceImpl.mobileNumberBelongToClient(#trafficDetailsRequest.msisdnId, authentication.principal.uid)")
    public List<TrafficDetail> trafficDetails(@RequestBody TrafficDetailsRequest trafficDetailsRequest) throws ValidateException {
        return mobileService.findTrafficDetails(trafficDetailsRequest);
    }

    @PostMapping("/activate/option")
    @PreAuthorize("#activateOptionRequest.customerId!=null&&#activateOptionRequest.customerId.toString() == authentication.principal.uid")
    public ActivateOptionResponse activateOption(@RequestBody ActivateOptionRequest activateOptionRequest) throws ValidateException {
        return this.mobileService.activateOption(activateOptionRequest);
    }

    @PostMapping("/change/tariff")
    @PreAuthorize("#activateOptionRequest.customerId!=null&&#activateOptionRequest.customerId.toString() == authentication.principal.uid")
    public ActivateOptionResponse changeTariffPlan(@RequestBody ActivateOptionRequest activateOptionRequest) throws ValidateException {
        return mobileService.changeTariffPlan(activateOptionRequest);
    }

    @GetMapping("/replenish/account")
    @PreAuthorize("T(com.optima.security.constants.TokenType).DISPOSABLE==authentication.principal.tokenType")
    public ModelAndView rechargeSimBalance(@RequestParam Long simNumber, @RequestParam Integer amount) throws ValidateException, PaymentException {
        ModifyBalanceRequest modifyBalanceRequest = new ModifyBalanceRequest();
        modifyBalanceRequest.setSimNumber(simNumber);
        modifyBalanceRequest.setAmount(amount);
        return mobileService.modifyBalance(modifyBalanceRequest);
    }

    @PostMapping("/ricarica/voucher")
    @PreAuthorize("#request.idCliente!=null&&#request.idCliente.toString() == authentication.principal.uid")
    public String topUpSimWithVoucherCode(@RequestBody TopUpSimWithVoucherCodeRequest request) {
        return mobileService.topUpSimWithVoucherCode(request);
    }
    @GetMapping("/operators/{clientId}")
    @PreAuthorize("#clientId==authentication.principal.uid")
    public List<MobileOperator> getMobileOperators(@PathVariable String clientId) {
        return mobileService.getMobileOperators(Long.valueOf(clientId));
    }
}
