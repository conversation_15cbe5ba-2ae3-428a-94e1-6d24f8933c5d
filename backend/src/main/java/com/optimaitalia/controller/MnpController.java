package com.optimaitalia.controller;

import com.optimaitalia.model.wrappers.mnp.MnpActivation;
import com.optimaitalia.service.MnpService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController()
@RequestMapping("/api/mnp")
public class MnpController {

    private final MnpService mnpService;

    public MnpController(MnpService mnpService) {
        this.mnpService = mnpService;
    }

    @PostMapping
    @PreAuthorize("#body.customerId.toString()==authentication.principal.uid")
    public ResponseEntity<?> postMnpActivation(@RequestBody MnpActivation body) {
        return mnpService.postMnpActivation(body);
    }

    @PostMapping(value = "/upload-file", consumes = {"multipart/form-data"})
    public ResponseEntity<?> mnpUploadFile(@RequestPart("file") MultipartFile file) throws IOException {
        return mnpService.mnpUploadFile(file);
    }
}
