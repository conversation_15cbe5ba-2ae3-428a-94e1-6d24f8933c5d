package com.optimaitalia.utils.dateDeserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class BillingDateDeserializer extends JsonDeserializer<Date> {

    private SimpleDateFormat sdf;

    public BillingDateDeserializer() {
        sdf = new SimpleDateFormat("MMM dd yyyy hh:mma", Locale.ITALY);
    }

    @Override
    public Date deserialize(<PERSON>sonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        try {
            return sdf.parse(jsonParser.getText());
        } catch (ParseException e) {
            return null;
        }
    }
}

