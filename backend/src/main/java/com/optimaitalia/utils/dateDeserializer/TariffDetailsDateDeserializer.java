package com.optimaitalia.utils.dateDeserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

public class TariffDetailsDateDeserializer extends JsonDeserializer<Date> {

    private final DateFormat dateFormat;

    public TariffDetailsDateDeserializer() {
        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ITALY);
        dateFormat.setTimeZone(TimeZone.getTimeZone("Europe/Rome"));
    }

    @Override
    public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        try {
            return dateFormat.parse(jsonParser.getText());
        } catch (ParseException e) {
            return null;
        }
    }
}
