package com.optimaitalia.utils.dateDeserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class InitialBonusDateDeserializer extends JsonDeserializer<Date> {

    private SimpleDateFormat sdf;

    public InitialBonusDateDeserializer() {
        sdf = new SimpleDateFormat("MMM yyyy", Locale.ITALY);
    }

    @Override
    public Date deserialize(JsonPars<PERSON> jsonParser, DeserializationContext deserializationContext) throws IOException {
        try {
            return sdf.parse(jsonParser.getText());
        } catch (ParseException e) {
            return null;
        }
    }
}