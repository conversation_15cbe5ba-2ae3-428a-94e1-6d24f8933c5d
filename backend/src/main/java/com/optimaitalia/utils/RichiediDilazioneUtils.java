package com.optimaitalia.utils;

import java.util.HashMap;
import java.util.Map;

public class RichiediDilazioneUtils {

    public static Map<String, String> casisticaMap = new HashMap<String, String>(){{
       put("Problemi di liquidità", "d875abca-ec02-e611-99e2-005056bb51fe");
       put("Presenza Conguagli a sfavore", "8d57bfd8-ec02-e611-99e2-005056bb51fe");
       put("Stima errata della taglia", "0f58cfde-ec02-e611-99e2-005056bb51fe");
       put("Presenza CMOR", "018038e6-ec02-e611-99e2-005056bb51fe");
       put("Altro", "38f37ded-ec02-e611-99e2-005056bb51fe");
       put("Saldo CR negativo", "c7bc8325-b575-4ce0-b07b-4c258f27d655");
       put("Calamità naturale", "e0d8ca02-d953-4406-a678-96aa1e4d61f9");
       put("Taglia PI errata", "facb41c8-0288-420d-a6af-c812d6bdc7ec");
       put("Ricostruzione consumi", "8d7344e2-8da2-4d19-9806-cb6521ca8717");
    }};

    public static Map<String, Integer> cadenzaMap = new HashMap<String, Integer>(){{
        put("30 giorni", 1);
        put("15 giorni", 2);
        put("7 giorni", 3);
        put("giorno fisso", 4);
    }};
}
