package com.optimaitalia.handlers.rest.template;

import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.ResponseErrorHandler;

import java.io.IOException;

import static org.springframework.http.HttpStatus.Series.CLIENT_ERROR;
import static org.springframework.http.HttpStatus.Series.SERVER_ERROR;

public class RestTemplateResponseErrorProcessor implements ResponseErrorHandler {

    private final RestTemplateResponseErrorHandlerProvider handlerProvider;

    public RestTemplateResponseErrorProcessor(RestTemplateResponseErrorHandlerProvider handlerProvider) {
        this.handlerProvider = handlerProvider;
    }

    @Override
    public boolean hasError(ClientHttpResponse response) throws IOException {
        return (response.getStatusCode().series() == CLIENT_ERROR || response.getStatusCode().series() == SERVER_ERROR);
    }

    @Override
    public void handleError(ClientHttpResponse response) throws IOException {
        RestTemplateResponseErrorHandlerFactory handlerFactory;
        if ((handlerFactory = handlerProvider.getHandlerFactory(response.getStatusCode().series())) != null) {
            RestTemplateResponseErrorHandler restTemplateResponseErrorHandler = handlerFactory.get(response.getStatusCode());
            if (restTemplateResponseErrorHandler != null) {
                restTemplateResponseErrorHandler.handleResponseError(response);
            }
        }
    }


}
