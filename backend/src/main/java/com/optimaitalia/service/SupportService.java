package com.optimaitalia.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.optima.common.exceptions.ValidateException;
import com.optimaitalia.model.wrappers.incidentEvent.IncidentEventResponse;
import com.optimaitalia.model.wrappers.support.RecontactRequest;
import org.springframework.http.ResponseEntity;

public interface SupportService {

    ResponseEntity recontactRequest(RecontactRequest recontactRequest) throws ValidateEx<PERSON>, JsonProcessingException;

    IncidentEventResponse recontactIncidentEvent(RecontactRequest recontactRequest) throws ValidateException;

    Boolean checkIncidentEvent(String clientId) throws JsonProcessingException;

    Boolean checkIncidentEventChangePromoMese(String clientId) throws JsonProcessingException;

    Boolean checkIncidentEventDifferentType(String clientId) throws JsonProcessingException;

    Boolean checkIncidentRichiestaPortabilita(String clientId, String msisdnId) throws JsonProcessingException;

    Boolean checkIncidentEventChangeDateScandenza(String clientId) throws JsonProcessingException;

    Boolean checkOpenIncidentEventPagamentoFlessibile(String clientId) throws JsonProcessingException;

    Boolean checkOpenIncidentEventCrossSelling(String clientId) throws JsonProcessingException;
}
