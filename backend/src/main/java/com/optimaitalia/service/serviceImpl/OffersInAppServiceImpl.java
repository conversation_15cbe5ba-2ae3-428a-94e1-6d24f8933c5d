package com.optimaitalia.service.serviceImpl;

import com.optima.security.service.SecurityService;
import com.optimaitalia.model.offer5g.Offer5GRequest;
import com.optimaitalia.model.offer5g.Offer5GResponse;
import com.optimaitalia.service.OffersInAppService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@RequiredArgsConstructor
public class OffersInAppServiceImpl implements OffersInAppService {

    private static final Logger logger = LogManager.getLogger(OffersInAppServiceImpl.class);

    @Value("${restdata.urls.app-offer-5G}")
    private String offer5GUrl;

    private final RestTemplate restTemplate;
    private final SecurityService securityService;

    @Override
    public ResponseEntity<Offer5GResponse> getOffer5GData(String clientId, String subscriptionId) {
        Long subscriptionIdLong = subscriptionId != null ? Long.parseLong(subscriptionId) : null;
        Offer5GRequest request = new Offer5GRequest(Long.parseLong(clientId), subscriptionIdLong);
        HttpEntity<Offer5GRequest> httpEntity = new HttpEntity<>(request, createHeaders());
        logger.info("Fetching 5G offer data for client id: {} and subscription id: {}", clientId, subscriptionId);
        return restTemplate.exchange(offer5GUrl, HttpMethod.POST, httpEntity, Offer5GResponse.class);
    }

    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String token = securityService.getToken().getAccessToken();
        headers.set("Authorization", "Bearer " + token);
        return headers;
    }
}
