package com.optimaitalia.service.serviceImpl;

import com.optima.security.service.SecurityService;
import com.optimaitalia.model.auditLog.AuditLogRequest;
import com.optimaitalia.model.auditLog.CustomerData;
import com.optimaitalia.service.AuditLogService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.transaction.Transactional;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Map;

@Service
@Transactional
public class AuditLogServiceImpl implements AuditLogService {

    private static final Logger logger = LogManager.getLogger(AuditLogServiceImpl.class);
    private final RestTemplate restTemplate;
    private final SecurityService securityService;

    @Value("${restdata.urls.auditLog}")
    private String auditLogUrl;

    public AuditLogServiceImpl(RestTemplate restTemplate, SecurityService securityService) {
        this.restTemplate = restTemplate;
        this.securityService = securityService;
    }

    @Override
    public void sendLog(String action, String clientId, String sourceIp, String Url, String resourceId, String resourceType) {
        AuditLogRequest body = new AuditLogRequest(null, setEventType(Url, action),
                new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").format(new Timestamp(System.currentTimeMillis())),
                clientId, "SELFCARE", "CALL_API", clientId, new CustomerData(action, Url),
                "CUSTOMER", resourceId, resourceType, sourceIp);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity<?> entity = new HttpEntity<Object>(body, headers);
        logger.info("Sending information to auditLog service");
        restTemplate.exchange(auditLogUrl, HttpMethod.POST, entity, Map.class);
    }

    public String setEventType(String Url, String action) {
        return Url.contains("pdf") ? "AUDIT_FILE_DOWNLOAD" : action.equals("GET") ? "AUDIT_USER_DISPLAY" : "AUDIT_USER_WRITE";
    }
}
