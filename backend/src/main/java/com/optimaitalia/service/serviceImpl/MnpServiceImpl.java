package com.optimaitalia.service.serviceImpl;

import com.optima.security.service.SecurityService;
import com.optimaitalia.model.wrappers.mnp.MnpActivation;
import com.optimaitalia.service.MnpService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Service
public class MnpServiceImpl implements MnpService {

    private static final Logger logger = LogManager.getLogger(UserDataServiceImpl.class);

    private final SecurityService securityService;
    private final RestTemplate restTemplate;

    @Value("${restdata.urls.mnpPostActivation}")
    private String mnpPostActivationUrl;

    @Value("${restdata.urls.mnpUploadFiles}")
    private String mnpUploadFilesUrl;

    public MnpServiceImpl(SecurityService securityService, RestTemplate restTemplate) {
        this.securityService = securityService;
        this.restTemplate = restTemplate;
    }

    @Override
    public ResponseEntity<?> postMnpActivation(MnpActivation body) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> httpEntity = new HttpEntity<>(body, httpHeaders);
        Map response = restTemplate.exchange(mnpPostActivationUrl, HttpMethod.POST, httpEntity, Map.class).getBody();
        logger.info("Activating MNP");
        return new ResponseEntity(response.get("errorStatus"), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<?> mnpUploadFile(MultipartFile file) throws IOException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> body = new HashMap<>();
        body.put("Type", "MNP");
        body.put("IsTypeFolder", true);
        body.put("FileName", file.getOriginalFilename());
        body.put("File", file.getBytes());
        HttpEntity<?> httpEntity = new HttpEntity<>(body, httpHeaders);
        Map response = restTemplate.exchange(mnpUploadFilesUrl, HttpMethod.POST, httpEntity, Map.class).getBody();
        logger.info("Uploading file");
        return new ResponseEntity(response, HttpStatus.OK);
    }
}
