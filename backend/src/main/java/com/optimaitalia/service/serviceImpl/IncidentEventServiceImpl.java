package com.optimaitalia.service.serviceImpl;

import com.optima.common.exceptions.ValidateException;
import com.optima.common.validators.OvalValidator;
import com.optima.security.service.SecurityService;
import com.optimaitalia.model.wrappers.incidentEvent.IncidentEventRequest;
import com.optimaitalia.model.wrappers.incidentEvent.IncidentEventResponse;
import com.optimaitalia.service.IncidentEventService;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;


@Service
public class IncidentEventServiceImpl implements IncidentEventService {

    private static final Logger logger = LogManager.getLogger(IncidentEventServiceImpl.class);

    private final RestTemplate restTemplate;

    private final SecurityService securityService;

    private final OvalValidator ovalValidator;

    @Value("${restdata.urls.incident-event}")
    private String customerIncidentEventUrl;

    public IncidentEventServiceImpl(RestTemplate restTemplate, SecurityService securityService, OvalValidator ovalValidator) {
        this.restTemplate = restTemplate;
        this.securityService = securityService;
        this.ovalValidator = ovalValidator;
    }

    @Override
    public IncidentEventResponse customerIncidentEvent(IncidentEventRequest incidentEventRequest) throws ValidateException {
        ovalValidator.validate(incidentEventRequest);
        logger.info("Passing data to incidentEvent service for user with id {}", incidentEventRequest.getCustomerId());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity<IncidentEventRequest> entity = new HttpEntity<>(incidentEventRequest, headers);
        logger.info("Request ["+incidentEventRequest+"]");
        IncidentEventResponse incidentEventResponse = restTemplate.postForObject(customerIncidentEventUrl, entity, IncidentEventResponse.class);
        logger.info("Variation data has been passed. Result: {}", incidentEventResponse);
        return incidentEventResponse;
    }
}
