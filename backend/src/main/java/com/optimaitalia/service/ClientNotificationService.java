package com.optimaitalia.service;

import com.optimaitalia.model.db.ClientNotification;

import java.util.List;

public interface ClientNotificationService {

    List<ClientNotification> getAllClientNotifications(Long clientId);

    List<ClientNotification> getAllClientNotificationsForUserList(List<Long> clientId);

    ClientNotification getClientNotificationById(Long clientId, Long id);

    ClientNotification createOrUpdateNotification(ClientNotification clientNotification);

    void deleteNotificationById(Long Id);

}
