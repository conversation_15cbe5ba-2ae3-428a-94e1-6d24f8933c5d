package com.optimaitalia.service;

import com.optimaitalia.model.wrappers.dilazione.DilazioneWrapper;
import com.optimaitalia.model.wrappers.dilazione.RichiediDilazione;
import com.optimaitalia.model.wrappers.dilazione.RichiediDilazioneRequest;

import java.util.LinkedHashMap;

public interface DilazioneClienteService {

    DilazioneWrapper getDilazioneClienteDetail(String clientId);

    RichiediDilazione getRichiediDilazioneDetail(String clientId);

    LinkedHashMap saveRichiediDilazione(RichiediDilazioneRequest request, String clientId);
}
