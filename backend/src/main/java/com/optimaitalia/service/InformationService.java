package com.optimaitalia.service;

import com.optima.security.model.userData.UserData;
import com.optimaitalia.model.Chart;
import com.optimaitalia.model.Contracts;
import com.optimaitalia.model.Shipment;
import com.optimaitalia.model.services.Service;
import com.optimaitalia.model.wrappers.contabile.Contabile;
import com.optimaitalia.model.wrappers.dilazione.Rate;
import com.optimaitalia.model.wrappers.invoice.Invoice;
import com.optimaitalia.model.wrappers.offer.Offer;
import com.optimaitalia.model.wrappers.publicAdministrator.BillingCenterInformation;
import com.optimaitalia.model.wrappers.user.response.GetLatestCreditPolicyStatusResponse;
import com.optimaitalia.model.wrappers.user.response.UserClusterInfo;

import java.util.List;

public interface InformationService {

    List<Chart> getChartInfo(String clientId);

    UserData getUserData(String clientId);

    List<Contracts> getContractsData(String clientId);

    List<Invoice> getInvoicesData(String clientId);

    BillingCenterInformation getBillingCenterInformation(String clientId);

    List<Rate> getRatesData(String clientId);

    List<Service> getServices(String clientId);

    List<Shipment> getShipment(String clientId);

    Contabile getContabileData(String clientId);

    List<Offer> getOffersData(Long clientId);

    Object getSaldo(String clientId);

    Object getSaldoInScadenza(String clientId);

    Boolean checkIfCreditCardEnabled(String clientId);

    List<UserClusterInfo> findUserClusterInfo(String clientId);

    String getUserCodeFromIncidentEvent(String clientId);

    GetLatestCreditPolicyStatusResponse getLatestCreditPolicyStatus(String clientId);
}

