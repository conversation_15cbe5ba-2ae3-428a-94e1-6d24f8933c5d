package com.optimaitalia.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.optima.common.exceptions.ValidateException;
import com.optimaitalia.model.wrappers.offer.*;
import com.optimaitalia.model.wrappers.offer.request.ChangePromoMeseRequest;
import com.optimaitalia.model.wrappers.offer.request.CheckTariffRequest;
import com.optimaitalia.model.wrappers.user.response.ChangePersonalDataResponse;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;

public interface OffersService {

    InitialBonusProgress loadInitialBonusProgress(String clientId, String billingId);

    Map governanceOfferClientBundleDetails(String clientId, String billingId);

    List<ClientBundle> governanceOfferClientBundle(String clientId, String billingId);

    List<ClientBundleDetails> getClientBundleDetails(String clientId, String billingId);

    ResponseEntity checkYourTariff(CheckTariffRequest checkTariffRequest) throws ValidateException;

    List<PromoMeseOffWrapper> promoMeseOff(String clientId);

    ChangePersonalDataResponse changePromoMeseOff(ChangePromoMeseRequest changePromoMeseRequest) throws JsonProcessingException;
}
