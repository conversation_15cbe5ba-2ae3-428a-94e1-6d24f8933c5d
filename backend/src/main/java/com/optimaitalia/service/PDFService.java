package com.optimaitalia.service;

import org.springframework.http.ResponseEntity;

public interface PDFService {

    ResponseEntity downloadTrafficoVoceFile(Long invoiceId, String clientId);

    ResponseEntity getFile(Long invoiceId, String clientId);

    ResponseEntity downloadTariffTransparencyPDF(String clientId, Long contractId);

    ResponseEntity getFileFromResource(String name);

    ResponseEntity downloadSegnalazionePDF(String clientId, String fileName);

    ResponseEntity<?> downloadContractsPDF(String spRelativeUri);

    ResponseEntity<?> downloadYearlyReportExcelFile(String clientId, String year);

    ResponseEntity downloadInvoicePDF(String spRelativeUri);

    ResponseEntity<?> downloadCondominiYearlyReportExcelFile(String fiscalCode, String year);
}
