package com.optimaitalia.service;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({
        "IdConsumo",
        "dataConsumo",
        "ora1",
        "ora2",
        "ora3",
        "ora4",
        "ora5",
        "ora6",
        "ora7",
        "ora8",
        "ora9",
        "ora10",
        "ora11",
        "ora12",
        "ora13",
        "ora14",
        "ora15",
        "ora16",
        "ora17",
        "ora18",
        "ora19",
        "ora20",
        "ora21",
        "ora22",
        "ora23",
        "ora24"
})
public abstract class ExportConsuptionFormat {

    @JsonProperty("dataConsumo")
    private String dataConsumo;

    @JsonProperty("ora1")
    private String ora1;

    @JsonProperty("ora2")
    private String ora2;

    @JsonProperty("ora3")
    private String ora3;

    @JsonProperty("ora4")
    private String ora4;

    @JsonProperty("ora5")
    private String ora5;

    @JsonProperty("ora6")
    private String ora6;

    @JsonProperty("ora7")
    private String ora7;

    @JsonProperty("ora8")
    private String ora8;

    @JsonProperty("ora9")
    private String ora9;

    @JsonProperty("ora10")
    private String ora10;

    @JsonProperty("ora11")
    private String ora11;

    @JsonProperty("ora12")
    private String ora12;

    @JsonProperty("ora13")
    private String ora13;

    @JsonProperty("ora14")
    private String ora14;

    @JsonProperty("ora15")
    private String ora15;

    @JsonProperty("ora16")
    private String ora16;

    @JsonProperty("ora17")
    private String ora17;

    @JsonProperty("ora18")
    private String ora18;

    @JsonProperty("ora19")
    private String ora19;

    @JsonProperty("ora20")
    private String ora20;

    @JsonProperty("ora21")
    private String ora21;

    @JsonProperty("ora22")
    private String ora22;

    @JsonProperty("ora23")
    private String ora23;

    @JsonProperty("ora24")
    private String ora24;

    @JsonIgnore
    @JsonProperty("IdConsumo")
    private String idConsumo;

    @JsonIgnore
    @JsonProperty("ka")
    private String ka;

    @JsonIgnore
    @JsonProperty("kp")
    private String kp;

    @JsonIgnore
    @JsonProperty("kr")
    private String kr;

}
