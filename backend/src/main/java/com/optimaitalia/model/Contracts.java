package com.optimaitalia.model;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.ContractsDateDeserializer;

import java.util.Date;


public class Contracts {

    private Long id;

    private Long idContrattoPI;

    private Date dataStipula;
    private String descrizioneTipoContratto;

    private String spRelativeUri;

    private Long clientId;

    private String canale;

    @JsonProperty("id")
    public Long getId() {
        return id;
    }

    @JsonProperty("descTipoContratto")
    public String getDescrizioneTipoContratto() {
        return descrizioneTipoContratto;
    }

    @JsonProperty("descrizioneTipoContratto")
    public void setDescrizioneTipoContratto(String descrizioneTipoContratto) {
        this.descrizioneTipoContratto = descrizioneTipoContratto;
    }

    @JsonProperty("idContratto")
    public void setId(Long id) {
        this.id = id;
    }

    public Long getIdContrattoPI() {
        return idContrattoPI;
    }

    public void setIdContrattoPI(Long idContrattoPI) {
        this.idContrattoPI = idContrattoPI;
    }

    @JsonProperty("dataStipula")
    public Date getDataStipula() {
        return dataStipula;
    }

    @JsonProperty("dataStipula")
    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public void setDataStipula(Date dataStipula) {
        this.dataStipula = dataStipula;
    }

    @JsonProperty("clientId")
    public Long getClientId() {
        return clientId;
    }

    @JsonProperty("idCliente")
    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    @JsonProperty("spRelativeUri")
    public String getSpRelativeUri() {
        return spRelativeUri;
    }

    public void setSpRelativeUri(String spRelativeUri) {
        this.spRelativeUri = spRelativeUri;
    }

    @JsonProperty("canale")
    public String getCanale() {
        return canale;
    }

    public void setCanale(String canale) {
        this.canale = canale;
    }
}
