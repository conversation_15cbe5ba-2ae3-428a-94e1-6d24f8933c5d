package com.optimaitalia.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.JacksonInvoiceDateDeserializer;

import java.util.Date;
import java.util.List;

public class Shipment {

    private Long id;

    private Date saterDate;

    private String cf;

    private String piva;

    private ShipmentAddress shipmentAddress;

    private ShipmentHeader shipmentHeader = new ShipmentHeader();
    @JsonProperty("listArticoli")
    private List<ListArticoli> listArticoli;

    @JsonProperty("id")
    public Long getId() {
        return id;
    }

    @JsonProperty("idspedizione")
    public void setId(Long id) {
        this.id = id;
    }

    @JsonProperty("startDate")
    public Date getSaterDate() {
        return saterDate;
    }

    @JsonProperty("datacreazione")
    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    public void setSaterDate(Date saterDate) {
        this.saterDate = saterDate;
    }

    public String getCf() {
        return cf;
    }

    public void setCf(String cf) {
        this.cf = cf;
    }

    public String getPiva() {
        return piva;
    }

    public void setPiva(String piva) {
        this.piva = piva;
    }

    @JsonProperty("shipmentAddress")
    public ShipmentAddress getShipmentAddress() {
        return shipmentAddress;
    }

    @JsonProperty("recapito")
    public void setShipmentAddress(ShipmentAddress shipmentAddress) {
        this.shipmentAddress = shipmentAddress;
    }

    public ShipmentHeader getShipmentHeader() {
        return shipmentHeader;
    }

    public void setShipmentHeader(ShipmentHeader shipmentHeader) {
        this.shipmentHeader = shipmentHeader;
    }
}
//
//            "idspedizione": 4899,
//                    "datacreazione": "13/06/2017",
//                    "nome": "Giuseppina",
//                    "cognome": "Di Tore  Giuseppina",
//                    "ragionesociale": null,
//                    "cf": "dtrgpp67r45l219n",
//                    "piva": null,
//                    "idsoggettoesterno": 178977,
//                    "tiposoggetto": "Cliente",
//                    "telefono": null,
//                    "note": null,
//                    "cell": null,
//                    "email": null,
//                    "dataConcordataConsegna": null,
//                    "dataAperturaGiacenza": null,
//                    "dataLasciatoAvviso": null,
//                    "numeroGiacenzaBartolini": null,
//                    "recapito": {
//                    "indirizzo": "ARCHIVOLTO VIA 123",
//                    "cap": "80022",
//                    "comune": "Arzano",
//                    "presso": null
//                    },
//                    "listArticoli": [
//                    {
//                    "qta": "1",
//                    "idarticoloSAP": "Sim_optima",
//                    "descrizionearticoloSAP": "Sim_optima",
//                    "stato": "NON COMPLETATA",
//                    "tipointervento": "SOSTITUZIONE"
//                    }
//                    ]