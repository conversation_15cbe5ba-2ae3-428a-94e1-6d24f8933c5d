package com.optimaitalia.model.auditLog;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AuditLogRequest {

    private String messageId;
    private String eventType;
    private String timestamp;
    private String userId;
    private String systemId;
    private String action;
    private String clientId;
    private CustomerData customerData;
    private String userType;
    private String resourceId;
    private String resourceType;
    private String sourceIp;


    public AuditLogRequest(String messageId, String eventType, String timestamp, String userId, String systemId, String action,
                           String clientId, CustomerData customerData, String userType, String resourceId, String resourceType,
                           String sourceIp) {
        this.messageId = messageId;
        this.eventType = eventType;
        this.timestamp = timestamp;
        this.userId = userId;
        this.systemId = systemId;
        this.action = action;
        this.clientId = clientId;
        this.customerData = customerData;
        this.userType = userType;
        this.resourceId = resourceId;
        this.resourceType = resourceType;
        this.sourceIp = sourceIp;
    }
}
