package com.optimaitalia.model.routerInfo;

import com.fasterxml.jackson.annotation.JsonProperty;

public class LineeUserParams {
    @JsonProperty("username")
    private String username;


    private String psw;


    private String phoneNumber;

    public String getUesrname() {
        return username;
    }

    @JsonProperty("psw")
    public String getPsw() {
        return psw;
    }
    @JsonProperty("password")
    public void setPsw(String psw) {
        this.psw = psw;
    }
    @JsonProperty("phoneNumber")
    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
}
