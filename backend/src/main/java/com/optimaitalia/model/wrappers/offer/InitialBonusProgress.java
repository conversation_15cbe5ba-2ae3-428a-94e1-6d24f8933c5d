package com.optimaitalia.model.wrappers.offer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.InitialBonusDateDeserializer;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class InitialBonusProgress {


    private BigDecimal currentInitialBonus;

    private List<ListaMeseAnno> initialBonusLog;

    @JsonProperty("currentInitialBonus")
    public BigDecimal getCurrentInitialBonus() {
        return currentInitialBonus;
    }

    @JsonProperty("BonusIniziale")
    public void setCurrentInitialBonus(BigDecimal currentInitialBonus) {
        this.currentInitialBonus = currentInitialBonus;
    }

    @JsonProperty("initialBonusLog")
    public List<ListaMeseAnno> getInitialBonusLog() {
        return initialBonusLog;
    }

    @JsonProperty("ListaMeseAnno")
    public void setInitialBonusLog(List<ListaMeseAnno> initialBonusLog) {
        this.initialBonusLog = initialBonusLog;
    }

    static class ListaMeseAnno {

        private Date meseAnno;

        private BigDecimal totale;


        @JsonProperty("date")
        public Date getMeseAnno() {
            return meseAnno;
        }

        @JsonProperty("MeseAnno")
        @JsonDeserialize(using = InitialBonusDateDeserializer.class)
        public void setMeseAnno(Date meseAnno) {
            this.meseAnno = meseAnno;
        }

        @JsonProperty("total")
        public BigDecimal getTotale() {
            return totale;
        }

        @JsonProperty("Totale")
        public void setTotale(BigDecimal totale) {
            this.totale = totale;
        }
    }

}
