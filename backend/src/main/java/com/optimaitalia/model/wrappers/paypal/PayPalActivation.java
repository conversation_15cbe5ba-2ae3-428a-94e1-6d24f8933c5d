package com.optimaitalia.model.wrappers.paypal;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class PayPalActivation {

    @JsonProperty("TipoPagamento")
    private String TipoPagamento;

    @JsonProperty("SistemaChiamante")
    private String SistemaChiamante;

    @JsonProperty("Data")
    private String Data;

    @JsonProperty("TotalePagamento")
    private Double TotalePagamento;

    @JsonProperty("Fatture")
    private List<FatturePayPal> Fatture;

    @JsonProperty("URL_OK")
    private String URL_OK;

    @JsonProperty("URL_KO")
    private String URL_KO;

    @JsonProperty("CodiceCliente")
    private Integer CodiceCliente;

    @JsonProperty("RagioneSociale")
    private String RagioneSociale;

    @JsonProperty("CF")
    private String CF;

    @JsonProperty("PIVA")
    private String PIVA;

}