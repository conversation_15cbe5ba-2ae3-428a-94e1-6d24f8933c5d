package com.optimaitalia.model.wrappers.payment;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Ricariche {

    @JsonProperty("NumeroTelefono")
    private String NumeroTelefono;

    @JsonProperty("Importo")
    private Integer Importo;

    @JsonProperty("SubscriptionID")
    private String SubscriptionID;

    @JsonProperty("ChannelId")
    private String ChannelId;
}
