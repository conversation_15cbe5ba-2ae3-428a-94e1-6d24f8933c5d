package com.optimaitalia.model.wrappers.mobile.products;

public class Dettaglio {

    private String codiceOfferta;

    private Integer costo;

    private Integer costoSped;

    private Integer costoAttivazione;

    private Integer costoSenzaPromozione;

    private String descrizioneDettaglio;

    private Integer durata;

    private Integer idCanaleVendita;

    private Long idPromozione;

    public String getCodiceOfferta() {
        return codiceOfferta;
    }

    public void setCodiceOfferta(String codiceOfferta) {
        this.codiceOfferta = codiceOfferta;
    }

    public Integer getCosto() {
        return costo;
    }

    public void setCosto(Integer costo) {
        this.costo = costo;
    }

    public Integer getCostoSped() {
        return costoSped;
    }

    public void setCostoSped(Integer costoSped) {
        this.costoSped = costoSped;
    }

    public Integer getCostoAttivazione() {
        return costoAttivazione;
    }

    public void setCostoAttivazione(Integer costoAttivazione) {
        this.costoAttivazione = costoAttivazione;
    }

    public Integer getCostoSenzaPromozione() {
        return costoSenzaPromozione;
    }

    public void setCostoSenzaPromozione(Integer costoSenzaPromozione) {
        this.costoSenzaPromozione = costoSenzaPromozione;
    }

    public String getDescrizioneDettaglio() {
        return descrizioneDettaglio;
    }

    public void setDescrizioneDettaglio(String descrizioneDettaglio) {
        this.descrizioneDettaglio = descrizioneDettaglio;
    }

    public Integer getDurata() {
        return durata;
    }

    public void setDurata(Integer durata) {
        this.durata = durata;
    }

    public Integer getIdCanaleVendita() {
        return idCanaleVendita;
    }

    public void setIdCanaleVendita(Integer idCanaleVendita) {
        this.idCanaleVendita = idCanaleVendita;
    }

    public Long getIdPromozione() {
        return idPromozione;
    }

    public void setIdPromozione(Long idPromozione) {
        this.idPromozione = idPromozione;
    }
}
