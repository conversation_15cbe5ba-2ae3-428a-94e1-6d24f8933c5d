package com.optimaitalia.model.wrappers.user.requests;

import net.sf.oval.constraint.MaxLength;
import net.sf.oval.constraint.MinLength;
import net.sf.oval.constraint.NotEmpty;
import net.sf.oval.constraint.NotNull;

public class ChangeAddressRequest extends UserDataChangeRequest {

    @NotNull
    @NotEmpty
    @MinLength(10)
    @MaxLength(100)
    private String addressToChange;

    @NotNull
    @NotEmpty
    @MinLength(10)
    @MaxLength(100)
    private String newAddress;

    public String getAddressToChange() {
        return addressToChange;
    }

    public void setAddressToChange(String addressToChange) {
        this.addressToChange = addressToChange;
    }

    public String getNewAddress() {
        return newAddress;
    }

    public void setNewAddress(String newAddress) {
        this.newAddress = newAddress;
    }
}
