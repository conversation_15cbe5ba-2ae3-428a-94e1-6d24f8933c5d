package com.optimaitalia.model.wrappers.user.requests;

public class Change implements Changeable {

    private Integer changeType;

    private String applyDate;

    private Integer changeId;

    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    public String getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(String applyDate) {
        this.applyDate = applyDate;
    }

    public Integer getChangeId() {
        return changeId;
    }

    public void setChangeId(Integer changeId) {
        this.changeId = changeId;
    }


    @Override
    public String toString() {
        return "Change{" +
                "changeType=" + changeType +
                ", applyDate='" + applyDate + '\'' +
                ", changeId=" + changeId +
                '}';
    }
}
