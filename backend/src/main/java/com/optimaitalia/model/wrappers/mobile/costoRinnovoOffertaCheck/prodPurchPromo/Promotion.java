package com.optimaitalia.model.wrappers.mobile.costoRinnovoOffertaCheck.prodPurchPromo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.TariffDetailsDateDeserializer;

import java.util.Date;

public class Promotion {

    @JsonDeserialize(using = TariffDetailsDateDeserializer.class)
    private Date createdOn;
    private Long validityPeriod;
    private String displayName;

    public Date getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(Date createdOn) {
        this.createdOn = createdOn;
    }

    public Long getValidityPeriod() {
        return validityPeriod;
    }

    public void setValidityPeriod(Long validityPeriod) {
        this.validityPeriod = validityPeriod;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
}
