package com.optimaitalia.model.wrappers.mobile;

import net.sf.oval.constraint.Max;
import net.sf.oval.constraint.MaxLength;
import net.sf.oval.constraint.MinLength;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public class MobilePaymentRequest {

    @NotNull
    @MinLength(8)
    @MaxLength(12)
    private Long simNumber;

    @NotNull
    @Min(5)
    @Max(50)
    private BigDecimal amount;

    @NotNull
    @MinLength(3)
    private Long clientId;

    public Long getSimNumber() {
        return simNumber;
    }

    public void setSimNumber(Long simNumber) {
        this.simNumber = simNumber;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }
}
