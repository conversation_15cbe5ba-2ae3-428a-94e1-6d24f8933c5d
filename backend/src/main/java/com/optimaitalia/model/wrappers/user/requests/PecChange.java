package com.optimaitalia.model.wrappers.user.requests;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PecChange extends Change {

    private String startingDate;

    private String fiscalCode;

    private String vatNumber;

    private String pec;

    private String name;

    private String surname;

    private String birthDate;

    private String documentNumber;

    private String documentType;

    private String docimentTypeDescription;

    private boolean isRCU;

    private boolean flag;

    @JsonProperty("startingdate")
    public String getStartingDate() {
        return startingDate;
    }

    public void setStartingDate(String startingDate) {
        this.startingDate = startingDate;
    }

    @JsonProperty("fiscalcode")
    public String getFiscalCode() {
        return fiscalCode;
    }

    public void setFiscalCode(String fiscalCode) {
        this.fiscalCode = fiscalCode;
    }

    @JsonProperty("vatnumber")
    public String getVatNumber() {
        return vatNumber;
    }

    public void setVatNumber(String vatNumber) {
        this.vatNumber = vatNumber;
    }

    public String getPec() {
        return pec;
    }

    public void setPec(String pec) {
        this.pec = pec;
    }

    @JsonProperty("nome")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("cognome")
    public String getSurname() {
        return surname;
    }

    public void setSurname(String surname) {
        this.surname = surname;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    @JsonProperty("numeroDocumento")
    public String getDocumentNumber() {
        return documentNumber;
    }

    public void setDocumentNumber(String documentNumber) {
        this.documentNumber = documentNumber;
    }

    @JsonProperty("tipoDocumento")
    public String getDocumentType() {
        return documentType;
    }

    public void setDocumentType(String documentType) {
        this.documentType = documentType;
    }

    @JsonProperty("tipoDocumentoDesc")
    public String getDocimentTypeDescription() {
        return docimentTypeDescription;
    }

    public void setDocimentTypeDescription(String docimentTypeDescription) {
        this.docimentTypeDescription = docimentTypeDescription;
    }

    public boolean getRCU() {
        return isRCU;
    }

    public void setRCU(boolean RCU) {
        isRCU = RCU;
    }

    public boolean getFlag() {
        return flag;
    }

    public void setFlag(boolean flag) {
        this.flag = flag;
    }

    @Override
    @JsonProperty("applydate")
    public String getApplyDate() {
        return super.getApplyDate();
    }
}
