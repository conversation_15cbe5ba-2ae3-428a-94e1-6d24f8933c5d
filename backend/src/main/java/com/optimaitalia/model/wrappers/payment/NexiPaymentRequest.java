package com.optimaitalia.model.wrappers.payment;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class NexiPaymentRequest {
    @JsonProperty("SistemaChiamante")
    private String SistemaChiamante;

    @JsonProperty("CodiceCliente")
    private Integer CodiceCliente;

    @JsonProperty("CodificaCliente")
    private String CodificaCliente;

    @JsonProperty("CF")
    private String CF;

    @JsonProperty("PIVA")
    private String PIVA;

    @JsonProperty("TipoOperazione")
    private String TipoOperazione;

    @JsonProperty("TipoPagamento")
    private String TipoPagamento;

    @JsonProperty("URL")
    private String URL;

    @JsonProperty("URL_Back")
    private String URL_Back;

    @JsonProperty("URLCallbackChiam")
    private String URLCallbackChiam;

    @JsonProperty("IdOrdine")
    private String IdOrdine;

    @JsonProperty("Importo")
    private Integer Importo;

    @JsonProperty("OneShot")
    private String OneShot;

    @JsonProperty("AddInfo1")
    private String AddInfo1;

    @JsonProperty("AddInfo2")
    private String AddInfo2;

    @JsonProperty("AddInfo3")
    private String AddInfo3;

    @JsonProperty("Ricariche")
    private List<Ricariche> Ricariche;
}
