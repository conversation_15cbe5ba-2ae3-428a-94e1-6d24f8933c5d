package com.optimaitalia.model.wrappers.user.requests;

import com.optimaitalia.model.enums.ShippingType;

public class ChangeShippingType extends Change {

    private Integer currentShippingTypeId;

    private ShippingType currentShippingType;

    private Integer newShippingTypeId;

    private ShippingType newShippingType;

    public Integer getCurrentShippingTypeId() {
        return currentShippingTypeId;
    }

    public void setCurrentShippingTypeId(Integer currentShippingTypeId) {
        this.currentShippingTypeId = currentShippingTypeId;
    }

    public ShippingType getCurrentShippingType() {
        return currentShippingType;
    }

    public void setCurrentShippingType(ShippingType currentShippingType) {
        this.currentShippingType = currentShippingType;
    }

    public Integer getNewShippingTypeId() {
        return newShippingTypeId;
    }

    public void setNewShippingTypeId(Integer newShippingTypeId) {
        this.newShippingTypeId = newShippingTypeId;
    }

    public ShippingType getNewShippingType() {
        return newShippingType;
    }

    public void setNewShippingType(ShippingType newShippingType) {
        this.newShippingType = newShippingType;
    }
}