package com.optimaitalia.model.wrappers.dilazione;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.JacksonInvoiceDateDeserializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class Dilazione {

    private String IdCRM;

    private Integer codiceCliente;

    private Date dataFine;

    private Date dataInizio;

    private List<Fatture> fatture;

    private Integer idDilazione;

    private Integer numeroRate;

    private List<Rate> rate;

    private Double scopertoDilazione;

    private Double totaleImporto;

    public String getIdCRM() {
        return IdCRM;
    }

    public void setIdCRM(String idCRM) {
        IdCRM = idCRM;
    }

    public Integer getCodiceCliente() {
        return codiceCliente;
    }

    public void setCodiceCliente(Integer codiceCliente) {
        this.codiceCliente = codiceCliente;
    }

    public Date getDataFine() {
        return dataFine;
    }

    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    public void setDataFine(Date dataFine) {
        this.dataFine = dataFine;
    }

    public Date getDataInizio() {
        return dataInizio;
    }

    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    public void setDataInizio(Date dataInizio) {
        this.dataInizio = dataInizio;
    }

    public List<Fatture> getFatture() {
        return fatture;
    }

    public void setFatture(List<Fatture> fatture) {
        this.fatture = fatture;
    }

    public Integer getIdDilazione() {
        return idDilazione;
    }

    public void setIdDilazione(Integer idDilazione) {
        this.idDilazione = idDilazione;
    }

    public Integer getNumeroRate() {
        return numeroRate;
    }

    public void setNumeroRate(Integer numeroRate) {
        this.numeroRate = numeroRate;
    }

    public List<Rate> getRate() {
        return rate;
    }

    public void setRate(List<Rate> rate) {
        this.rate = rate;
    }

    public Double getScopertoDilazione() {
        return scopertoDilazione;
    }

    public void setScopertoDilazione(double scopertoDilazione) {
        this.scopertoDilazione = scopertoDilazione;
    }

    public Double getTotaleImporto() {
        return totaleImporto;
    }

    public void setTotaleImporto(double totaleImporto) {
        this.totaleImporto = totaleImporto;
    }
}
