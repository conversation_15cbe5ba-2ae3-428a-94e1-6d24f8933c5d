package com.optimaitalia.model.wrappers.user.response;

import lombok.Data;

@Data
public class OriginalRequest {

    private Integer changeType;

    private String companyName;

    private String fiscalCode;

    private String VATNumber;

    private String phoneNumber;

    private String email;

    private String startingDate;

    private String nome;

    private String cognome;

    private String birthDate;

    private String numeroDocumento;

    private String tipoDocumento;

    private String tipoDocumentoDesc;

    private String applyDate;

    private Integer changeId;

}