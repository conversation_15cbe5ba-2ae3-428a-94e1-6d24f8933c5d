package com.optimaitalia.model.wrappers.contabile;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.BigDecimalDeserializer;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class Contabile {
    private String importoCredito;
    private String scopertoVoce;
    private String scopertoADSL;
    private String scopertoEnergia;
    private String scopertoGAS;
    private String scopertoMobile;
    private String scopertoDevice;
    private String scopertoCanoneRai;
    private Double numeroCompetenzeScoperte;
    private Long idCliente;
    private Double numeroFattureScoperte;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal totaleScoperto;
}
