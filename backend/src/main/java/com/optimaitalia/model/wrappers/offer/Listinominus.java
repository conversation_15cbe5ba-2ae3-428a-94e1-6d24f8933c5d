package com.optimaitalia.model.wrappers.offer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.optimaitalia.model.wrappers.contoRelux.Servizio;

public class Listinominus {

    private String codice;

    private Double euro;

    private Integer id;

    private Integer quant;

    private Integer ramo;

    private Servi<PERSON> servizio;

    private Integer tipo;

    @JsonProperty("codice")
    public String getCodice() {
        return codice;
    }

    @JsonProperty("Codice")
    public void setCodice(String codice) {
        this.codice = codice;
    }

    @JsonProperty("euro")
    public Double getEuro() {
        return euro;
    }

    @JsonProperty("Euro")
    public void setEuro(Double euro) {
        this.euro = euro;
    }

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("Id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("quant")
    public Integer getQuant() {
        return quant;
    }

    @JsonProperty("Quant")
    public void setQuant(Integer quant) {
        this.quant = quant;
    }

    @JsonProperty("ramo")
    public Integer getRamo() {
        return ramo;
    }

    @JsonProperty("Ramo")
    public void setRamo(Integer ramo) {
        this.ramo = ramo;
    }

    @JsonProperty("servizio")
    public Servizio getServizio() {
        return servizio;
    }

    @JsonProperty("Servizio")
    public void setServizio(Servizio servizio) {
        this.servizio = servizio;
    }

    @JsonProperty("tipo")
    public Integer getTipo() {
        return tipo;
    }

    @JsonProperty("Tipo")
    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }
}
