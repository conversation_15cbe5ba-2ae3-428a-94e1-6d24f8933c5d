package com.optimaitalia.model.wrappers.offer.request;

import com.optimaitalia.model.wrappers.offer.PromoLimitsAttributes;

import java.util.List;

public class Change {

    private Integer changeType;
    private Integer idFatt;
    private String mese1;
    private String mese2;
    private String mese3;

    private String mese1Nuovo;
    private String mese2Nuovo;
    private String mese3Nuovo;

    private String mese1DataInizio;
    private String mese2DataInizio;
    private String mese3DataInizio;

    private String mese1DataFine;
    private String mese2DataFine;
    private String mese3DataFine;

    private String codPromo;
    private String servizi;

    private String attributes;

    private List<Integer> servizi1;
    private List<Integer> servizi2;
    private List<Integer> servizi3;

    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    public Integer getIdFatt() {
        return idFatt;
    }

    public void setIdFatt(Integer idFatt) {
        this.idFatt = idFatt;
    }

    public String getMese1() {
        return mese1;
    }

    public void setMese1(String mese1) {
        this.mese1 = mese1;
    }

    public String getMese2() {
        return mese2;
    }

    public void setMese2(String mese2) {
        this.mese2 = mese2;
    }

    public String getMese3() {
        return mese3;
    }

    public void setMese3(String mese3) {
        this.mese3 = mese3;
    }

    public String getMese1Nuovo() {
        return mese1Nuovo;
    }

    public void setMese1Nuovo(String mese1Nuovo) {
        this.mese1Nuovo = mese1Nuovo;
    }

    public String getMese2Nuovo() {
        return mese2Nuovo;
    }

    public void setMese2Nuovo(String mese2Nuovo) {
        this.mese2Nuovo = mese2Nuovo;
    }

    public String getMese3Nuovo() {
        return mese3Nuovo;
    }

    public void setMese3Nuovo(String mese3Nuovo) {
        this.mese3Nuovo = mese3Nuovo;
    }

    public String getMese1DataInizio() {
        return mese1DataInizio;
    }

    public void setMese1DataInizio(String mese1DataInizio) {
        this.mese1DataInizio = mese1DataInizio;
    }

    public String getMese2DataInizio() {
        return mese2DataInizio;
    }

    public void setMese2DataInizio(String mese2DataInizio) {
        this.mese2DataInizio = mese2DataInizio;
    }

    public String getMese3DataInizio() {
        return mese3DataInizio;
    }

    public void setMese3DataInizio(String mese3DataInizio) {
        this.mese3DataInizio = mese3DataInizio;
    }

    public String getMese1DataFine() {
        return mese1DataFine;
    }

    public void setMese1DataFine(String mese1DataFine) {
        this.mese1DataFine = mese1DataFine;
    }

    public String getMese2DataFine() {
        return mese2DataFine;
    }

    public void setMese2DataFine(String mese2DataFine) {
        this.mese2DataFine = mese2DataFine;
    }

    public String getMese3DataFine() {
        return mese3DataFine;
    }

    public void setMese3DataFine(String mese3DataFine) {
        this.mese3DataFine = mese3DataFine;
    }

    public String getCodPromo() {
        return codPromo;
    }

    public void setCodPromo(String codPromo) {
        this.codPromo = codPromo;
    }

    public String getServizi() {
        return servizi;
    }

    public void setServizi(String servizi) {
        this.servizi = servizi;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }

    public List<Integer> getServizi1() {
        return servizi1;
    }

    public void setServizi1(List<Integer> servizi1) {
        this.servizi1 = servizi1;
    }

    public List<Integer> getServizi2() {
        return servizi2;
    }

    public void setServizi2(List<Integer> servizi2) {
        this.servizi2 = servizi2;
    }

    public List<Integer> getServizi3() {
        return servizi3;
    }

    public void setServizi3(List<Integer> servizi3) {
        this.servizi3 = servizi3;
    }
}
