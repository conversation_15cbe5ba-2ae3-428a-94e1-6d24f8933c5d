package com.optimaitalia.model.wrappers.questionsAndAnswers;

import com.fasterxml.jackson.annotation.JsonProperty;

public class FormAnswerMetadata {

    private String cluster;

    private String created;

    private String modulo;

    private String servizio;

    private String title;

    @JsonProperty("cluster")
    public String getCluster() {
        return cluster;
    }

    @JsonProperty("Cluster")
    public void setCluster(String cluster) {
        this.cluster = cluster;
    }

    @JsonProperty("created")
    public String getCreated() {
        return created;
    }

    @JsonProperty("Created")
    public void setCreated(String created) {
        this.created = created;
    }

    @JsonProperty("modulo")
    public String getModulo() {
        return modulo;
    }

    @JsonProperty("Modulo")
    public void setModulo(String modulo) {
        this.modulo = modulo;
    }

    @JsonProperty("servizio")
    public String getServizio() {
        return servizio;
    }

    @JsonProperty("Servizio")
    public void setServizio(String servizio) {
        this.servizio = servizio;
    }

    @JsonProperty("title")
    public String getTitle() {
        return title;
    }

    @JsonProperty("Title")
    public void setTitle(String title) {
        this.title = title;
    }
}
