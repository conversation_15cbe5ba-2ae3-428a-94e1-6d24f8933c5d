package com.optimaitalia.model.wrappers.mobile.conracts;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.model.wrappers.mobile.products.ProductActivationRecord;
import com.optimaitalia.model.wrappers.mobile.products.ProductRecord;
import com.optimaitalia.utils.dateDeserializer.ContractsDateDeserializer;

import java.util.Date;
import java.util.List;

public class ContractRecord {

    private Integer idFattura;

    private Integer idContratto;

    private Integer canaleAcquisto;

    private String descrizioneCanaleAcquisto;

    private List<ProductActivationRecord> additionalProducts;

    private Date createdOn;

    private Integer customerId;

    private Boolean dataEnabled;

    private Boolean fiveGEnabled;

    private Long id;

    private Integer languageId;

    private LastStatusChange lastStatusChange;

    private Integer lastStatusChangeId;

    private ProductRecord mainProduct;

    private Integer mainProductId;

    private String mainProductRenewalPolicy;

    private Integer mainProductRenewalPolicyId;

    private Integer mappingId;

    private Boolean mmsEnabled;

    private Msisdn msisdn;

    private Long msisdnId;

    private Integer ownerId;

    private Boolean premiumBarringEnabled;

    private Boolean roamingDataEnabled;

    private Boolean roamingEnabled;

    private String roamingLimit;

    private Boolean roamingLimitEnabled;

    private Sim sim;

    private String simId;

    private Boolean smsIncomingEnabled;

    private Boolean smsOutgoingEnabled;

    private Boolean videoEnabled;

    private Boolean voiceIncomingEnabled;

    private Boolean voiceInternationalEnabled;

    private Boolean voiceOutgoingEnabled;

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    private Date date;


    public Integer getIdFattura() {
        return idFattura;
    }

    public void setIdFattura(Integer idFattura) {
        this.idFattura = idFattura;
    }

    public Integer getIdContratto() {
        return idContratto;
    }

    public void setIdContratto(Integer idContratto) {
        this.idContratto = idContratto;
    }

    public Integer getCanaleAcquisto() {
        return canaleAcquisto;
    }

    public void setCanaleAcquisto(Integer canaleAcquisto) {
        this.canaleAcquisto = canaleAcquisto;
    }

    public String getDescrizioneCanaleAcquisto() {
        return descrizioneCanaleAcquisto;
    }

    public void setDescrizioneCanaleAcquisto(String descrizioneCanaleAcquisto) {
        this.descrizioneCanaleAcquisto = descrizioneCanaleAcquisto;
    }

    public Date getCreatedOn() {
        return createdOn;
    }

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public void setCreatedOn(Date createdOn) {
        this.createdOn = createdOn;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public Boolean getDataEnabled() {
        return dataEnabled;
    }

    public void setDataEnabled(Boolean dataEnabled) {
        this.dataEnabled = dataEnabled;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public Integer getLastStatusChangeId() {
        return lastStatusChangeId;
    }

    public void setLastStatusChangeId(Integer lastStatusChangeId) {
        this.lastStatusChangeId = lastStatusChangeId;
    }

    public Integer getMainProductId() {
        return mainProductId;
    }

    public void setMainProductId(Integer mainProductId) {
        this.mainProductId = mainProductId;
    }

    public String getMainProductRenewalPolicy() {
        return mainProductRenewalPolicy;
    }

    public void setMainProductRenewalPolicy(String mainProductRenewalPolicy) {
        this.mainProductRenewalPolicy = mainProductRenewalPolicy;
    }

    public Integer getMainProductRenewalPolicyId() {
        return mainProductRenewalPolicyId;
    }

    public void setMainProductRenewalPolicyId(Integer mainProductRenewalPolicyId) {
        this.mainProductRenewalPolicyId = mainProductRenewalPolicyId;
    }

    public Integer getMappingId() {
        return mappingId;
    }

    public void setMappingId(Integer mappingId) {
        this.mappingId = mappingId;
    }

    public Boolean getMmsEnabled() {
        return mmsEnabled;
    }

    public void setMmsEnabled(Boolean mmsEnabled) {
        this.mmsEnabled = mmsEnabled;
    }

    public Msisdn getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(Msisdn msisdn) {
        this.msisdn = msisdn;
    }

    public Long getMsisdnId() {
        return msisdnId;
    }

    public void setMsisdnId(Long msisdnId) {
        this.msisdnId = msisdnId;
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public Boolean getPremiumBarringEnabled() {
        return premiumBarringEnabled;
    }

    public void setPremiumBarringEnabled(Boolean premiumBarringEnabled) {
        this.premiumBarringEnabled = premiumBarringEnabled;
    }

    public Boolean getRoamingDataEnabled() {
        return roamingDataEnabled;
    }

    public void setRoamingDataEnabled(Boolean roamingDataEnabled) {
        this.roamingDataEnabled = roamingDataEnabled;
    }

    public Boolean getRoamingEnabled() {
        return roamingEnabled;
    }

    public void setRoamingEnabled(Boolean roamingEnabled) {
        this.roamingEnabled = roamingEnabled;
    }

    public String getRoamingLimit() {
        return roamingLimit;
    }

    public void setRoamingLimit(String roamingLimit) {
        this.roamingLimit = roamingLimit;
    }

    public Boolean getRoamingLimitEnabled() {
        return roamingLimitEnabled;
    }

    public void setRoamingLimitEnabled(Boolean roamingLimitEnabled) {
        this.roamingLimitEnabled = roamingLimitEnabled;
    }

    public Sim getSim() {
        return sim;
    }

    public void setSim(Sim sim) {
        this.sim = sim;
    }

    public String getSimId() {
        return simId;
    }

    public void setSimId(String simId) {
        this.simId = simId;
    }

    public Boolean getSmsIncomingEnabled() {
        return smsIncomingEnabled;
    }

    public void setSmsIncomingEnabled(Boolean smsIncomingEnabled) {
        this.smsIncomingEnabled = smsIncomingEnabled;
    }

    public Boolean getSmsOutgoingEnabled() {
        return smsOutgoingEnabled;
    }

    public void setSmsOutgoingEnabled(Boolean smsOutgoingEnabled) {
        this.smsOutgoingEnabled = smsOutgoingEnabled;
    }

    public Boolean getVideoEnabled() {
        return videoEnabled;
    }

    public void setVideoEnabled(Boolean videoEnabled) {
        this.videoEnabled = videoEnabled;
    }

    public Boolean getVoiceIncomingEnabled() {
        return voiceIncomingEnabled;
    }

    public void setVoiceIncomingEnabled(Boolean voiceIncomingEnabled) {
        this.voiceIncomingEnabled = voiceIncomingEnabled;
    }

    public Boolean getVoiceInternationalEnabled() {
        return voiceInternationalEnabled;
    }

    public void setVoiceInternationalEnabled(Boolean voiceInternationalEnabled) {
        this.voiceInternationalEnabled = voiceInternationalEnabled;
    }

    public Boolean getVoiceOutgoingEnabled() {
        return voiceOutgoingEnabled;
    }

    public void setVoiceOutgoingEnabled(Boolean voiceOutgoingEnabled) {
        this.voiceOutgoingEnabled = voiceOutgoingEnabled;
    }

    @JsonProperty("fiveGEnabled")
    public Boolean getFiveGEnabled() {
        return fiveGEnabled;
    }
    
    @JsonProperty("5gEnabled")
    public void setFiveGEnabled(Boolean fiveGEnabled) {
        this.fiveGEnabled = fiveGEnabled;
    }
    
    public List<ProductActivationRecord> getAdditionalProducts() {
        return additionalProducts;
    }

    public void setAdditionalProducts(List<ProductActivationRecord> additionalProducts) {
        this.additionalProducts = additionalProducts;
    }

    public ProductRecord getMainProduct() {
        return mainProduct;
    }

    public void setMainProduct(ProductRecord mainProduct) {
        this.mainProduct = mainProduct;
    }

    public LastStatusChange getLastStatusChange() {
        return lastStatusChange;
    }

    public void setLastStatusChange(LastStatusChange lastStatusChange) {
        this.lastStatusChange = lastStatusChange;
    }
}
