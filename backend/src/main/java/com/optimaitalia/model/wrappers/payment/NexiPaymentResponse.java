package com.optimaitalia.model.wrappers.payment;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class NexiPaymentResponse {
    private Integer idResponse;
    private String esito;
    private String descrizione;
    private String urL_Form;
    private String alias;
    private String importo;
    private String divisa;
    private String codtrans;
    private String url;
    private String url_back;
    private String mac;
    private String num_contratto;
    private String tipo_servizio;
    private String tipo_richiesta;
    private String urlpost;
    private String html;
}
