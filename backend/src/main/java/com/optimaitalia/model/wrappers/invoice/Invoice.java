package com.optimaitalia.model.wrappers.invoice;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.JacksonInvoiceDateDeserializer;

import java.math.BigDecimal;
import java.util.Date;

public class Invoice {

    private Long id;

    private Integer billingYear;

    private Date startDate;

    private Date endDate;

    private Long idCliente;

    private Integer idFatt;

    private Float rate;

    private Float creditNote;

    private String paymentMode;

    private String fileName;

    private String numeroFattura;

    private Boolean oscurata;

    private String spUriTrafficoVoce;

    private BigDecimal opened;

    private String invoiceSeries;

    private String shipment;

    private String status;

    private String reversalInvoicing;

    private BigDecimal total;

    private BigDecimal totalEvasion;

    private String downloadUrl;

    private Boolean validataSdi;

    private Boolean isSold;

    private BigDecimal amountTransferred;

    @JsonProperty("id")
    public Long getId() {
        return id;
    }

    @JsonProperty("IdInvoice")
    public void setId(Long id) {
        this.id = id;
    }

    @JsonProperty("billingYear")
    public Integer getBillingYear() {
        return billingYear;
    }

    @JsonProperty("AnnoFatturazione")
    public void setBillingYear(Integer billingYear) {
        this.billingYear = billingYear;
    }

    @JsonProperty("Ceduta")
    public void setIsSold(Boolean isSold) {
        this.isSold = isSold;
    }

    @JsonProperty("isSold")
    public Boolean getIsSold() {
        return isSold;
    }

    @JsonProperty("amountTransferred")
    public BigDecimal getAmountTransferred() {
        return amountTransferred;
    }

    @JsonProperty("ImportoCeduto")
    public void setAmountTransferred(BigDecimal amountTransferred) {
        this.amountTransferred = amountTransferred;
    }

    @JsonProperty("startDate")
    public Date getStartDate() {
        return startDate;
    }

    @JsonProperty("DataFattura")
    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @JsonProperty("endDate")
    public Date getEndDate() {
        return endDate;
    }

    @JsonProperty("DataScadenza")
    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @JsonProperty("idCliente")
    public Long getIdCliente() {
        return idCliente;
    }

    @JsonProperty("IdCliente")
    public void setIdCliente(Long idCliente) {
        this.idCliente = idCliente;
    }

    @JsonProperty("idFatt")
    public Integer getIdFatt() {
        return idFatt;
    }

    @JsonProperty("IdFatt")
    public void setIdFatt(Integer idFatt) {
        this.idFatt = idFatt;
    }

    @JsonProperty("rate")
    public Float getRate() {
        return rate;
    }

    @JsonProperty("Imponibile")
    public void setRate(Float rate) {
        this.rate = rate;
    }

    @JsonProperty("creditNote")
    public Float getCreditNote() {
        return creditNote;
    }

    @JsonProperty("IsNotaCredito")
    public void setCreditNote(Float creditNote) {
        this.creditNote = creditNote;
    }

    @JsonProperty("paymentMode")
    public String getPaymentMode() {
        return paymentMode;
    }

    @JsonProperty("ModalitaPagamento")
    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    @JsonProperty("nomeFile")
    public String getFileName() {
        return fileName;
    }

    @JsonProperty("NomeFile")
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @JsonProperty("numeroFattura")
    public String getNumeroFattura() {
        return numeroFattura;
    }

    @JsonProperty("NumeroFattura")
    public void setNumeroFattura(String numeroFattura) {
        this.numeroFattura = numeroFattura;
    }

    @JsonProperty("oscurata")
    public Boolean getOscurata() {
        return oscurata;
    }

    @JsonProperty("Oscurata")
    public void setOscurata(Boolean oscurata) {
        this.oscurata = oscurata;
    }

    @JsonProperty("spUriTrafficoVoce")
    public String getSpUriTrafficoVoce() {
        return spUriTrafficoVoce;
    }

    @JsonProperty("SpUriTrafficoVoce")
    public void setSpUriTrafficoVoce(String spUriTrafficoVoce) {
        this.spUriTrafficoVoce = spUriTrafficoVoce;
    }

    @JsonProperty("opened")
    public BigDecimal getOpened() {
        return opened;
    }

    @JsonProperty("Scoperto")
    public void setOpened(BigDecimal opened) {
        this.opened = opened;
    }

    @JsonProperty("invoiceSeries")
    public String getInvoiceSeries() {
        return invoiceSeries;
    }

    @JsonProperty("SerieFattura")
    public void setInvoiceSeries(String invoiceSeries) {
        this.invoiceSeries = invoiceSeries;
    }

    @JsonProperty("shipment")
    public String getShipment() {
        return shipment;
    }

    @JsonProperty("Spedizione")
    public void setShipment(String shipment) {
        this.shipment = shipment;
    }

    @JsonProperty("status")
    public String getStatus() {
        return status;
    }

    @JsonProperty("Stato")
    public void setStatus(String status) {
        this.status = status;
    }

    @JsonProperty("reversalInvoicing")
    public String getReversalInvoicing() {
        return reversalInvoicing;
    }

    @JsonProperty("StornoRifatturazione")
    public void setReversalInvoicing(String reversalInvoicing) {
        this.reversalInvoicing = reversalInvoicing;
    }

    @JsonProperty("total")
    public BigDecimal getTotal() {
        return total;
    }

    @JsonProperty("Totale")
    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    @JsonProperty("totalEvasion")
    public BigDecimal getTotalEvasion() {
        return totalEvasion;
    }

    @JsonProperty("TotaleEvaso")
    public void setTotalEvasion(BigDecimal totalEvasion) {
        this.totalEvasion = totalEvasion;
    }

    @JsonProperty("downloadUrl")
    public String getDownloadUrl() {
        return downloadUrl;
    }

    @JsonProperty("UriSharePoint")
    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }


    @JsonProperty("validataSdi")
    public Boolean getValidataSdi() {
        return validataSdi;
    }
    @JsonProperty("ValidataSdi")
    public void setValidataSdi(Boolean validataSdi) {
        this.validataSdi = validataSdi;
    }
}

