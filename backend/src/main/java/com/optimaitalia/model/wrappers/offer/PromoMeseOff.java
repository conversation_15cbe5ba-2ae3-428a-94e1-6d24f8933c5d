package com.optimaitalia.model.wrappers.offer;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.util.ArrayList;

public class PromoMeseOff {
    private String codPromo;
    private String descrizionePromo;
    private PromoLimitsAttributes promoAttributes;
    private String dataInizioValidita;
    private String dataFineValidita;
    private ArrayList<Integer> servizi;

    public String getCodPromo() {
        return codPromo;
    }

    public void setCodPromo(String codPromo) {
        this.codPromo = codPromo;
    }

    public String getDescrizionePromo() {
        return descrizionePromo;
    }

    public void setDescrizionePromo(String descrizionePromo) {
        this.descrizionePromo = descrizionePromo;
    }

    @JsonProperty("promoAttributes")
    public PromoLimitsAttributes getPromoAttributes() {
        return promoAttributes;
    }

    @JsonProperty("attributes")
    public void setPromoAttributes(PromoLimitsAttributes promoAttributes) {
        this.promoAttributes = promoAttributes;
    }

    public String getDataInizioValidita() {
        return dataInizioValidita;
    }

    public void setDataInizioValidita(String dataInizioValidita) {
        this.dataInizioValidita = dataInizioValidita;
    }

    public String getDataFineValidita() {
        return dataFineValidita;
    }

    public void setDataFineValidita(String dataFineValidita) {
        this.dataFineValidita = dataFineValidita;
    }

    public ArrayList<Integer> getServizi() {
        return servizi;
    }

    public void setServizi(ArrayList<Integer> servizi) {
        this.servizi = servizi;
    }
}
