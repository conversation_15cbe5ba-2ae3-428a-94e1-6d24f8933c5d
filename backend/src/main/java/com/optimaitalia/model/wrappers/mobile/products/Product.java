package com.optimaitalia.model.wrappers.mobile.products;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Product {

    private Integer type;

    private Integer productId;

    private String productName;

    private String productDescription;

    private String activationCost;

    private String renewalCost;

    @JsonProperty("type")
    public Integer getType() {
        return type;
    }

    @JsonProperty("tipo")
    public void setType(Integer type) {
        this.type = type;
    }

    @JsonProperty("productId")
    public Integer getProductId() {
        return productId;
    }

    @JsonProperty("idProdotto")
    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @JsonProperty("productName")
    public String getProductName() {
        return productName;
    }

    @JsonProperty("nomeProdotto")
    public void setProductName(String productName) {
        this.productName = productName;
    }

    @JsonProperty("productDescription")
    public String getProductDescription() {
        return productDescription;
    }

    @JsonProperty("descrizioneProdotto")
    public void setProductDescription(String productDescription) {
        this.productDescription = productDescription;
    }

    @JsonProperty("activationCost")
    public String getActivationCost() {
        return activationCost;
    }

    @JsonProperty("costoAttivazione")
    public void setActivationCost(String activationCost) {
        this.activationCost = activationCost;
    }

    @JsonProperty("renewalCost")
    public String getRenewalCost() {
        return renewalCost;
    }

    @JsonProperty("costoRinnovo")
    public void setRenewalCost(String renewalCost) {
        this.renewalCost = renewalCost;
    }
}
