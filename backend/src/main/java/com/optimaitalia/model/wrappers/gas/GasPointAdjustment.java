package com.optimaitalia.model.wrappers.gas;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.BillingDateDeserializer;
import com.optimaitalia.utils.dateDeserializer.GasPointAdjustmentDateDeserializer;

import java.math.BigDecimal;
import java.util.Date;

public class GasPointAdjustment {

    private String coefficienteAdesione;

    private Integer consumo;

    private BigDecimal consumoAdeguato;

    private Date dataFattura;

    private Date dataLettura;

    private String elaborato;

    private String fatturaEmissione;

    private String finePeriodo;

    private String idCliente;

    private Date inizioPeriodo;

    private String letturaApertura;

    private String letturaChiusura;

    private String letturaEmissione;

    private String letturaErrata;

    private String letturaFatturata;

    private String letturaSwitch;

    private String origineDati;

    private String pdr;

    private Integer valoreLettura;

    @JsonProperty("coefficienteAdesione")
    public String getCoefficienteAdesione() {
        return coefficienteAdesione;
    }

    @JsonProperty("CoefficienteAdesione")
    public void setCoefficienteAdesione(String coefficienteAdesione) {
        this.coefficienteAdesione = coefficienteAdesione;
    }

    @JsonProperty("consumo")
    public Integer getConsumo() {
        return consumo;
    }

    @JsonProperty("Consumo")
    public void setConsumo(Integer consumo) {
        this.consumo = consumo;
    }

    @JsonProperty("consumoAdeguato")
    public BigDecimal getConsumoAdeguato() {
        return consumoAdeguato;
    }

    @JsonProperty("ConsumoAdeguato")
    public void setConsumoAdeguato(BigDecimal consumoAdeguato) {
        this.consumoAdeguato = consumoAdeguato;
    }

    @JsonProperty("dataFattura")
    public Date getDataFattura() {
        return dataFattura;
    }

    @JsonProperty("DataFattura")
    @JsonDeserialize(using = BillingDateDeserializer.class)
    public void setDataFattura(Date dataFattura) {
        this.dataFattura = dataFattura;
    }

    @JsonProperty("dataLettura")
    public Date getDataLettura() {
        return dataLettura;
    }

    @JsonProperty("DataLettura")
    @JsonDeserialize(using = GasPointAdjustmentDateDeserializer.class)
    public void setDataLettura(Date dataLettura) {
        this.dataLettura = dataLettura;
    }

    @JsonProperty("elaborato")
    public String getElaborato() {
        return elaborato;
    }

    @JsonProperty("Elaborato")
    public void setElaborato(String elaborato) {
        this.elaborato = elaborato;
    }

    @JsonProperty("fatturaEmissione")
    public String getFatturaEmissione() {
        return fatturaEmissione;
    }

    @JsonProperty("FatturaEmissione")
    public void setFatturaEmissione(String fatturaEmissione) {
        this.fatturaEmissione = fatturaEmissione;
    }

    @JsonProperty("finePeriodo")
    public String getFinePeriodo() {
        return finePeriodo;
    }

    @JsonProperty("FinePeriodo")
    public void setFinePeriodo(String finePeriodo) {
        this.finePeriodo = finePeriodo;
    }

    @JsonProperty("idCliente")
    public String getIdCliente() {
        return idCliente;
    }

    @JsonProperty("IdCliente")
    public void setIdCliente(String idCliente) {
        this.idCliente = idCliente;
    }

    @JsonProperty("inizioPeriodo")
    public Date getInizioPeriodo() {
        return inizioPeriodo;
    }

    @JsonProperty("InizioPeriodo")
    @JsonDeserialize(using = GasPointAdjustmentDateDeserializer.class)
    public void setInizioPeriodo(Date inizioPeriodo) {
        this.inizioPeriodo = inizioPeriodo;
    }

    @JsonProperty("letturaApertura")
    public String getLetturaApertura() {
        return letturaApertura;
    }

    @JsonProperty("LetturaApertura")
    public void setLetturaApertura(String letturaApertura) {
        this.letturaApertura = letturaApertura;
    }

    @JsonProperty("letturaChiusura")
    public String getLetturaChiusura() {
        return letturaChiusura;
    }

    @JsonProperty("LetturaChiusura")
    public void setLetturaChiusura(String letturaChiusura) {
        this.letturaChiusura = letturaChiusura;
    }

    @JsonProperty("letturaEmissione")
    public String getLetturaEmissione() {
        return letturaEmissione;
    }

    @JsonProperty("LetturaEmissione")
    public void setLetturaEmissione(String letturaEmissione) {
        this.letturaEmissione = letturaEmissione;
    }

    @JsonProperty("letturaErrata")
    public String getLetturaErrata() {
        return letturaErrata;
    }

    @JsonProperty("LetturaErrata")
    public void setLetturaErrata(String letturaErrata) {
        this.letturaErrata = letturaErrata;
    }

    @JsonProperty("letturaFatturata")
    public String getLetturaFatturata() {
        return letturaFatturata;
    }

    @JsonProperty("LetturaFatturata")
    public void setLetturaFatturata(String letturaFatturata) {
        this.letturaFatturata = letturaFatturata;
    }

    @JsonProperty("letturaSwitch")
    public String getLetturaSwitch() {
        return letturaSwitch;
    }

    @JsonProperty("LetturaSwitch")
    public void setLetturaSwitch(String letturaSwitch) {
        this.letturaSwitch = letturaSwitch;
    }

    @JsonProperty("origineDati")
    public String getOrigineDati() {
        return origineDati;
    }

    @JsonProperty("OrigineDati")
    public void setOrigineDati(String origineDati) {
        this.origineDati = origineDati;
    }

    @JsonProperty("pdr")
    public String getPdr() {
        return pdr;
    }

    @JsonProperty("Pdr")
    public void setPdr(String pdr) {
        this.pdr = pdr;
    }

    @JsonProperty("valoreLettura")
    public Integer getValoreLettura() {
        return valoreLettura;
    }

    @JsonProperty("ValoreLettura")
    public void setValoreLettura(Integer valoreLettura) {
        this.valoreLettura = valoreLettura;
    }
}
