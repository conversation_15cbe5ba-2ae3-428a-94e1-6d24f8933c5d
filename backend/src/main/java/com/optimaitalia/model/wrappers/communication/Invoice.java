package com.optimaitalia.model.wrappers.communication;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.CommunicationDateDeserializer;

import java.util.Date;

public class Invoice {

    private Long invoiceNumber;

    private Date issueDate;

    private Date expiryDate;

    private Double total;

    @JsonProperty("invoiceNumber")
    public Long getInvoiceNumber() {
        return invoiceNumber;
    }

    @JsonProperty("NumeroFatt")
    public void setInvoiceNumber(Long invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    @JsonProperty("issueDate")
    public Date getIssueDate() {
        return issueDate;
    }

    @JsonDeserialize(using = CommunicationDateDeserializer.class)
    @JsonProperty("DataEmissione")
    public void setIssueDate(Date issueDate) {
        this.issueDate = issueDate;
    }

    @JsonProperty("expiryDate")
    public Date getExpiryDate() {
        return expiryDate;
    }

    @JsonDeserialize(using = CommunicationDateDeserializer.class)
    @JsonProperty("DataScadenza")
    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    @JsonProperty("total")
    public Double getTotal() {
        return total;
    }

    @JsonProperty("Totale")
    public void setTotal(Double total) {
        this.total = total;
    }
}
