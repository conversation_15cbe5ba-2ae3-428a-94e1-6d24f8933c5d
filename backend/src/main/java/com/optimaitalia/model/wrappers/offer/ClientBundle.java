package com.optimaitalia.model.wrappers.offer;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class ClientBundle {

    private String cliente;

    private List<ClientBundleDetails> clientBundleDetails;

    private String finval;

    @JsonProperty("cliente")
    public String getCliente() {
        return cliente;
    }

    @JsonProperty("Cliente")
    public void setCliente(String cliente) {
        this.cliente = cliente;
    }

    @JsonProperty("clientBundleDetails")
    public List<ClientBundleDetails> getClientBundleDetails() {
        return clientBundleDetails;
    }

    @JsonProperty("ClientiBundleDett")
    public void setClientBundleDetails(List<ClientBundleDetails> clientBundleDetails) {
        this.clientBundleDetails = clientBundleDetails;
    }

    @JsonProperty("finval")
    public String getFinval() {
        return finval;
    }

    @JsonProperty("Finval")
    public void setFinval(String finval) {
        this.finval = finval;
    }
}
