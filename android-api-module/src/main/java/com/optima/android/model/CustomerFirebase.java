package com.optima.android.model;

import net.sf.oval.constraint.MinLength;
import net.sf.oval.constraint.NotEmpty;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "customer_firebase")
public class CustomerFirebase implements Serializable {

    @Column(name = "customer_id")
    @NotNull
    @NotEmpty
    @MinLength(4)
    private String customerId;

    @Id
    @Column(name = "firebase_id")
    @NotNull
    @NotEmpty
    @MinLength(4)
    private String firebaseId;

    @Column
    @Temporal(TemporalType.TIMESTAMP)
    private Date date;

    @Column(name = "app_version")
    private String appVersion;

    @Column(name = "app_so")
    private String appSO;

    @Column(name = "deleted")
    private Boolean deleted;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getFirebaseId() {
        return firebaseId;
    }

    public void setFirebaseId(String firebaseId) {
        this.firebaseId = firebaseId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getAppSO() {
        return appSO;
    }

    public void setAppSO(String appSO) {
        this.appSO = appSO;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}
