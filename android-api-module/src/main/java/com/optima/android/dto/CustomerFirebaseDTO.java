package com.optima.android.dto;

import com.optima.android.model.CustomerFirebase;
import lombok.Data;

import java.util.Date;

@Data
public class CustomerFirebaseDTO {

    private String firebaseId;

    private String customerId;

    private Date date;

    public CustomerFirebaseDTO(CustomerFirebase customerFirebase) {
        this.firebaseId = customerFirebase.getFirebaseId();
        this.customerId = customerFirebase.getCustomerId();
        this.date = customerFirebase.getDate();
    }
}
