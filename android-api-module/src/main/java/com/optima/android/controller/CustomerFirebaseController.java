package com.optima.android.controller;

import com.optima.android.dto.CustomerFirebaseDTO;
import com.optima.android.model.CustomerFirebase;
import com.optima.android.service.CustomerFirebaseService;
import com.optima.common.exceptions.ValidateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/android")
public class CustomerFirebaseController {

    private final CustomerFirebaseService customerFirebaseService;

    @Autowired
    public CustomerFirebaseController(CustomerFirebaseService customerFirebaseService) {
        this.customerFirebaseService = customerFirebaseService;
    }

    @PostMapping("/register/firebase")
    public CustomerFirebase addCustomerFirebase(@RequestBody CustomerFirebase customerFirebase) throws ValidateException {
        return customerFirebaseService.addCustomerFirebase(customerFirebase);
    }


    @GetMapping("/last/firebase")
    public CustomerFirebase getLastCustomerFirebase(@RequestParam String customerId) {
        return customerFirebaseService.getLastCustomerFirebaseByCustomerId(customerId);
    }

    @GetMapping("/customer")
    public CustomerFirebaseDTO customerFirebase(@RequestParam String firebaseId) {
        return customerFirebaseService.findNotDeletedByFirebaseId(firebaseId);
    }


    @GetMapping("/customer/logout")
    public CustomerFirebaseDTO logoutFirebase(@RequestParam String firebaseId) {
        CustomerFirebaseDTO customer =  customerFirebaseService.findNotDeletedByFirebaseId(firebaseId);
        if(customer!=null && customer.getCustomerId() != null){
            customerFirebaseService.logoutFirebase(firebaseId);
        }
        return new CustomerFirebaseDTO(new CustomerFirebase());
    }

}
