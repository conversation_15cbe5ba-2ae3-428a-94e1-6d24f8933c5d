{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "project": {"name": "ng2angle"}, "apps": [{"root": "src", "outDir": "./target/static", "assets": ["assets", "favicon1.ico"], "index": "index.html", "main": "main.ts", "polyfills": "polyfills.ts", "test": "test.ts", "tsconfig": "tsconfig.app.json", "testTsconfig": "tsconfig.spec.json", "prefix": "app", "styles": ["../node_modules/ngx-bootstrap/datepicker/bs-datepicker.css", "../node_modules/bootstrap/dist/css/bootstrap.min.css", "bs-datepicker.css", "styles.scss"], "scripts": ["../node_modules/chart.js/dist/Chart.js", "../node_modules/chart.js/dist/Chart.bundle.js", "../node_modules/image-map-resizer/js/imageMapResizer.js", "../node_modules/bootstrap/js/modal.js", "../node_modules/bootstrap/js/dropdown.js", "../node_modules/bootstrap/js/tooltip.js", "../node_modules/moment/min/moment-with-locales.min.js", "../node_modules/fullcalendar/dist/fullcalendar.js", "../node_modules/hammerjs/hammer.min.js"], "environmentSource": "environments/environment.ts", "environments": {"dev": "environments/environment.ts", "prod": "environments/environment.prod.ts"}}], "e2e": {"protractor": {"config": "./protractor.conf.js"}}, "lint": [{"project": "src/tsconfig.app.json", "exclude": "**/node_modules/**"}, {"project": "src/tsconfig.spec.json", "exclude": "**/node_modules/**"}, {"project": "e2e/tsconfig.e2e.json", "exclude": "**/node_modules/**"}], "test": {"karma": {"config": "./karma.conf.js"}}, "defaults": {"serve": {"host": "0.0.0.0"}, "styleExt": "scss", "prefixInterfaces": false, "inline": {"style": false, "template": false}, "spec": {"class": false, "component": true, "directive": true, "module": false, "pipe": true, "service": true}}}