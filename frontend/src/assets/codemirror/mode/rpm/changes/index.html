<!doctype html>

<title>CodeMirror: R<PERSON> changes mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../../lib/codemirror.css">
<script src="../../../lib/codemirror.js"></script>
<script src="changes.js"></script>
<link rel="stylesheet" href="../../../doc/docs.css">
<style type="text/css">.CodeMirror {
  border-top: 1px solid black;
  border-bottom: 1px solid black;
}</style>

<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../../doc/logo.png"></a>

  <ul>
    <li><a href="../../../index.html">Home</a>
    <li><a href="../../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../../index.html">Language modes</a>
    <li><a class=active href="javascript: void(0)">RPM changes</a>
  </ul>
</div>

<article>
  <h2>RPM changes mode</h2>

  <div><textarea id="code" name="code">
</textarea></div>
  <script>
    var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
      mode: {name: "changes"},
      lineNumbers: true,
      indentUnit: 4
    });
  </script>

  <p><strong>MIME types defined:</strong> <code>text/x-rpm-changes</code>.</p>
</article>
