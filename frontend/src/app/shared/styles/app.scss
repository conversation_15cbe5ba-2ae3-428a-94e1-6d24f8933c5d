// Remove Select default arrow on IE
select::-ms-expand {
    display: none;
}

/*!
 *
 * Angle - Bootstrap Admin App + Angular Material
 *
 * Version: 3.8.6
 * Author: @themicon_co
 * Website: http://themicon.co
 * License: https://wrapbootstrap.com/help/licenses
 *
 */

// Override bootstrap variables
@import "app/variables";

// Bootstrap
@import "bootstrap/bootstrap/_mixins";

// Global definition of media queries
@import "app/media-queries";
// Utilities
@import "app/utils-definitions";

// Bootstrap reset
@import "app/bootstrap-reset";

// Layout
@import "app/layout";
@import "app/layout-extra";
@import "app/layout-animation";
@import "app/top-navbar";
@import "app/sidebar";
@import "app/offsidebar";

// Components
@import "app/button-extra";
@import "app/panels";
@import "app/user-block";
@import "app/circles";
@import "app/dropdown-extra";
@import "app/masonry-grid";
@import "app/widget";
@import "app/table-grid";
@import "app/file-upload";

// Tables
@import "app/table-extras";

// Plugins
@import "app/datepicker";
@import "app/calendar";

// Plans
@import "app/plans";

// Utilities
@import "app/utils";

// Print CSS
@import "app/print";

@import "app/material/colors";
@import "app/material/welcome";
@import "app/material/cards";
@import "app/material/md-forms";
@import "app/material/md-list";
@import "app/material/md-inputs";
@import "app/material/ngmaterial";

//self care common css file
@import "app.common";
@import "app.icons";
