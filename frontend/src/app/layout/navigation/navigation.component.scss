@import "~app/shared/styles/colors";

.main-content {
  width: 80%;
  margin-top: 10%;
}

.no-communication-message {
  padding: 15px;
  text-align: center;
  min-height: 150px;
  font-weight: bold;
  color: #36749C;
}

.item-comunicazione {
  color: #36749C;
  list-style-type: disc;
  border: none;
  text-align: justify;
  font-size: 16px;
  padding-right: 5px;
  font-weight: normal;
  white-space: pre-wrap;
}
.item-title-comunicazione {
  list-style-type: none;
  border: none;
  text-align: justify;
  font-size: 16px;
  padding-right: 5px;
  font-weight: normal;
  white-space: pre-wrap;
}

app-msg-notification {
  display: none;
}

.mobileIcon {
  float: left;
  font-size: 25px;
  margin-right: 10px;
}

.exclamation {
  color: #e2513b;
}

.tickmob {
  border-radius: 50%;
  background: #4CAF50;
  color: white;
  width: auto;
  padding: 4px;
}

.display {
  display: block;
  transition: background-color 0.25s, color 0.15s;
}

.userInfo {
  .fa-exclamation-triangle {
    font-size: 25px;
  }

  .fa-check {
    font-size: 25px;
  }

  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: stretch;
  flex-wrap: nowrap;

  text-align: center;
  width: 18%;
  position: absolute;
  right: 7%;
  top: 1%;
  margin: 0.5%;
  height: 80%;
  border-left: 1px solid rgba(54, 116, 157, 0.18);

  .nameBlock {
    width: 70%;
    float: left;
    position: relative;
    height: 100%;
    cursor: pointer;
    color: #36749C;
    font-size: 18px;

    p {
      margin-bottom: 0;
      text-transform: uppercase;
      width: 85%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      float: left;
    }

  }

  .block {
    width: 10%;
    float: left;
    position: relative;
    height: 100%;

    .tick {
      border-radius: 50%;
      left: 80%;
      background: #4CAF50;
      color: white;
      width: auto;
      padding: 2px 4px;
    }

    .triangle {
      left: 70%;
    }

    p {
      margin: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

    }
  }

  .msgNumber {
    position: absolute;
    right: -17px;
    top: -5px;
    background: #00b9e2;
    height: 20px;
    line-height: 20px;
    width: 20px;
    border-radius: 50%;
    color: white;
    font-size: 0.8em;
    font-weight: 600;
  }

}

.userInfo p {
  margin-bottom: 0;
  text-transform: uppercase;
  width: 85%;
  float: left;
}

.linkDecoration {
  text-decoration: underline;
}

.navbar-header {
  background: none;
}

.wrapper > footer {
  margin-left: 0 !important;
  border: none;
  width: 100%;
}

.content-wrapper {
  margin: 1% auto;
  box-shadow: none;
  border: none;
  background: white;
  min-height: 84vh;
}

.navbar .topnavbar {
  position: fixed;
  top: 0;
  height: 10%;
  margin-left: 15%;
}

.menu {
  float: right;
  margin: 0 10px;
  display: none;
  color: #e2513b;
  font-size: 20px;
}

.topnavbar {
  // z-index: 2050; // it breaks down another themes blocks
}

.topnav a {
  float: left;
  display: block;
  color: #8b8b8b;
  text-align: center;
  padding: 25px 0px 11px 16px;
  text-decoration: none;
  font-size: 18px;
  border-bottom: 3px solid transparent;
  text-transform: uppercase;
}

.topnav {
  top: 0;
  overflow: hidden;
  //float: left;
  //margin-left: 0;
  padding-left: 0;
  //position: initial;
  margin-left: 16%;
  //position: absolute;
  min-width: 50%;
  line-height: 60px;
}

.topnav a span {
  padding: 5px;

}

.topnav a:hover span {
  border-bottom: 3px solid white;
  color: white;
  background: #00b9e2;
}

//li.active a span {
//  border-bottom: 3px solid white;
//  color: white;
//  background: #e2513b;
//  text-transform: uppercase;
//}
.sidebar-subnav li a {
  color: #36749d;
}

li.active a span {;
  color: white;
  background: #00b9e2;
  text-transform: uppercase;
}

li.activeM span {;
  color: white;
  background: #00b9e2;
  text-transform: uppercase;
  border-bottom: 1px solid white;
}

.activeSubMenu a {
  color: #00b9e2 !important;
}

.col-md-8 {
  width: 70%;
  float: left;
}

.col-md-4 {
  width: 30%;
  float: left;
}

.topnav .sidebar-subnav > li > a {
  color: #e2513b !important;

}

/*::ng-deep .cdk-overlay-container {
  // margin-top: 100px;
}

::ng-deep .mat-menu-panel {
  width: 300px;
  background-color: #ededed;
}*/

.user {
  height: 100px;
  //width: 300px;
  text-transform: uppercase;
  font-size: 15px;
  text-align: center;
  right: 30%;
}

.text {
  float: left;
  // width: 260px;
  vertical-align: middle;
  border-left: 1px solid rgba(136, 136, 136, 0.3);
}

.text p {
  line-height: 15px;
}

.arrow {
  float: left;
}

::ng-deep .cdk-focused {
  border: none;
  background: none;
}

::ng-deep button .user .cdk-mouse-focused {
  border: none;
  background: none;
}

.navbar {
  position: fixed;
  width: 100%;
  color: #00b9e2;
}

.navbar-right ul {
  width: 20%;
  float: left;
}

.navbar-right li {
  text-transform: uppercase;
}

.wrapperBg {
  width: 100%;
  overflow-x: hidden;
  overflow-y: hidden;
  // background: url("/assets/img/newLayout/bg_everywhere.svg") no-repeat;
  background-color: white;
  background-size: cover;
  height: 100vh;
  z-index: -1000;
  position: fixed;
}

// Main wrapper
.wrapper {
  width: 100%;
  height: auto;
  min-height: 79%;
  overflow-x: hidden;
  overflow-y: hidden;
  background-color: #ffffff;
}

.topnav .sub-menu {
  background: white;
}

.topnav .nav .sidebar-subnav {
  position: fixed;
  width: 100%;

}

.sub {
  color: #e2513b !important;
}

.topnav .sidebar-subnav.opening {
  height: auto;
  -webkit-transition: height .2s ease;
  transition: height .2s ease;
  -webkit-animation: fadeInLeft 0.5s;
  animation: fadeInLeft 0.5s;
  position: fixed;
  width: 100%;
  left: 0;
  margin-top: 3.5%;
  color: #e2513b;
  background: white;
}

.topnav li.active > a {
  border-bottom: solid 2px white;
}

.sidebar-subnav.opening > div.active > a {
  border-bottom: solid 1px #e2513b;
}

.punkt {
  margin-left: 20%;
}

.jumbotron {
  border: none;
  background: none;
}

.topnav .nav > li {
  float: left;
}

.aside-inner {
  display: none;
}

.navbar {
  max-height: 100px;
}

.notification-block {
  display: block;
  position: relative;
  right: 0;
  top: 20px;

  @media only screen and (max-width: 1660px) {
    right: 0;
    top: 17px;
  }

  @media only screen and (max-width: 1370px) {
    top: 11px;
  }
}

.logout {
  background: url("../../../assets/img/newLayout/blue_logout.png") no-repeat center;
  background-size: contain;
  width: 40px;
  height: 40px;
  margin-right: 60px;
  margin-top: 30px;
  z-index: 2000;
  top: 0;
  right: 0;
  position: absolute;
  cursor: pointer;
}

.navbar-right {
  margin-right: 0;
}

.icon-envelope, .icon-documents, .icon-gear {
  font-size: 30px;
  color: #36749d;
  vertical-align: middle;
}

.label {
  background-color: #36749d;
  padding: .5em .1em;
  width: 15px;
  height: 15px;
  text-align: center;
  font-size: 55%;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  border-radius: 50%;
  vertical-align: middle;
  white-space: nowrap;
  position: absolute;
  margin-top: 10px;
  margin-left: -12px;
}

.icon {
  width: 35%;
  float: left;
}

.iconText {
  color: #36749d;
  text-transform: uppercase;
  font-size: 0.8em;
}

.ultimoAccesso {
  color: #36749d;
  font-size: 0.8em;
  text-align: center;
}

.messages {
  display: none;
  background-color: white;
  border-radius: 5px;
  width: auto;
  margin: 90px 15px 0 15px;
}

.username {
  color: #36749d;
  text-transform: uppercase;
  font-size: 18px;
  font-weight: 500;
  padding: 20px;
}

.notification {
  width: 15px;
  height: 15px;
  background-color: #e54c36;
  text-align: center;
  line-height: 16px;
  font-size: 9px;
  color: white;
  border-radius: 50%;
  position: absolute;
}

.col-md-6 {
  width: 45%;
  float: left;
}

.notificBlock {
  padding: 0 0 20px 20px;

  .noteText {
    color: #36749d;
    text-transform: uppercase;
    line-height: 30px;
    font-size: 12px;
    float: left;
  }

  .number {
    position: absolute;
    color: white;
    background-color: #36749d;
    text-align: center;
    width: 14px;
    height: 14px;
    line-height: 15px;
    font-size: 10px;
    border-radius: 50%;
    margin-left: -7px;
  }
}

.info {
  font-size: 12px;
  text-transform: lowercase;
  border-bottom: 1px solid white;
  padding-bottom: 10px;
}

button {
  border: none;
  background: none;
}

.side-menu-icon {
  display: none;
  background: url("../../../assets/img/optimaIcons/menu_blue.png") no-repeat;
  background-size: contain;
  height: 75px;
  position: absolute;
  left: 0;
  width: 100px;
  z-index: 2000;
}

.side-menu-icon-cyan {
  display: none;
  background: url("../../../assets/img/optimaIcons/menu_cyan.png") no-repeat;
  background-size: contain;
  height: 75px;
  position: absolute;
  left: 0;
  width: 100px;
  z-index: 2000;
}

.footerBgMobile {
  margin-top: 10%;
}

.name-in-invoice {
  text-align: right;
  overflow: hidden;
}

@media only screen and (max-width: 1660px) {

  .client-notification {
    padding-top: 5px;
  }

  .userInfo {
    .fa-exclamation-triangle {
      font-size: 18px;
    }

    .fa-check {
      font-size: 18px;
    }
  }
  .icon-envelope, .icon-documents, .icon-gear {
    font-size: 25px;
  }
  .label {
    padding: .4em .1em;
    width: 13px;
    height: 13px;
    position: absolute;
    margin-top: 10px;
    margin-left: -12px;
  }
  .icon {
    width: 30%;
    float: left;
  }
  .iconText {
    font-size: 0.7em;
  }
  .ultimoAccesso {
    font-size: 0.7em;
  }

  /*
  ::ng-deep .cdk-overlay-container {
    margin-top: 80px;
  }

  ::ng-deep .mat-menu-panel {
    width: 240px;
    background-color: #ededed;
  }
  */

  .logout {
    //position: absolute;
    //right: 0;
    width: 35px;
    height: 35px;
    margin-right: 35px;
    margin-top: 25px;
    //top: 0;
  }

  .userInfo {
    .nameBlock {
      font-size: 14px;
    }
  }


  .name-in-invoice {
    .user {
      height: 90px;
      text-transform: uppercase;
      font-size: 12px;
      text-align: center;
    }

    .text {
      vertical-align: middle;
      border-left: 1px solid rgba(136, 136, 136, 0.3);
      width: 230px;
    }

    .text p {
      line-height: 14px;
    }
  }
  .topnav {
    margin-left: 16%;
    position: absolute;
    padding-top: 10px;
    top: 0;
  }
  .topnavbar .navbar-header .brand-logo {
    z-index: 15;
    margin-left: -6%;
  }
  .topnav a {
    padding: 9px 12px 9px 12px;
    font-size: 14px;
  }
  .navbar .topnavbar {
    padding: 0;
  }
  .topnavbar .navbar-header .brand-logo {
    padding: 15px 10px;
  }
  .topnav ul {
    font-size: 12px;
  }
  ul.nav.navbar-nav.navbar-right {
    margin-top: -5px;
  }
  .topnav a:hover {
    border-bottom: 2px solid white;
  }

  .topnav li.active > a {
    border-bottom: 2px solid white;
  }
  .img-responsive {
    width: 70%;
  }
  .navbar {
    max-height: 85px;
  }
  .navbar-header {
    margin-top: 10px;
    width: 240px;

    .app-logo {
      margin-left: 30px;
      margin-right: 0;
    }
  }
}

.communication {
  margin-left: -10px;
  margin-right: -10px;
  border: 1px solid #b6cce3;
  border-radius: 5px;
  margin-top: 60px;

  .icon {
    background: url("/assets/img/optima/Set_Icone_AreaClienti_Comuncazioniperte.png") no-repeat;
    background-size: contain;
    height: 45px;
    position: absolute;
    float: left;
    margin: -5px 4px;
    width: 45px
  }

  .title {
    background-color: #f0f5f9;
    color: #36749d;
    text-align: center;
    font-size: 15px;
    line-height: 25px;
    border-radius: 5px 5px 0 0;
  }

  .items {
    width: 80%;
    margin: auto;
    padding: 10px 0;

    .item {
      list-style-type: disc;
      border: none;
      text-align: justify;
      font-size: 13px;
    }
  }
}

@media only screen and (max-width: 1370px) {

  .client-notification {
    padding: 0;
  }

  .icon-envelope, .icon-documents, .icon-gear {
    font-size: 25px;
  }
  .label {
    padding: .35em .08em;
    width: 12px;
    height: 1px;
    position: absolute;
    margin-top: 10px;
    margin-left: -12px;
  }
  .icon {
    width: 30%;
    float: left;
  }
  .iconText {
    font-size: 0.6em;
  }
  .ultimoAccesso {
    font-size: 0.6em;
  }

  /*
  ::ng-deep .cdk-overlay-container {
    margin-top: 75px;
  }
  */

  /*::ng-deep .mat-menu-panel {
    width: 200px;
    width: 100%;
    background-color: #ededed;
  }*/
  .img-responsive {
    width: 55%;
  }
  .logout {
    width: 30px;
    height: 30px;
    margin-right: 30px;
    margin-top: 20px;
  }
  .navbar {
    max-height: 75px;
  }
  .topnav {
    z-index: 20;
    margin-left: 16%;
    position: absolute;
    top: 0;

  }
  .topnav a {
    padding: 9px 10px 5px 10px;
    font-size: 12px;
    margin-top: -5px;
  }

  .topnavbar .navbar-header .brand-logo {
    z-index: 15;
    padding: 20px 6px;
    margin-left: -6%;
  }
  .topnav ul {
    font-size: 10px;
  }
  .topnav ul.nav.navbar-nav.navbar-right {
    margin-top: -12px;
  }
  .img-responsive {
    width: 40%;
  }
  .dropdown > a > .label {
    top: 20px;
  }
  .topnav li.active > a {
    border-bottom: 1px solid white;
  }
  .userInfo {
    .nameBlock {
      font-size: 12px;
    }
  }
  .navbar-header {
    height: 70px;
    width: 170px;
    margin-left: 0px;

    .img-responsive {
      margin-left: 30px;
      width: 150px;
    }
  }
}

.col {
  float: left;
  padding-top: 20px;

  color: #36749d;
  font-size: 12px;
  line-height: 5px;
}

.gear {
  width: 20px;
  height: 20px;
  background: url("/assets/img/optimaIcons/gear_color.png") no-repeat;
  background-size: contain;

}

.ico {
  float: left;
  right: 0;
  position: absolute;
}

.menu-label:hover {
  border-bottom: 1px solid white;
}

@media only screen and (max-width: 1200px) {
  .userInfo {
    font-size: 12px;

    .fa-exclamation-triangle {
      font-size: 16px;
    }

    .fa-check {
      font-size: 16px;
    }
  }

  .label {
    padding: .35em 0;
    width: 12px;
    height: 1px;
    position: absolute;
    margin-top: 10px;
    margin-left: -25px;
  }
  .topnav {
    margin-left: 16%;
    position: absolute;
    top: 0;
  }
  .topnav a {
    padding: 2px 7px 5px 5px;
  }
  .content-wrapper {
    margin: auto;
  }
  .navbar-header {
    margin-top: 0;
    width: 160px;

    .navbar-brand {
      height: 60px;
    }

    .brand-logo {
      width: 160px;
      margin-left: 0;
    }

    .img-responsive {
      margin-left: 20px;
    }
  }

}

@media only screen and (max-width: 1070px) {
  .mainMenuMobile {
    z-index: 500;
  }
  .topnav {
    position: absolute;
    z-index: 20;
    margin: 0 0 0 16%;
    top: 0;
  }

  .topnav a {
    padding: 1px 3px 5px 3px;
    font-size: 12px;
  }
  .navbar-header {
    margin-left: 0;
  }
  .topnav ul.nav.navbar-nav.navbar-right {
    margin-top: -10px;
    right: 3%;
    position: absolute;
  }

  .topnav a:hover {
    border-bottom: 1px solid white;
  }

  .topnavbar {
    padding-bottom: 15px;
  }
  .sidebar-subnav.opening {
    margin-top: 0;
  }
  .aside-inner {
    display: none !important;
  }
}

@media only screen and (max-width: 980px) {
  app-msg-notification {
    display: none;
  }
  .topnav a {
    padding-top: 5px;
  }
  .userInfo {
    display: none;
  }
  .name-in-invoice {
    display: none;
  }
  .messages {
    margin: 90px 70px 0 70px;
  }
  .messages {
    display: block;
  }

  .navbar-header {
    width: 130px;
    height: 50px;
    margin-top: 7px;

    .navbar-brand {
      height: 50px;
    }

    .brand-logo {
      padding-bottom: 0px;
      height: 47px;
    }

    .img-responsive {
      width: 130px;
    }
  }
}

@media only screen and (max-width: 880px) {

  .notDisplay {
    display: none;
  }
  .navbar-header {
    margin-left: 10px;

    .img-responsive {
      margin-left: 10px;
      width: 120px;
    }
  }
}

@media only screen and (max-width: 800px) {

  .side-menu-icon {
    display: block;
  }
  .side-menu-icon-cyan {
    display: block;
  }

  .aside-inner {
    width: 100%;
    text-align: center;
    background: $cyan !important;
  }
  .topnav {
    display: none;
  }

  .menu {
    position: absolute;
    left: 5%;
    font-size: 25px;
    font-weight: bold;
    margin-top: 15px;
  }
  .img-responsive {
    width: 65%;
    margin-left: 50% !important;
  }
  .topnavbar .navbar-header .brand-logo {
    z-index: 15;
    padding: 14px 16px;
    margin-left: 0;
  }
  .topnavbar {
    padding: 0;
  }
  ul.nav.navbar-nav.navbar-right {
    float: right;
    margin-right: 4%;
  }
  ul.nav.navbar-nav.navbar-right > li {
    float: left;
  }
  .sidebar-subnav > li > a, .sidebar-subnav > li > .nav-item {
    padding: 5px 13px 5px 13px;
    border: 1px solid #36749d;
    border-radius: 10px;
    margin: 5px;
  }
  .sidebar > .nav > li > a, .sidebar > .nav > li > .nav-item, .sidebar > .nav > li {
    padding: 16px 12px;
    color: white;
  }
  .menu {
    display: block;
  }
  .content-wrapper {
    width: 100%;
    padding-left: 0;
    background: none;
    margin: 0 0 50px 0;
  }

  .sidebar-subnav {
    text-align: center;
    width: 100%;
  }
  .sidebar-subnav li {
    display: inline-block;
    width: auto;
    font-size: 12px;
  }

  .open {
    display: block !important;
  }

  .mainMenuMobile {
    height: 100vh;
    position: fixed;
    top: 0;
    background: white;
    text-transform: uppercase;

    .sidebar {
      margin-top: 90px;
      background-color: $cyan;
      color: white;
    }

    .nav li {
      //border-bottom: 1px solid white;
    }
  }

  .col-md-4 {
    border-right: 1px solid white;
  }

  .img-responsive {
    width: 15%;
    margin: auto !important;
  }
  .topnavbar .navbar-header .navbar-brand {
    padding: 0;
    width: 100%;
  }
  .topnavbar .navbar-header .brand-logo {
    z-index: 15;
    padding: 4px 16px;
    margin-left: 0;
  }

  .img-responsive {
    width: 40%;
    margin-left: 30% !important;
  }
  .logout {
    position: absolute;
    width: 20px;
    height: 20px;
    margin-right: 20px;
    margin-top: 15px;
  }
  .navbar {
    max-height: 50px;
  }
  .side-menu-icon {
    height: 50px;
  }
  .img-responsive {
    width: 15%;
    margin: auto !important;
  }
  .navbar-header {
    width: 100%;
    margin-top: 0;
  }
  .messages {
    margin: 60px 60px -10px 60px;
  }
}

@media only screen and (max-width: 800px) {
  .sub {
    color: white !important;

    a {
      color: white !important;
    }
  }

  .navbar-header {
    background: none;
    height: 50px;

    .brand-logo {
      margin: 0 auto !important;
    }
  }

  .content-wrapper {
    margin: 0 0 50px 0;
    padding-left: 15px;
    padding-right: 15px;
  }
  .mainMenuMobile {
    display: none;
  }
}

@media only screen and (max-width: 800px) {
  .mainMenuMobile {
    display: none;
  }
  .navMobile {
    text-align: left;
    padding-top: 5%;
    padding-left: 30%;
    font-size: 26px;
  }
  .navMobile > li > div > a {
    color: white;
  }
}

@media only screen and (max-width: 767px) {
  .messages {
    margin: 60px 15px -30px 15px;
  }
  li.activeM a {
    color: white;
    text-transform: uppercase;

  }
  .navbar-brand {
    height: 50px;
  }
  .navMobile {
    text-align: left;
    padding-top: 5%;
    padding-left: 27%;
    font-size: 25px;
  }
}

// END NEW VERSION //
@media only screen and (max-width: 550px) {
  .img-responsive {
    width: 25%;
    margin: auto !important;
  }
  .messages {
    margin: 60px 15px -20px 15px;
  }
  .navMobile {
    text-align: left;
    padding-left: 22%;
    font-size: 18px;
  }
}

@media only screen and (max-width: 400px) {

  .img-responsive {
    width: 35%;
    margin: auto !important;
  }
  .navMobile {
    text-align: left;
    padding-left: 20%;
    font-size: 16px;
  }
}

@media only screen and (max-width: 300px) {
  .img-responsive {
    width: 45%;
  }
  .navMobile {
    text-align: left;
    padding-left: 15%;
    font-size: 16px;
  }
}
