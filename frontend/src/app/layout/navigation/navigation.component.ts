import {Component, On<PERSON>hanges, OnDestroy, OnInit, SimpleChanges, ViewChild} from '@angular/core';

import {MenuService} from '../../core/menu/menu.service';
import {SettingsService} from '../../core/settings/settings.service';
import {Observable} from 'rxjs/Observable';
import {select} from '@angular-redux/store';
import {MatMenuTrigger} from '@angular/material';
import {Toast} from 'angular2-toaster';
import {UserDataService} from '../../common/services/user-data/userData.service';
import {UserActions} from '../../redux/user/actions';
import {UserData} from '../../common/model/userData.model';

import {ServicesActions} from '../../redux/services/actions';
import {AuthService} from '../../services/auth/auth.service';
import ChatUser from '../../chat/model/ChatUser';

import {UserServicesService} from '../../routes/profilePage/userServices/userServices.service';
import {OffersService} from '../../common/services/offers/offers.service';
import {Subscription} from 'rxjs/Subscription';
import {ObservableUtils} from '../../common/utils/ObservableUtils';
import {InvoiceService} from '../../routes/invoices/invoice.service';
import Msg from '../../common/model/Msg';
import {AllServices} from '../../common/enum/AllServices';
import {ContabileActions} from '../../redux/contabile/actions';
import CommunicationState from '../../redux/model/CommunicationState';
import {NotificaMdpInfo} from '../../common/model/notifica-mdp/NotificaMdpInfo';
import {getModalDataWithUnSaved, infoModalAboutUnsavedPromoChangesModalData} from '../../routes/profilePage/config/config';
import {DialogModalActions} from '../../redux/dialogModal/actions';
import {MonthModalActions} from '../../redux/monthModal/actions';
import {ClientNotificationService} from '../../common/services/client-notification/client-notification.service';

@Component({
  selector: 'app-navigation',
  templateUrl: './navigation.component.html',
  styleUrls: ['./navigation.component.scss']
})
export class NavigationComponent implements OnDestroy, OnChanges, OnInit {
  @ViewChild(MatMenuTrigger) trigger: MatMenuTrigger;
  menuItems: Array<any>;
  userInfo: UserData;
  index: number;
  routerDisplay = false;
  @select(['services', 'services'])
  services: Observable<object>;
  @select(['services', 'activeServices'])
  serviceData: Observable<object>;
  display = false;
  aggiornaDisplay = false;
  @select(['notification', 'toast'])
  toast: Observable<Toast>;
  msgs: Array<Msg> = [];
  @select(['user', 'userInfo'])
  userInfoRedux: Observable<UserData>;

  @select(['monthModal', 'canRedirectWithUnsaved'])
  isUnsavedRedux: Observable<boolean>;
  isUnsaved;


  @select(['communication'])
  communication: Observable<CommunicationState>;

  @select(['notificaMdp', 'notificaMdpInfo'])
  notificaMdpInfo: Observable<NotificaMdpInfo>;

  width = window.innerWidth > 980;

  data: Array<any>;
  chatUser: ChatUser;
  userName: string;
  active = [];
  hasPasswordPec = false;

  contabile: number;

  userInfoSubscription: Subscription;
  servicedDataSubscription: Subscription;
  servicesSubscription: Subscription;
  communicationSubscription: Subscription;
  promoSubscription: Subscription;
  activeMenu = false;
  totalNumber = 0;

  constructor(
    private servicesActions: ServicesActions,
    private service: UserServicesService,
    private offersService: OffersService,
    public menu: MenuService,
    public settings: SettingsService,
    private userDataService: UserDataService,
    private userActions: UserActions,
    private contabileActions: ContabileActions,
    private authService: AuthService,
    private invoiceService: InvoiceService,
    private dialogModalActions: DialogModalActions,
    private monthModalActions: MonthModalActions,
    private clientNotificationService: ClientNotificationService
    // private communicationActions: CommunicationActions
  ) {
    this.invoiceService.getSaldo().subscribe(saldo => {
      if (saldo) {
        this.contabile = saldo;
        this.totalNumber++;
        this.msgs.push(new Msg('0', null, '', false));
      }
    });
    this.service.getUserData().subscribe(response => {
      this.servicesActions.servicesLoaded(response);
    });

    this.index = 0;
    // this call should be moved to other place. Quick fix.
    this.userDataService.getUserData().subscribe(userInfo => {
      userActions.userInfoLoaded(userInfo);
    });
    this.userInfoSubscription = this.userInfoRedux.subscribe(userInfo => {
      if (userInfo) {
        const chatUser = new ChatUser();
        chatUser.email = userInfo.email;
        chatUser.firstName = userInfo.firstName;
        chatUser.lastName = userInfo.lastName;
        chatUser.username = userInfo.nameInInvoice;
        chatUser.phoneNumber = userInfo.phoneNumber;
        this.hasPasswordPec = userInfo.hasPasswordPec;
        this.chatUser = chatUser;

        this.userInfo = userInfo;
        this.userName = userInfo.nameInInvoice ? userInfo.nameInInvoice : userInfo.firstName;
      }
    });

    this.menuItems = menu.geMenuWithSubMenu();
    this.offersService.loadClientOffers(localStorage.getItem('clientId')).subscribe(
      (response: any) => {
        this.userActions.clientOffersLoaded(response);
        if (response.length > 0) {
          const mills = new Date().getTime();
          this.active = response.filter(value => value.scadenzaAnnoContrattuale > mills || value.scadenzaAnnoContrattuale == null);
        }
      });

    this.servicedDataSubscription = this.serviceData.subscribe(services => {
      this.routerDisplay = services && (services[AllServices.ADSL] || services[AllServices.VOIP]);
    });

    this.servicesSubscription = this.services.subscribe(data => {
      const temp = Object.values(data).filter(obj => obj['serviceName'] === 'ADSL'
        || obj['serviceName'] === 'VOIP');
      if (temp.length) {
        this.aggiornaDisplay = true;
      }
    });

    this.communicationSubscription = this.communication.subscribe((communicationState) => {
      const {recommendedBlocks} = communicationState;

      if (recommendedBlocks) {
        this.data = recommendedBlocks.map((value) => {
          return {
            creationDate: value.recommendedDate,
            subject: 'Raccomandata',
            fileUrl: this.getPdfFileUrl(value.pdfName)
          };
        });
      }
    });

    this.promoSubscription = this.isUnsavedRedux.subscribe(isUnsavedReduxItem => {
      if (isUnsavedReduxItem) {
        this.isUnsaved = isUnsavedReduxItem;
      }
    });
  }

  getPdfFileUrl(fileName: string) {
    return fileName && `/api/communication/recommended/pdf/${localStorage.getItem('clientId')}?file=${fileName}&access_token=${localStorage.getItem('access_token')}`;
  }

  closeMenu() {
    this.index = 0;
  }

  goHome() {
    this.authService.goHome();
  }

  indexIncrement() {
    this.index++;
  }

  logout() {
    if (this.isUnsaved) {
      this.dialogModalActions.showDialogModal(infoModalAboutUnsavedPromoChangesModalData);
      this.monthModalActions.sendUnsavedStatusToSubscribers(getModalDataWithUnSaved(false));
    } else {
      this.authService.logout();
    }
  }

  toggle() {
    if (this.display) {
      this.msgs.forEach(msg => {
        msg.status = true;
      });
      if (this.msgs.filter(msg => msg.status === true).length) {
        this.totalNumber = 0;
      }
    }
    this.display = !this.display;
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.userInfoSubscription, this.servicedDataSubscription, this.communicationSubscription, this.promoSubscription]);
  }

  ngOnChanges(changes: SimpleChanges): void {
  }

  ngOnInit(): void {
  }

}
