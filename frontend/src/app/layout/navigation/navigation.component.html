<!-- START Top Navbar-->
<div class="wrapperBg">
  <a name="top">
  </a>
</div>
<div class="wrapper">
  <!--<app-sidebar class="aside"></app-sidebar>-->
  <!-- top navbar-->
  <nav class="navbar topnavbar" role="navigation">
    <div class="side-menu-icon" [ngClass]="{'side-menu-icon-cyan': index%2===1 }" (click)="indexIncrement()">
    </div>
    <div class="navbar-header">
      <a class="navbar-brand" [routerLink]="['/home/<USER>']">
        <div class="brand-logo">
          <img class="img-responsive" src="assets/img/logo/optima_new_main_logo.svg" alt="App Logo" (click)="goHome()">
        </div>
      </a>
    </div>
    <!--ORIGINAL TOP NAV-->
    <!--<div class="topnav">-->
    <!--<ul class="nav">-->

    <!--<li *ngFor='let item of menuItems' [ngClass]="{'nav-heading': item.heading}" [routerLinkActive]="['active']">-->
    <!--<a *ngIf="!item.heading && !item.submenu && !item.elink" [routerLink]="item.link" title="{{item.text}}">-->
    <!--<span class="pull-right" *ngIf="item.alert" [ngClass]="item.label || 'label label-success'">{{item.alert}}-->
    <!--</span>-->
    <!--<span>{{item.text}}</span>-->
    <!--</a>-->

    <!--&lt;!&ndash; has submenu &ndash;&gt;-->
    <!--<a *ngIf="!item.heading && item.submenu" [routerLink]="item.link"-->
    <!--title="{{item.text}}">-->
    <!--<span class="pull-right" *ngIf="item.alert"-->
    <!--[ngClass]="item.label || 'label label-success'">{{item.submenu[0].alert}}-->
    <!--</span>-->
    <!--<span>{{item.text}}</span>-->
    <!--</a>-->
    <!--</li>-->
    <!--</ul>-->
    <!--</div>-->

    <!--FIX FOR LIGHT VERSION-->
    <div class="topnav">
      <ul class="nav">

        <li *ngFor='let item of menuItems' [ngClass]="{'nav-heading': item.heading}" [routerLinkActive]="['active']">
          <div *ngIf="item.text !== 'MODULI'">
            <a *ngIf="!item.heading && !item.submenu && !item.elink" [routerLink]="item.link" title="{{item.text}}">
              <span class="pull-right" *ngIf="item.alert" [ngClass]="item.label || 'label label-success'">{{item.alert}}
              </span>
              <span>{{item.text}}</span>
            </a>

            <!-- has submenu -->
            <a *ngIf="!item.heading && item.submenu" [routerLink]="item.link"
               title="{{item.text}}">
              <span class="pull-right" *ngIf="item.alert"
                    [ngClass]="item.label || 'label label-success'">{{item.submenu[0].alert}}
              </span>
              <span>{{item.text}}</span>
            </a>
          </div>
          <div *ngIf="item.text === 'MODULI'">
            <a title="{{item.text}}" href="http://www.optimaitalia.com/moduli-optima-italia.html" target="_blank">
              <span>{{item.text}} </span>
            </a>
          </div>

        </li>
      </ul>
    </div>
    <div *ngIf="userInfo" class="userInfo">
      <div class="notification-block">
        <app-client-notification></app-client-notification>
      </div>
      <div class="block">
        <p *ngIf="contabile " class="triangle">
          <i class="fa fa-exclamation-triangle fa-4x"></i>
        </p>
        <p *ngIf="!contabile || contabile <=0" class="tick">
          <i class="fa fa-check fa-4x"></i>
        </p>
      </div>
      <div class="nameBlock" (click)="toggle()">
        <p>{{userInfo.nameInInvoice}}
          <span *ngIf="totalNumber" class="msgNumber">{{totalNumber}}</span>
        </p>
      </div>
    </div>
    <div class="logout" (click)="logout()"></div>
  </nav>

  <app-msg-notification [msgs]="msgs" [ngClass]="{'display': display }">
    <div>
      Attenzione, risulta uno scoperto di €
      <b>{{contabile | number : '1.2-2'}}</b>
      per dettagli e per procedere al pagamento accedi alla sezione
      <i routerLink="/invoices/all">"Fatture e Pagamenti"</i>
    </div>
  </app-msg-notification>
  <!--START sidebar nav-->
  <!-- Main section-->
  <div class="aside-inner mainMenuMobile pull-right" [ngClass]="{'open': index%2===1 }">
    <nav class="sidebar" [class.show-scrollbar]="settings.layout.asideScrollbar">

      <!-- START sidebar nav-->
      <ul class="nav navMobile">
        <li *ngFor='let item of menuItems' [ngClass]="{'nav-heading': item.heading}" [routerLinkActive]="['activeM']">
          <!-- menu heading -->
          <div *ngIf="item.text !== 'MODULI'">
            <span *ngIf="item.heading">{{item.text}}</span>
            <!-- single menu item -->
            <a *ngIf="!item.heading && !item.submenu && !item.elink" [routerLink]="item.link" (click)="closeMenu()"
               [attr.route]="item.link"
               title="{{item.text}}">
              <span class="menu-label">{{item.text}}</span>
            </a>
            <!-- has submenu -->
            <a *ngIf="!item.heading && item.submenu" class="sub" title="{{item.text}}" (click)="closeMenu()"
               [routerLink]="item.link">
              <span class="menu-label">{{item.text}}</span>
            </a>
          </div>
          <div *ngIf="item.text === 'MODULI'">
            <a title="{{item.text}}" href="http://www.optimaitalia.com/moduli-optima-italia.html" target="_blank">
              <span class="menu-label">{{item.text}} </span>
            </a>
          </div>

          <!-- SUBLEVEL -->
          <!--<ul *ngIf="item.submenu" class="nav sidebar-subnav" [routerLinkActive]="['opening']"-->
          <!--[routerLinkActiveOptions]="{exact:true}">-->
          <!--<li *ngFor='let subitem of item.submenu' [routerLinkActive]="['activeSubMenu']">-->
          <!--&lt;!&ndash; sublevel: single menu item  &ndash;&gt;-->
          <!--<a *ngIf="!subitem.submenu && subitem.link!=='/profile/tutto-in-uno'" [routerLink]="subitem.link"-->
          <!--(click)="closeMenu()" [attr.route]="subitem.link"-->
          <!--title="{{subitem.text}}">-->

          <!--<span>{{subitem.text}}</span>-->
          <!--</a>-->
          <!--<a *ngIf="!subitem.submenu && subitem.link=='/profile/tutto-in-uno' && active.length>0"-->
          <!--[routerLink]="subitem.link" (click)="closeMenu()" [attr.route]="subitem.link"-->
          <!--title="{{subitem.text}}">-->
          <!--<span>{{subitem.text}}</span>-->
          <!--</a>-->
          <!--<li>-->
          <!--<li>-->
          <!--<a *ngIf="routerDisplay && item.link ==='/faidate'"-->
          <!--[routerLink]="['/faidate/routerConfigurazione/configuraIlTuoRouter']" (click)="closeMenu()">-->
          <!--<span>CONFIGURAZIONE ROUTER</span>-->
          <!--</a>-->
          <!--<a *ngIf="!routerDisplay && item.link ==='/faidate' && aggiornaDisplay"-->
          <!--[routerLink]="['/faidate/routerConfigurazione/aggiorna']" (click)="closeMenu()">-->
          <!--<span>AGGIORNA IL SOFTWARE DEL ROUTER</span>-->
          <!--</a>-->
          <!--</li>-->
          <!--<li class="able {{activeMenu?'activeSubMenu':''}}">-->
          <!--<a *ngIf="hasPasswordPec && item.link ==='/faidate'" (click)="closeMenu()" target="_blank"-->
          <!--[attr.href]="'https://webmail.pec.it'">-->
          <!--<span>ACCESSO PEC</span>-->
          <!--</a>-->
          <!--</li>-->
          <!--<li>-->
          <!--<a *ngIf="item.link ==='/faidate'" (click)="openChat()"-->
          <!--href="javascript: void(0)">-->
          <!--<span>CHATTA CON NOI</span>-->
          <!--</a>-->
          <!--</li>-->
          <!--<li>-->
          <!--<a *ngIf="item.link ==='/faidate'" [routerLink]="['/faidate/recontact']"-->
          <!--href="javascript: void(0)">-->
          <!--<span>RICHIEDI RICONTATTO</span>-->
          <!--</a>-->
          <!--</li>-->
          <!--</ul>-->
        </li>
      </ul>
      <!-- END sidebar nav-->
      <div class="footerBgMobile">
        <div class="brand-logo">
          <img class="img-responsive" src="assets/img/logo/optima-white.svg" alt="App Logo"/>
        </div>
        <!--<p class="link" routerLink="/assistant" (click)="closeMenu()">
          CONTATTACI
        </p>-->

        <p class="link">
          <a style="color: white;" href="http://www.optimaitalia.com" (click)="closeMenu()">TORNA AL SITO</a>
        </p>

      </div>
    </nav>
  </div>
  <!--END Sidebar (left)-->

  <!--  <div class="communication messages" *ngIf="userInfo">-->
  <!--    <div class="icon"></div>-->
  <!--    <div class="title">-->
  <!--      Comunicazioni per te-->
  <!--    </div>-->
  <!--    <ul class="items" *ngIf="hasCommunication | async;else noCommunicationMessage">&lt;!&ndash;&ndash;&gt;-->
  <!--      <div *ngFor="let item of data; let i = index">-->
  <!--        <li *ngIf="i > data.length - 4" class="item-comunicazione">{{item.subject}}        {{item.creationDate| date : "dd/MM/y"}}        <a-->
  <!--          *ngIf="item.fileUrl" class="icon-pdf-load" [attr.href]="item.fileUrl" target="_blank"></a></li>-->
  <!--      </div>-->
  <!--    </ul>-->
  <!--    <ng-template #noCommunicationMessage>-->
  <!--      <div class="no-communication-message">L'Area Clienti potrebbe non avere tutte le funzionalità normalmente disponibili.-->
  <!--        Stiamo rapidamente procedendo al ripristino totale con interventi di perfezionamento e manutenzione.-->
  <!--        Ci scusiamo per il temporaneo disagio.</div>-->
  <!--    </ng-template>-->
  <!--  </div>-->

  <!--Page content-->

  <div class="content-wrapper">
    <router-outlet></router-outlet>
  </div>
  <app-e-mail class="email-button"></app-e-mail>
  <app-optima-chat [chatUser]="chatUser"></app-optima-chat>
  <app-notification-component [toast]="toast | async"></app-notification-component>
  <app-dialog-modal-wrapper></app-dialog-modal-wrapper>
  <app-month-modal-wrapper></app-month-modal-wrapper>
  <app-notifica-mdp [mdpInfo]="notificaMdpInfo"></app-notifica-mdp>
  <app-modal-wrapper></app-modal-wrapper>
</div>
<app-footer></app-footer>





