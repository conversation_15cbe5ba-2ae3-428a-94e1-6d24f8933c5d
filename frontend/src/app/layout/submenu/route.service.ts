import {Injectable} from '@angular/core';

import 'rxjs/add/operator/delay';
import 'rxjs/add/operator/do';
import {MenuTag} from './submenu.model';

@Injectable()
export class RouteService {
  public getSubmenu(route, menu): MenuTag {
    const menuItems = menu.geMenuWithSubMenu();
    let tags: MenuTag = null;
    const curUrl = route.parent.url.value[0].path;
    menuItems.forEach(tag => {
      if (tag.link.split('/')[1] === curUrl && tag.submenu) {
        tags = tag;
      }
    });
    return tags;
  }
}
