import ComunicazioniEmailInfo from '../../common/model/communications/ComunicazioniEmailInfo';
import { RecommendedBlock } from '../../common/model/communications/RecommendedBlock';
import { ComunicazioniNote } from '../../common/model/communications/ComunicazioniNote';

export default class CommunicationState {
  communicationsEmailsLoading: boolean;
  communicationsEmailsLoaded: boolean;
  recommendedBlocksLoading: boolean;
  recommendedBlocksLoaded: boolean;
  communicationNoteLoading: boolean;
  communicationNoteLoaded: boolean;
  communicationEmailInfo: Array<ComunicazioniEmailInfo>;
  recommendedBlocks: Array<RecommendedBlock>;
  communicationNote: Array<ComunicazioniNote>;
  hasCommunicationEmailInfo: boolean;
  hasRecommendedBlocks: boolean;
  hasCommunicationNote: boolean;
}
