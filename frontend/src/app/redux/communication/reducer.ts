import CommunicationState from '../model/CommunicationState';
import { Type } from './types';


const initialState = new CommunicationState();


export default function reducer(state = initialState, action) {
  switch (action.type) {
    case Type.COMMUNICATION_EMAIL_LOADING:
      return {...state, communicationsEmailsLoading: true};
    case Type.COMMUNICATION_EMAIL_LOADED:
      return {
        ...state,
        communicationsEmailsLoading: false,
        communicationsEmailsLoaded: true,
        communicationEmailInfo: action.communicationEmailInfo,
        hasCommunicationEmailInfo: action.hasCommunicationEmailInfo
      };
    case Type.RECOMMENDED_BLOCK_LOADING:
      return {...state, recommendedBlocksLoading: true};
    case Type.RECOMMENDED_BLOCK_LOADED:
      return {
        ...state,
        recommendedBlocksLoading: false,
        recommendedBlocksLoaded: true,
        recommendedBlocks: action.recommendedBlocks,
        hasRecommendedBlocks: action.hasRecommendedBlocks
      };
    case Type.COMMUNICATION_NOTE_LOADING:
      return {...state, communicationNoteLoading: true};
    case Type.COMMUNICATION_NOTE_LOADED:
      return {
        ...state,
        communicationNoteLoading: false,
        communicationNoteLoaded: true,
        communicationNote: action.communicationNote,
        hasCommunicationNote: action.hasCommunicationNote
      };
    default:
      return state;
  }

}
