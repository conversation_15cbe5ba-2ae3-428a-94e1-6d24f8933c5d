import {getContext, put, takeEvery} from 'redux-saga/effects';
import {Type} from '../services/types';
import {ConfigurazioneRouterService} from '../../common/services/configurazione-router/configurazione-router.service';


function* loadRouterInfo(action) {
  const context = yield getContext('context');
  const service = context.get(ConfigurazioneRouterService);
  const routerDetails = yield (<ConfigurazioneRouterService>service).loadRouterInfo(action.clientId).toPromise();
  yield put({type: Type.ROUTER_INFO_LOADED, routerInfo: routerDetails});
}


export function* routerRootSaga() {
  yield takeEvery(Type.ROUTER_INFO_LOADING, loadRouterInfo);
}
