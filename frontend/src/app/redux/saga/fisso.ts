import {getContext, put, takeEvery} from 'redux-saga/effects';
import {Type} from '../services/types';
import {NormalizeUtils} from '../../common/utils/NormalizeUtils';
import {FissoService} from '../../common/services/fisso/fisso.service';


function* loadFissoPodDetails(action) {
  const context = yield getContext('context');
  const service = context.get(FissoService);
  const podDetails = yield (<FissoService>service).getAllPodDetailsWithVlanId(action.clientId).toPromise();
  yield put({type: Type.FISSO_POD_DETAILS_LOADED, podDetails: NormalizeUtils.normalizeList(podDetails, 'dialer')});
}


export function* fissoRootSaga() {
  yield takeEvery(Type.FISSO_POD_DETAILS_LOADING, loadFissoPodDetails);
}
