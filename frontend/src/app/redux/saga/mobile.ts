import { getContext, put, takeEvery } from 'redux-saga/effects';
import { Type } from '../mobile/types';
import { MobileService } from '../../common/services/mobile/mobile.service';


function* loadMobileContractRecords(action) {
  const context = yield getContext('context');
  const service = context.get(MobileService);
  const contractRecords = yield (<MobileService>service).loadContractRecords(action.clientId).toPromise();
  yield put({type: Type.CONTRACT_RECORDS_LOADED, contractRecords});
}


export function* mobileRootSaga() {
  yield takeEvery(Type.CONTRACT_RECORDS_LOADING, loadMobileContractRecords);
}
