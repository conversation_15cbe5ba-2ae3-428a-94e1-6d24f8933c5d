import { Type } from './types';
import { dispatch } from '@angular-redux/store';
import { Injectable } from '@angular/core';
import { UserDetails } from '../../common/model/UserDetails';
import { UserData } from '../../common/model/userData.model';
import { Offer } from '../model/Offer';


@Injectable()
export class UserActions {

  @dispatch()
  setUerDetails = (userDetails: UserDetails) => ({
    type: Type.SET_USER_DETAILS,
    userDetails
  })

  @dispatch()
  userInfoLoaded = (userInfo: UserData) => ({
    type: Type.USER_INFO_LOADED,
    userInfo
  })

  @dispatch()
  clientOffersLoaded = (offers: Array<Offer> = []) => {
    const mills = new Date().getTime();
    const activeOffers = offers.filter(value => value.scadenzaAnnoContrattuale > mills || value.scadenzaAnnoContrattuale == null);
    return {
      type: Type.USER_OFFERS_LOADED,
      offers,
      activeOffers,
      hasActiveOffers: activeOffers.length > 0
    };
  }


}
