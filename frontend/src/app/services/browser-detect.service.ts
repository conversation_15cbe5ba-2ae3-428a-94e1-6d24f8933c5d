import { Injectable } from '@angular/core';

@Injectable()
export class BrowserDetectService {

    osData: any;

    init() {
        const brwData = this.searchBrwData() || null;
        const osData = this.searchOsData();
        const isSupport = this.checkSupport(brwData, osData);
        //
        this.consoleLogData(brwData, osData, isSupport);
    }

    consoleLogData(brwData, osData, isSupport) {
        alert(
            '\n' +
            'os.Name = ' + osData + '\n' +
            'brw.name = ' + brwData.browserName + '\n' +
            'brw.fullVersion = ' + brwData.fullVersion + '\n' +
            'brw.majorVersion = ' + brwData.majorVersion + '\n' +
            'brw.minSupportVersion = ' + brwData.minSupportVersion + '\n' +
            'isSupport = ' + isSupport + '\n' +
            'navigator.appName = ' + brwData.navigatorAppName + '\n' +
            'navigator.userAgent = ' + brwData.navigatorUserAgent + '\n' +
            '\n'
        );
    }


    checkSupport(brwData, osData) {
        const result = +brwData.majorVersion >= +brwData.minSupportVersion;
        //
        return result;
    }

    searchOsData() {
        if (navigator.appVersion.indexOf('Win') !== -1) { this.osData = 'Windows'; }
        if (navigator.appVersion.indexOf('Mac') !== -1) { this.osData = 'MacOS'; }
        if (navigator.appVersion.indexOf('X11') !== -1) { this.osData = 'UNIX'; }
        if (navigator.appVersion.indexOf('Linux') !== -1) { this.osData = 'Linux'; }
        //
        return this.osData;
    }

    searchBrwData() {
        const nAgt = navigator.userAgent;
        let browserName = navigator.appName;
        let fullVersion = '' + parseFloat(navigator.appVersion);
        let majorVersion = parseInt(navigator.appVersion, 10);
        let minSupportVersion = 0;
        let nameOffset: any;
        let verOffset: any;
        let ix: any;


        // In Opera, the true version is after "Opera" or after "Version"
        if ((verOffset = nAgt.indexOf('OPR')) !== -1) {
            browserName = 'Opera';
            fullVersion = nAgt.substring(verOffset + 4);
            if ((verOffset = nAgt.indexOf('Version')) !== -1) {
                fullVersion = nAgt.substring(verOffset + 8);
            }
            minSupportVersion = 55; // Opera latest. latest - 55 / https://en.wikipedia.org/wiki/History_of_the_Opera_web_browser
        }
        // In Opera, the true version is after "Opera" or after "Version"
        if ((verOffset = nAgt.indexOf('Opera')) !== -1) {
            browserName = 'Opera';
            fullVersion = nAgt.substring(verOffset + 6);
            if ((verOffset = nAgt.indexOf('Version')) !== -1) {
                fullVersion = nAgt.substring(verOffset + 8);
            }
            minSupportVersion = 55; // Opera latest. latest - 55 / https://en.wikipedia.org/wiki/History_of_the_Opera_web_browser
        }
        // In MSIE, the true version is after "MSIE" in userAgent
        else if ((verOffset = nAgt.indexOf('MSIE')) !== -1) {
            browserName = 'Microsoft Internet Explorer';
            fullVersion = nAgt.substring(verOffset + 5);
            minSupportVersion = 9; // versions - 9, 10, 11 / https://en.wikipedia.org/wiki/Internet_Explorer_version_history
        }

        // In Trident, the true version is after "Trident" in userAgent
        else if ((verOffset = nAgt.indexOf('Trident')) !== -1) {
            browserName = 'Microsoft Internet Explorer';
            fullVersion = nAgt.substring(verOffset + 8);
            if ((verOffset = nAgt.indexOf('rv:')) !== -1) {
                fullVersion = nAgt.substring(verOffset + 3);
            }
            minSupportVersion = 5; // versions - 5, 6, 7, 8 (ie - 9,10,11) / https://en.wikipedia.org/wiki/Trident_(software)
        }

        // In Edge, the true version is after "Edge" in userAgent
        else if ((verOffset = nAgt.indexOf('Edge')) !== -1) {
            browserName = 'Edge';
            fullVersion = nAgt.substring(verOffset + 5);
            minSupportVersion = 16; // (16,17) 2 most recent major versions. latest - 17 / https://en.wikipedia.org/wiki/Microsoft_Edge
        }

        // In Chrome, the true version is after "Chrome"
        else if ((verOffset = nAgt.indexOf('Chrome')) !== -1) {
            browserName = 'Chrome';
            fullVersion = nAgt.substring(verOffset + 7);
            minSupportVersion = 33; // Chrome latest. latest - 68 / https://en.wikipedia.org/wiki/Google_Chrome_version_history
        }
        // In Safari, the true version is after "Safari" or after "Version"
        else if ((verOffset = nAgt.indexOf('Safari')) !== -1) {
            browserName = 'Safari';
            fullVersion = nAgt.substring(verOffset + 7);
            if ((verOffset = nAgt.indexOf('Version')) !== -1) {
                fullVersion = nAgt.substring(verOffset + 8);
            }
            minSupportVersion = 4; // (4, 5) 2 most recent major versions / https://en.wikipedia.org/wiki/Safari_version_history
        }
        // In Firefox, the true version is after "Firefox"
        else if ((verOffset = nAgt.indexOf('Firefox')) !== -1) {
            browserName = 'Firefox';
            fullVersion = nAgt.substring(verOffset + 8);
            minSupportVersion = 44; // Firefox latest. latest - 62 / https://en.wikipedia.org/wiki/Firefox_version_history
        }
        // In most other browsers, "name/version" is at the end of userAgent
        else if ((nameOffset = nAgt.lastIndexOf(' ') + 1) <
            (verOffset = nAgt.lastIndexOf('/'))) {
            browserName = nAgt.substring(nameOffset, verOffset);
            fullVersion = nAgt.substring(verOffset + 1);
            if (browserName.toLowerCase() === browserName.toUpperCase()) {
                browserName = navigator.appName;
            }
        }
        // trim the fullVersion string at semicolon/space if present
        if ((ix = fullVersion.indexOf(';')) !== -1) {
            fullVersion = fullVersion.substring(0, ix);
        }
        if ((ix = fullVersion.indexOf(' ')) !== -1) {
            fullVersion = fullVersion.substring(0, ix);
        }
        if ((ix = fullVersion.indexOf(')')) !== -1) {
            fullVersion = fullVersion.substring(0, ix);
        }

        majorVersion = parseInt('' + fullVersion, 10);
        if (isNaN(majorVersion)) {
            fullVersion = '' + parseFloat(navigator.appVersion);
            majorVersion = parseInt(navigator.appVersion, 10);
        }

        const brwData = {
            browserName: browserName,
            fullVersion: fullVersion,
            majorVersion: majorVersion,
            navigatorAppName: navigator.appName,
            navigatorUserAgent: navigator.userAgent,
            minSupportVersion: minSupportVersion
        };
        //
        return brwData;
    }

}
