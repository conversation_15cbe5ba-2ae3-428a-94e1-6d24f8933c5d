import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, CanActivateChild, CanLoad, Route, Router, RouterStateSnapshot} from '@angular/router';

import {AuthService} from './auth.service';
import {Observable} from 'rxjs/Observable';
import {map} from 'rxjs/operators';

@Injectable()
export class AuthGuard implements CanActivate, CanActivateChild, CanLoad {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {
  }

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> | boolean {
    const xOptimaAuth = route.queryParamMap.get('X-Optima-Auth');
    if (xOptimaAuth && this.isValidXOptimaAuth(xOptimaAuth)) {
      return this.checkSSOLogin(state.url, xOptimaAuth);
    } else {
      const url = state.url;
      return this.checkLogin(url);
    }
  }

  canActivateChild(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> | boolean {
    return this.canActivate(route, state);
  }

  canLoad(route: Route): boolean {
    const url = `/${route.path}`;
    return this.checkLogin(url);
  }

  checkLogin(url: string): boolean {
    if (this.authService.checkLogin()) {
      return true;
    }
    // Store the attempted URL for redirecting
    this.authService.redirectUrl = url;
    this.router.navigate(['/login']);
    return false;
  }

  checkSSOLogin(url: string, token: string): Observable<boolean> {
    return this.authService.checkSSOLogin(token, token.split(',')[1]).pipe(
      map((isLoggedIn) => {
        if (isLoggedIn) {
          const currentUrl = this.router.parseUrl(url);
          delete currentUrl.queryParams['X-Optima-Auth'];
          this.router.navigateByUrl(currentUrl);
          return true;
        } else {
          this.authService.redirectUrl = url;
          this.router.navigate(['/login']);
          return false;
        }
      })
    );
  }

  private cleanUrl() {
    // Remove X-Optima-Auth from the URL without reloading
    const currentUrlTree = this.router.parseUrl(this.router.url);
    currentUrlTree.queryParams = {};
    return currentUrlTree.queryParams = {};  // Strip query parameters
  }

  private isValidXOptimaAuth(value: string): boolean {
    const regex = /^[a-f0-9]{32},\d+,\d+$/;
    return regex.test(value);
  }
}
