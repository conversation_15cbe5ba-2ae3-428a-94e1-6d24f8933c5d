export enum CIChatMessageType {
  CHAT_MESSAGE_FROM_CUSTOMER = 'CHAT_MESSAGE_FROM_CUSTOMER',
  CHAT_MESSAGE_FROM_AGENT = 'CHAT_MESSAGE_FROM_AGENT',
  PAGE_PUSHED_BY_CUSTOMER = 'PAGE_PUSHED_BY_CUSTOMER',
  PAGE_PUSHED_BY_AGENT = 'PAGE_PUSHED_BY_AGENT',
  FORM_SHARED_BY_CUSTOMER = 'FORM_SHARED_BY_CUSTOMER',
  FORM_SHARED_BY_AGENT = 'FORM_SHARED_BY_AGENT',
  CALL_ME_REQUEST_FROM_CUSTOMER = 'CALL_ME_REQUEST_FROM_CUSTOMER',
  PAGE_PREVIEWED_BY_CUSTOMER = 'PAGE_PREVIEWED_BY_CUSTOMER',
  PAGE_PREVIEWED_BY_AGENT = 'PAGE_PREVIEWED_BY_AGENT',
  SESSION_DISCONNECTED_BY_CUSTOMER = 'SESSION_DISCONNECTED_BY_CUSTOMER',
  SESSION_DISCONNECTED_BY_AGENT = 'SESSION_DISCONNECTED_BY_AGENT',
  PRIVATE_CHAT_MESSAGE_BETWEEN_AGENTS = 'PRIVATE_CHAT_MESSAGE_BETWEEN_AGENTS',
  COMFORT_MESSAGE = 'COMFORT_MESSAGE',
  CHAT_MESSAGE_FROM_CUSTOM_AGENT = 'CHAT_MESSAGE_FROM_CUSTOM_AGENT',
  PAGE_PUSHED_BY_CUSTOM_AGENT = 'PAGE_PUSHED_BY_CUSTOM_AGENT',
  FORM_SHARED_BY_CUSTOM_AGENT = 'FORM_SHARED_BY_CUSTOM_AGENT',
  PAGE_PREVIEWED_BY_CUSTOM_AGENT = 'PAGE_PREVIEWED_BY_CUSTOM_AGENT',
  SESSION_DISCONNECTED_BY_CUSTOM_AGENT = 'SESSION_DISCONNECTED_BY_CUSTOM_AGENT',
  PRIVATE_CHAT_MESSAGE_BETWEEN_CUSTOM_AGENTS = 'PRIVATE_CHAT_MESSAGE_BETWEEN_CUSTOM_AGENTS',
  PRIVATE_CHAT_MESSAGE_BETWEEN_AGENTS_AND_CUSTOM_AGENTS = 'PRIVATE_CHAT_MESSAGE_BETWEEN_AGENTS_AND_CUSTOM_AGENTS'
}
