import { NgModule } from '@angular/core';
import { CommonModule as AngularCommonModule } from '@angular/common';
import { ChatService } from './service/chat/chat.service';
import { ChatComponent } from './component/chat/chat.component';
import { SharedModule } from '../shared/shared.module';
import { ChatInitializationService } from './service/chat-initialization/chat-initialization.service';

@NgModule({
  imports: [
    AngularCommonModule, SharedModule
  ],
  providers: [ChatService, ChatInitializationService],
  declarations: [ChatComponent],
  exports: [ChatComponent]
})
export class ChatModule {
}
