[data-tooltip]::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 0;
  top: 70px;
  background: #36749c;
  color: #fff;
  padding: 0.3em;
  pointer-events: none;
  opacity: 0;
  transition: 0.5s;
  border-radius: 8px;
  font-size: initial;
}

[data-tooltip]:hover::after {
  opacity: 1;
}
.accept-button, .fa-remove, .edit-button, .fa-eye, .fa-eye-slash {
  float: right;
  margin-right: 5%;
  cursor: pointer;
  font-size: 18px;
  color: #a9a9a9;
}

.fa-remove {
  color: #e54c36;
}

.accept-button {
  color: #5cb85c;
}

.disabled-input {
  border: 0 !important;
  box-shadow: none !important;
  background-color: rgba(0, 0, 0, 0) !important;
  cursor: default !important;
}

.form-block {
  float: left;
  width: 100%;

  .editor {
    border: 0;
    outline: 0;
    background: transparent;
    width: 100%;

    &.active {
      border-bottom: 1px solid #b6cce3;
    }
  }

  .errors-block {
    float: left;
    font-size: 12px;
    color: #e54c36;
    width: 100%;
  }

  .richiedi {
    margin-top: 4px;
  }

  .select-block {
    float: left;
    width: 50%;

    select {
      background-color: #ffffff;
      border: 0;
      outline: 0;
      width: 100%;
      border-bottom: 1px solid #b6cce3;
    }
  }

  .flex-text-password-identification {
    display: flex;
    font-style: italic;
    align-items: center;
    font-size: 13px;
    width: 80%;

    .exclamation-point {
      width: 20px;
      height: 20px;
      margin-right: 10px;
    }
  }
}

.modifiable {
  width: 80%;
}

.accept-decline-block {
  width: 20%;
  float: right;
}

.accept-modal, .decline-modal {
  border: 1px solid #36749d;
  background-color: #ffffff;
  color: #36749d;
}

.decline-modal {
  border: 1px solid #e54c36;
  color: #e54c36;
}

.confirm-window {
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);

  .close {
    color: #36749d;
    opacity: 1;
  }

  .modal-dialog {
    margin-top: 10%;
  }

  .modal-footer {
    border-top: none;
  }

  .modal-header {
    border-bottom: none;
  }
}

.modal-window {
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);

  .close {
    color: #36749d;
    opacity: 1;
  }

  .modal-dialog {
    margin-top: 10%;
  }

  .modal-footer {
    border-top: 1px solid #b6cce3;
  }

  .modal-header {
    border-bottom: 1px solid #b6cce3;
  }

  .modal-body {
    color: #36749d;
  }
}

.checkbox-editor {
  border-bottom: 1px solid #b6cce3;
  cursor: pointer;
  float: left;
  width: 75%
}

.confirm-description {
  color: #e54c36;
  text-align: center;
}


@media only screen and (max-width: 991px) {
  .form-block {
    .select-block {
      width: 100%;
    }
  }
  .disabled-input {
    font-size: 12px;
  }
}

@media only screen and (max-width: 535px) {
  .accept-button, .fa-remove, .edit-button, .fa-eye, .fa-eye-slash {
    float: right;
    margin-right: 0;
    cursor: pointer;
    font-size: 12px;
  }
  .flex-text-password-identification {
    font-size: 12px !important;
  }
  .disabled-input {
    width: 100%;
    font-size: 12px;
  }
  .form-block {
    .errors-block {
      font-size: 7px;
    }
  }
}
