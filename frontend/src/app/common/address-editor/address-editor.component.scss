.accept-button, .fa-remove, .edit-button {
  float: right;
  margin-right: 5%;
  cursor: pointer;
  font-size: 18px;
  color: #a9a9a9;
}

.fa-remove {
  color: #e54c36;
}

.accept-button {
  color: #5cb85c;
}

.form-block {
  float: left;
  width: 75%;
  .select-block {
    float: left;
    width: 50%;
    select {
      background-color: #ffffff;
      border: 0;
      outline: 0;
      width: 100%;
      border-bottom: 1px solid #b6cce3;
    }
  }
}

.accept-decline-block {
  width: 25%;
  float: right;
}

.accept-modal, .decline-modal {
  border: 1px solid #36749d;
  background-color: #ffffff;
  color: #36749d;
}

.decline-modal {
  border: 1px solid #e54c36;
  color: #e54c36;
}

.checkbox-placeholder {

}

.form-input {
  width: 19%;
  float: left;
  margin-right: 1%;
}

.confirm-window {
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  .close {
    color: #36749d;
    opacity: 1;
  }
  .modal-dialog {
    margin-top: 10%;
  }
  .modal-footer {
    border-top: none;
  }
  .modal-header {
    border-bottom: none;
  }
}

.modal-window {
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  .close {
    color: #36749d;
    opacity: 1;
  }
  .modal-dialog {
    margin-top: 10%;
  }
  .modal-footer {
    border-top: 1px solid #b6cce3;
  }
  .modal-header {
    border-bottom: 1px solid #b6cce3;
  }
  .modal-body {
    color: #36749d;
  }
}

.checkbox-editor {
  border-bottom: 1px solid #b6cce3;
  cursor: pointer;
  float: left;
  width: 100%;
  margin-bottom: 2%;
}

.checkbox-label {
  display: inline;
}

.checkbox-item {
  margin-bottom: 1%;
}

@media only screen and (max-width: 991px) {
  .form-input {
    display: block;
    width: 100%;
    margin-right: 0;
    margin-bottom: 2%;
  }
}
