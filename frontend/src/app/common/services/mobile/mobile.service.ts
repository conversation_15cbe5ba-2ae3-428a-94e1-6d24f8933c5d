import {Injectable} from '@angular/core';
import {HttpClient, HttpParams} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import TariffDetailsRequest from '../../model/mobile/TariffDetailsRequest';
import TariffDetail from '../../model/mobile/TariffDetail';
import ProductRecordsRequest from '../../model/mobile/ProductRecordsRequest';
import {ProductRecord} from '../../model/mobile/product-record/ProductRecord';
import {ContractRecord} from '../../model/mobile/contract-record/ContractRecord';
import ActivateOptionRequest from '../../model/mobile/ActivateOptionRequest';
import {ActivateOptionResponse} from '../../model/mobile/ActivateOptionResponse';
import AdditionalProduct from '../../model/mobile/contract-record/AdditionalProduct';
import {ProductMapping} from '../../model/mobile/product-record/ProductMapping';
import {Product} from '../../model/mobile/product-record/Product';
import {TopUpSimWithVoucherCodeRequest} from '../../model/mobile/TopUpSimWithVoucherCodeRequest';
import MobileOperator from '../../model/mobile/MobileOperator';
import CheckContractResponse from '../../model/mobile/contract-record/CheckContractResponse';


@Injectable()
export class MobileService {

  constructor(private httpClient: HttpClient) {
  }

  checkPeriod(clientId, prId): Observable<CheckContractResponse> {
    return this.httpClient.get<CheckContractResponse>('/api/mobile/contrattiData/check', {
      params: new HttpParams().set('clientId', clientId).set('prId', prId)
    });
  }

  loadContractRecords(clientId): Observable<Array<ContractRecord>> {
    return this.httpClient.get<Array<ContractRecord>>('/api/mobile/contract/records', {
      params: new HttpParams().set('clientId', clientId)
    });
  }

  loadContractRecordsBySimNumber(msisdnId): Observable<Array<ContractRecord>> {
    return this.httpClient.get<Array<ContractRecord>>('/api/mobile/contract/records', {
      params: new HttpParams().set('simNumber', msisdnId).set('clientId', localStorage.getItem('clientId'))
    });
  }

  loadSimDetailRecord(msisdn) {
    return this.httpClient.get('/api/mobile/sim-detail/aggregation', {
      params: new HttpParams().set('msisdn', msisdn)
    });
  }

  loadSimBalance(msisdn) {
    return this.httpClient.get('/api/mobile/sim-detail/balance', {
      params: new HttpParams().set('msisdn', msisdn)
    });
  }

  loadProductDescription(productId) {
    return this.httpClient.get('/api/mobile/product/description', {
      params: new HttpParams().set('productId', productId)
    });
  }

  loadTariffDetails(request: TariffDetailsRequest): Observable<Array<TariffDetail>> {
    return this.httpClient.post<Array<TariffDetail>>('/api/mobile/traffic/details', request);
  }

  loadProductRecords(request: ProductRecordsRequest): Observable<Array<ProductRecord>> {
    return this.httpClient.post<Array<ProductRecord>>(`/api/mobile/product/records/${localStorage.getItem('clientId')}`, request);
  }

  loadProductRecordsOffers(request: ProductRecordsRequest): Observable<Array<Product>> {
    return this.httpClient.post<Array<Product>>('/api/mobile/product/records/offers', request);
  }

  activateOption(request: ActivateOptionRequest): Observable<ActivateOptionResponse> {
    return this.httpClient.post<ActivateOptionResponse>('/api/mobile/activate/option', request);
  }

  changeTariff(request: ActivateOptionRequest): Observable<ActivateOptionResponse> {
    return this.httpClient.post<ActivateOptionResponse>('/api/mobile/change/tariff', request);
  }

  loadLastRechargeData(simNumber: string): Observable<AdditionalProduct> {
    return this.httpClient.get<AdditionalProduct>('/api/mobile/last/recharge', {
      params: new HttpParams().set('simNumber', simNumber)
    });
  }

  changeTariffPlan(newOptionId: number, selectedRecord: ContractRecord): Observable<ActivateOptionResponse> {
    const activateOptionRequest = new ActivateOptionRequest();
    activateOptionRequest.newOptionId = newOptionId;
    activateOptionRequest.customerId = localStorage.getItem('clientId');
    activateOptionRequest.simNumber = selectedRecord.msisdnId;
    return this.changeTariff(activateOptionRequest);
  }

  loadProductRecordsByProductType(contractRecord: ContractRecord, productTypeId: number): Observable<Array<ProductRecord>> {
    if (!contractRecord || !productTypeId) {
      return;
    }
    const request = new ProductRecordsRequest();
    request.subscriptionId = contractRecord.id;
    if (contractRecord.mainProduct) {
      request.tariffPlanId = contractRecord.mainProduct.tariffPlanId;
      request.productTypeId = productTypeId;
    }
    return this.loadProductRecords(request);
  }

  loadProductRecordsOffersByProductType(contractRecord: ContractRecord, productTypeId: number): Observable<Array<Product>> {
    if (!contractRecord || !productTypeId) {
      return;
    }
    const request = new ProductRecordsRequest();
    request.subscriptionId = contractRecord.id;
    if (contractRecord.mainProduct) {
      request.tariffPlanId = contractRecord.mainProduct.tariffPlanId;
      request.productTypeId = productTypeId;
    }
    return this.loadProductRecordsOffers(request);
  }

  topUpSimWithVoucherCode(request: TopUpSimWithVoucherCodeRequest): Observable<any> {
    return this.httpClient.post<any>('/api/mobile/ricarica/voucher', request);
  }

  getMobileOperators(): Observable<Array<MobileOperator>> {
    return this.httpClient.get<Array<MobileOperator>>(`/api/mobile/operators/${localStorage.getItem('clientId')}`);
  }

  activateNewOption(newOptionId: number, selectedRecord: ContractRecord): Observable<ActivateOptionResponse> {
    if (newOptionId && selectedRecord) {
      const activateOptionRequest = new ActivateOptionRequest();
      activateOptionRequest.newOptionId = newOptionId;
      activateOptionRequest.customerId = localStorage.getItem('clientId');
      activateOptionRequest.simNumber = selectedRecord.msisdnId;
      return this.activateOption(activateOptionRequest);
    }
  }

  getYourOffers(contractRecord: ContractRecord): Array<ProductMapping> {
    const yourProductNameArray = [];
    if (contractRecord.additionalProducts && contractRecord.additionalProducts.length > 0) {
      contractRecord.additionalProducts.forEach((element) => {
        if (element.product.productMapping != null && element.product.productMapping.idTipoProdotto === 2) {
          yourProductNameArray.push(element.product);
        }
      });
      return yourProductNameArray;
    }
    yourProductNameArray.push(contractRecord.mainProduct);
    return yourProductNameArray;

  }

  getYourOptions(contractRecord: ContractRecord): Array<ProductMapping> {
    const addProductNameArray = [];
    if (contractRecord.additionalProducts && contractRecord.additionalProducts.length > 0) {
      contractRecord.additionalProducts.forEach((element) => {
        if (element.active === true && element.product.renewalPeriod == null) {
          // if (element.product.productMapping != null && element.product.productMapping.idTipoProdotto === 3) {
          addProductNameArray.push(element.product);
        }
      });
    }
    return addProductNameArray;
  }

}
