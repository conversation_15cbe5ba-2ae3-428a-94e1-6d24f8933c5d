import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import {AutoLetturaForm} from '../../../routes/autolettura/model/AutoLetturaForm';
import {IncidentEventUtils} from '../../../routes/autolettura/utils/enums/IncidentEventUtils';
import {ServiceType} from '../../../routes/autolettura/utils/enums/ServiceType';
import {IncidentCategory} from '../../enum/IncidentCategory';
import {IncidentEvent} from '../../enum/IncidentEvent';
import {IncidentEventResponse} from '../../../routes/autolettura/model/IncidentEventResponse';
import {IncidentEvent as IncidentEventRequest} from '../../model/incidentEvent/IncidentEvent';
import {ServiceResponseStatus} from '../../enum/ServiceResponseStatus';
import {NotificationService} from '../notification/notification.service';
import {IncidentEventCategory} from '../../enum/IncidentEventCategory';
import {ChangeSilttamentoProssimaRequest} from '../../model/offers/ChangeSilttamentoProssimaRequest';

const activateNewProductAnnotation =
  'Il cliente richiede da selfcare ricontatto per attivazione nuovi prodotti. Inviata mail a gestioneapt per fissare app.to con sale.';
const remodulationProductAnnotation = 'Il cliente richiede da selfcare ricontatto per aggiunta utenze e servizi.';
const remodulationProfileAnnotation = 'Il cliente richiede rimodulazione da selfcare, contattare il cliente per proporre rimodulazione coerente con i suoi consumi';

@Injectable()
export class IncidentEventService {

  constructor(private http: HttpClient, private notificationService: NotificationService) {
  }

  incidentEvent(request: IncidentEventRequest): Observable<any> {
    return this.http.post('/api/incident-event', request);
  }

  getUserServices(clientId): Observable<any> {
    return this.http.get(`/api/services/${clientId}`);
  }

  getActiveServices(data) {
    const result = {};
    data.forEach((value) => {
      const activeUtilities = value.utilities.filter((utility) => utility.status === 'ATTIVATO');
      if (activeUtilities.length > 0) {
        result[value.serviceName] = {'serviceName': value.serviceName, utilities: activeUtilities};
      }
    });
    return result;
  }

  prepareDataForIncidentEventRequest(formData: AutoLetturaForm): IncidentEventRequest {
    if (formData) {
      return {
        customerId: localStorage.getItem('clientId'),
        incidentEvent: IncidentEvent.AUTOLETTURA,
        incidentCategory: `Inserimento autolettura ${IncidentCategory[formData.type]}`,
        serviceType: ServiceType[formData.type],
        incidentAnnotation: IncidentEventUtils.buildIncidentAnnotation(formData),
        origin: 200007,
        genericAttributes: [
          {
            key: IncidentEventUtils.podOrPDR(formData.pdr),
            value: formData.pdr
          }
        ]
      } as IncidentEventRequest;
    }
    return null;
  }

  remodulationProductRequest(serviceType: number = 0): Observable<IncidentEventResponse> {
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: IncidentEvent.INFORMAZIONE_COMMERCIALE,
      incidentCategory: IncidentCategory.RIMODULAZIONE_OFFERTA,
      serviceType: serviceType,
      incidentAnnotation: remodulationProductAnnotation,
      origin: 200007,
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }

  remodulationProfileRequest(serviceType: number = 0, idFattura: string): Observable<IncidentEventResponse> {
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: IncidentEvent.RIMODULAZIONE_OFFERTA_SELFCARE,
      incidentCategory: IncidentCategory.TAGLIA,
      serviceType: serviceType,
      incidentAnnotation: remodulationProfileAnnotation + ' per id fatturazione ' + idFattura,
      origin: 200007,
      genericAttributes: [{
        key: 'idFatt',
        value: idFattura
      }]
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }

  balancePaymentRequest(serviceType: number = 0, idFattura: string, pagaBalance: number): Observable<IncidentEventResponse> {
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: IncidentEvent.VARIAZIONE_FATTURAZIONE,
      incidentCategory: IncidentCategory.PAGAMENTO_SALDO_NEGATIVO,
      serviceType: serviceType,
      incidentAnnotation: `Il cliente richiede pagamento saldo CR negativo esposto in fattura di ${pagaBalance} euro su idFatt ${idFattura}`,
      origin: 200007,
      genericAttributes: null
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }

  disattivazioneAmazonPrimeRequest(serviceType: number = 0): Observable<IncidentEventResponse> {
    const disattivaDate = new Date();
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: IncidentEvent.DISATTIVAZIONE_AMAZON_PRIME,
      incidentCategory: IncidentCategory.DISATTIVA,
      serviceType: serviceType,
      incidentAnnotation: 'Inoltrata Richiesta disattivazione Amazon Prime da Selfcare in data '
        + disattivaDate.getDate() + '-' + disattivaDate.getMonth() + '-' + disattivaDate.getFullYear(),
      origin: 200007,
      genericAttributes: null
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }

  activateNewProductRequest(serviceType: number = 0): Observable<IncidentEventResponse> {
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: IncidentEvent.INFORMAZIONE_COMMERCIALE,
      incidentCategory: IncidentCategory.ATTIVAZIONE_NUOVI_PRODOTTI,
      serviceType: serviceType,
      incidentAnnotation: activateNewProductAnnotation,
      origin: 200007,
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }

  commercialInformationIncidentEvent(clientId: string | number, incidentCategory,
                                     incidentAnnotation: string): Observable<IncidentEventResponse> {
    if (!clientId || !incidentCategory || !incidentAnnotation) {
      return Observable.throw('All parameters are required');
    }
    const request = {
      customerId: clientId,
      incidentEvent: IncidentEvent.INFORMAZIONE_COMMERCIALE,
      incidentCategory,
      serviceType: 0,
      incidentAnnotation,
      origin: 200007,
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }

  createIncidentEventResponseNotification(response: IncidentEventResponse, successMessage: string, errorMessage?: string) {
    if (response.status === ServiceResponseStatus.OK) {
      this.notificationService.successMessage(successMessage);
    } else {
      this.notificationService.errorMessage(errorMessage ? errorMessage : response.message);
    }
  }

  variazionePromoMese(unModifiedMonthNames: string, modifiedMonthNames: string) {
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: IncidentEvent.VARIAZIONE_PROMO3MESI,
      incidentCategory: IncidentEventCategory.PROMOMESI3,
      serviceType: 6,
      incidentAnnotation: `Inserita da cliente richiesta di modifica data emissione sconto mese off ${unModifiedMonthNames}.` +
        ` Nuovo mese di emissione ${modifiedMonthNames}`,
      origin: 200007
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }

  pagamentoFatturaFlessibile(changeSilttamentoProssimaRequest: ChangeSilttamentoProssimaRequest) {
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: IncidentEvent.PAGAMENTO_FATTURA_FLESSIBLE,
      incidentCategory: IncidentEventCategory.MODIFICA_SCANDEZA_FATTURA,
      serviceType: 6,
      incidentAnnotation: `Il cliente richiede slittamento prossima fattura per IdFatt |${changeSilttamentoProssimaRequest.idFatt}|`,
      origin: 200007
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }

  createIncidentEventMNP(msisdnId: number) {
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: IncidentEvent.MNP_IN_POST_ATTIVAZIONE,
      incidentCategory: IncidentEventCategory.POST_ATTIVAZIONE,
      serviceType: 10,
      incidentAnnotation: `Inserita da cliente richiesta di MNP-IN Post Attivazione per la SIM ${msisdnId}.` +
        ` Procedere ad applicare la variazione.`,
      origin: 200007
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }

    recontactRequest(incidentAnnotation: string) {
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: IncidentEvent.RECONTACT_REQUEST,
      incidentCategory: IncidentEventCategory.DASH_BUTTON,
      serviceType: 0,
      incidentAnnotation,
      origin: 200007,
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }

  openIncidentEventForContractCopy(numberContract: number) {
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: IncidentEvent.CONTRACT_REQUEST,
      incidentCategory: IncidentCategory.CONTRACT_COPY,
      serviceType: 6,
      incidentAnnotation: `Il cliente richiede da selfcare copia del contratto N° ${numberContract}`,
      origin: 200007,
      genericAttributes: null
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }

  openIncidentEventForAllegaPagamento() {
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: IncidentEvent.ALLEGO_PAGAMENTO,
      incidentCategory: IncidentCategory.ALLEGO_PAGAMENTO,
      serviceType: 6,
      incidentAnnotation: '',
      origin: 200007,
      genericAttributes: null
     } as IncidentEventRequest;
     return this.incidentEvent(request);
  }

  openIncidentEventForCrossSelling(service: string, codiceOfferta: string) {
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: IncidentEvent.CROSS_SELLING,
      incidentCategory: IncidentCategory.CROSS_SELLING,
      serviceType: 6,
      incidentAnnotation: `Servizio - ${service}, codice Offerta - ${codiceOfferta}`,
      origin: 200007,
      genericAttributes: null
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }

  openIncidentEventForRedeemVoucher(voucherCode: string, typeOfVoucher: string, voucherAmount: number) {
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: typeOfVoucher === 'ENERGY' ? IncidentEvent.REDEEM_ENERGY_VOUCHER : IncidentEvent.REDEEM_MOBILE_VOUCHER,
      incidentCategory: typeOfVoucher === 'ENERGY' ? IncidentCategory.REDEEM_ENERGY_VOUCHER : IncidentCategory.REDEEM_MOBILE_VOUCHER,
      serviceType: 6,
      incidentAnnotation: `Card n. ${voucherCode}, valore del voucher: ${voucherAmount} €`,
      origin: 200007,
      genericAttributes: null
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }

  openIncidentEventoForActivate5GOffer(subscriptionId: string, codiceOfferta: string,
                                       costo: number): Observable<IncidentEventResponse> {
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: IncidentEvent.OFFER_5G,
      incidentCategory: IncidentCategory.OFFER_5G,
      serviceType: 10,
      incidentAnnotation: '',
      origin: 200017,
      genericAttributes: [
        {
          key: 'SUBID',
          value: subscriptionId
        },
        {
          key: 'CodOff',
          value: codiceOfferta
        },
        {
          key: 'costo',
          value: costo.toString()
        }
      ]
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }

  openIncidentForDeactivate5G(subscriptionId: string, codiceOfferta: string,
                                       costo: number, answer: string): Observable<IncidentEventResponse> {
    const request = {
      customerId: localStorage.getItem('clientId'),
      incidentEvent: IncidentEvent.Deactivation_5G,
      incidentCategory: IncidentCategory.OFFER_5G,
      serviceType: 10,
      incidentAnnotation: `Perche hai deciso di disattivare il 5g? -> ${answer}`,
      origin: 200017,
      genericAttributes: [
        {
          key: 'SUBID',
          value: subscriptionId
        },
        {
          key: 'CodOff',
          value: codiceOfferta
        },
        {
          key: 'costo',
          value: costo.toString()
        }
      ]
    } as IncidentEventRequest;
    return this.incidentEvent(request);
  }
}
