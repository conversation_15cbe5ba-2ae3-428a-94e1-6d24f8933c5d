import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import {MnpActivation, MnpUploadFileResponse} from '../../model/mnp/mnp.model';

@Injectable()
export class MnpService {

  readonly url = 'api/mnp';
  readonly urlUploadFile = 'api/mnp/upload-file';

  constructor(private http: HttpClient) {}

  public postMnpActivation(body: MnpActivation): Observable<any> {
    return this.http.post<any>(this.url, body);
  }

  public mnpUploadFile(body: FormData): Observable<MnpUploadFileResponse> {
    return this.http.post<MnpUploadFileResponse>(this.urlUploadFile, body);
  }

}
