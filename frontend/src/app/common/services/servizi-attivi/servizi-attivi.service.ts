import { Injectable } from '@angular/core';
import { MobileRecordStatus } from '../../enum/MobileRecordStatus';



@Injectable()
export class ServiziAttiviService {
  name = {
    'ENERGIA': 'POD',
    'ELETTRICITA': 'POD',
    'GAS': 'PDR',
    'WLR': 'LINEA',
    'VOCE': 'LINEA',
    'VOIP': 'LINEA',
    'ADSL': 'LINEA',
    'ADSL\HDSL': 'LINEA',
    'MOBILE': 'SIM',
    'FISSO': 'LINEA',
    'LUCE': 'POD',
    'FIBRA': 'LINEA',
  };

  map = {
    ACTIVE: 'ATTIVATO',
    DEACTIVATED: 'DISATTIVATO',
    HARDSUSPENSION: 'SOSPESO',
    INITIACTIVE: 'ATTIVATO',
    LOSTSIM: 'SOSPESO',
    PORTEDOUT: 'DISATTIVATO',
    'PORTED OUT': 'DISATTIVATO',
    SOFTSUSPENSION: 'SOSPESO',
    MOB_INVIATO_LOGISTICA: 'SPEDIZIONE IN CORSO',
    MO<PERSON>_SIM_NON_CONSEGNATA: 'SPEDIZIONE IN CORSO',
    MOB_NUOVA: 'In attivazione',
    IN_ATTESA_DI_RISPOSTA: 'IN ATTIVAZIONE',
    MOB_RICHIESTA_FALLITA: 'IN ATTIVAZIONE',
    MOB_DA_INVIARE_LOGISTICA: 'SPEDIZIONE IN CORSO',
    CANCELLATA: 'DISATTIVATO',
    [MobileRecordStatus.LOST_SIM.toUpperCase()]: 'SOSPESA',
    [MobileRecordStatus.HARD_SUSPENSION.toUpperCase()]: 'SOSPESA',
    [MobileRecordStatus.SOFT_SUSPENSION.toUpperCase()]: 'SOSPESA'
  };

  mobileStatusDecode(status: string) {
    if (status && this.map[status.trim().toUpperCase()]) {
      return this.map[status.trim().toUpperCase()];
    }
    return '-';
  }

  getNumberName(name) {
    return this.name[name.toUpperCase()];
  }

  constructor() {
  }

}
