import { Injectable } from '@angular/core';
import {InfoPod2G, PodDetail} from '../../model/services/PodDetail';
import { HttpClient } from '@angular/common/http';
import PodDetailsRequest from '../../../common/model/services/PodDetailsRequest';
import { Observable } from 'rxjs/Observable';
import { UserServices, Utility } from '../../model/services/userServices.model';
import { PodRequest } from '../../model/services/PodDetailsRequest';
import {EnergyDetails, EnergyPointAdjustment} from '../../model/energy/EnergyPointAdjustment';
import * as moment from 'moment';
import {Subject} from 'rxjs/Subject';

@Injectable()
export class EnergiaService {
  private static subjectMonth = new Subject<any>();
  private static subjectDays = new Subject<any>();

  static getClickEventFromMonthsChart(): Observable<any> {
    return this.subjectMonth.asObservable();
  }

  static sendClickEventFromMonthsChart(month: any) {
    this.subjectMonth.next(month);
  }

  static getClickEventFromDaysChart(): Observable<any> {
    return this.subjectDays.asObservable();
  }

  static sendClickEventFromDaysChart(day: any) {
    this.subjectDays.next(day);
  }

  constructor(private httpClient: HttpClient) {
  }

  loadPodDetails(request: PodDetailsRequest): Observable<Array<PodDetail>> {
    return this.httpClient.post<Array<PodDetail>>('/api/electricity/pod/details', request);
  }

  load2GPodDetails(request: any[]): Observable<Array<InfoPod2G>> {
    return this.httpClient.post<Array<InfoPod2G>>('/api/electricity/pod/2g-details', request);
  }

  loadLucePodDetails(utilities: Array<Utility>): Observable<Array<PodDetail>> {
    if (utilities && utilities.length > 0) {
      const request = new PodDetailsRequest();
      request.mostraStoria = true;
      request.podRequests = utilities.map(utility => ({
        clientId: localStorage.getItem('clientId'),
        pod: utility.utNumber as string
      })) as PodRequest[];
      return this.loadPodDetails(request);
    }
    return Observable.empty();
  }

  loadPodDetailsByUserService(service: UserServices): Observable<Array<PodDetail>> {
    if (service) {
      return this.loadLucePodDetails(service.utilities);
    }
    return Observable.empty();
  }

  energyPointAdjustments(clientId: string, punto: number): Observable<Array<EnergyPointAdjustment>> {
    return this.httpClient.get<Array<EnergyPointAdjustment>>(`/api/electricity/point/adjustments?clientId=${clientId}&punto=${punto}`)
      .map(response => {
        response.forEach(item => item.dataLettura = moment(`${item.mese}-${item.anno}`, 'MM-YYYY').toDate());
        return response;
      });
  }

  energyDetailsByHours(clientId: string, pod: string, startDay: string, endDay: string): Observable<Array<EnergyDetails>> {
    return this.httpClient.post<any>(`/api/electricity/detailsByHours`, {'idConsumi': [clientId], 'utenza': pod, 'dataDa': startDay, 'dataA': endDay});
  }

  getExcelFile(clientId: string, pod: string, startDay: string, endDay: string): Observable<Blob> {
    return this.httpClient.post(`/api/electricity/excel`, {'idConsumi': [clientId], 'utenza': pod, 'dataDa': startDay, 'dataA': endDay}, {responseType: 'blob'});
  }
}
