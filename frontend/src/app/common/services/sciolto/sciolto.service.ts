import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';

@Injectable()
export class ScioltoService {

  constructor(private http: HttpClient) {
  }

  public getGeneralScioltoInformation() {
    return this.http.get<any>(`/api/sciolto/general/information/${localStorage.getItem('clientId')}`);
  }

  public getScioltoInformation(offer: string, userCluster: string): Observable<any> {
    return this.http.get<any>(`/api/sciolto/information/${offer}/${userCluster}`);
  }
}
