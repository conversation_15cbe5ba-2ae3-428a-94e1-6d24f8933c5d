import {CUSTOM_ELEMENTS_SCHEMA, ModuleWithProviders, NgModule} from '@angular/core';
import {CommonModule as AngularCommon} from '@angular/common';
import {MatButtonModule, MatMenuModule, MatTableModule} from '@angular/material';

import {SpinnerComponent} from './components/spinner/spinner.component';
import {SpinnerAction} from '../redux/spinner/actions';
import {HTTP_INTERCEPTORS, HttpClientModule} from '@angular/common/http';
import {EMailComponent} from './components/e-mail/e-mail.component';
import {ModalModule} from 'ngx-bootstrap';

import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {EmailService} from './services/email/email.service';
import {OptimaIconUtils} from './utils/OptimaIconUtils';
import {NotificationService} from './services/notification/notification.service';
import {ActiveServicesComponent} from './components/active-services/active-services.component';
import {ActiveServicesMobileComponent} from './components/active-services-mobile-v/active-services-mobile-v.component';
import {NotificationComponent} from './components/notification/notification.component';
import {ToasterModule, ToasterService} from 'angular2-toaster';
import {AbsPipe} from './pipes/AbsPipe';
import {RouterModule} from '@angular/router';
import {NgVarDirective} from './directives/ng-var';
import {KeysPipe} from './pipes/keys-pipe/keys.pipe';
import {ContractService} from './services/contracts/userContract.service';
import {OffersService} from './services/offers/offers.service';
import {UserDataService} from './services/user-data/userData.service';
import {EnergiaService} from './services/energia/energia-service.service';
import {DotNetDatePipe} from './pipes/dot-net-date/dot-net-date.pipe';
import {AuthHttpInterceptor} from '../services/auth/auth-http.interceptor';
import {GasService} from './services/gas/gas.service';
import {FissoService} from './services/fisso/fisso.service';
import {FibraService} from './services/fibra/fibra.service';

import {ChartsModule} from 'ng2-charts';
import {SafeHtmlPipe} from '../routes/mobile/utils/pipes/mobile.html.pipe';

import {UserServicesService} from '../routes/profilePage/userServices/userServices.service';
import {ConfigurazioneRouterService} from './services/configurazione-router/configurazione-router.service';
import {MobileLayoutComponent} from '../routes/home/<USER>/mobile-layout/mobile-layout.component';
import {LuceLayoutComponent} from '../routes/home/<USER>/luce-layout/luce-layout.component';
import {GasLayoutComponent} from '../routes/home/<USER>/gas-layout/gas-layout.component';
import {FibraLayoutComponent} from '../routes/home/<USER>/fibra-layout/fibra-layout.component';
import {FissoLayoutComponent} from '../routes/home/<USER>/fisso-layout/fisso-layout.component';
import {NgxEditorModule} from 'ngx-editor';
import {PdfService} from './services/pdf/pdf.service';
import {CommunicationService} from './services/comunicazioni/comunicazioni.service';
import {FormInputComponent} from './components/form-input/form-input.component';
import {ModalComponent} from './components/modal/modal.component';
import {ChartComponent} from './components/chart/chart.component';
import {InfoComponent} from './components/info/info.component';

import {ServiceCardComponent} from './components/service-card/service-card.component';
import {ServiziAttiviRouteService} from './services/servizi-attivi-router/servizi-attivi-route.service';
import {ServiziAttiviService} from './services/servizi-attivi/servizi-attivi.service';
import {InvoiceService} from '../routes/invoices/invoice.service';
import {IncidentEventService} from './services/incedentEvent/incident-event.service';
import {UserDataEditorComponent} from './user-data-editor/user-data-editor.component';
import {AddressEditorComponent} from './address-editor/address-editor.component';
import {MobileService} from './services/mobile/mobile.service';
import {NumberFormatPipe} from './pipes/number-pipe/number.pipe';
import {ConfirmWindowComponent} from './components/confirm-window/confirm-window.component';
import {InputComponent} from './components/input/input.component';
import {SelectComponent} from './components/select/select.component';

import {PaginatorComponent} from './components/paginator/paginator.component';
import {ModalWindowComponent} from './components/modal-window/modal-window.component';
import {ModalWrapperComponent} from './components/modal-wrapper/modal-wrapper.component';
import {NotificaMdpComponent} from './components/notifica-mdp/notifica-mdp.component';
import {DialogModalWindowComponent} from './components/dialog-modal-window/dialog-modal-window.component';
import {DialogModalWrapperComponent} from './components/dialog-modal-wrapper/dialog-modal-wrapper.component';
import {OtpService} from './services/otp/otp.service';
import {AmazonPrimeService} from './services/amazonprime/amazon-prime.service';
import {MonthModalWrapperComponent} from './components/month-modal-wrapper/month-modal-wrapper.component';
import {MonthModalWindowComponent} from './components/month-modal-window/month-modal-window.component';
import {PagamentoFlessibileService} from './services/pagamento-flessibile/pagamento-flessibile.service';
import {ClientNotificationService} from './services/client-notification/client-notification.service';
import {EtichettaLabelService} from './services/etichetta-label/etichetta-label.service';
import {ServiziAggiuntniviComponent} from '../routes/home/<USER>/servizi-aggiuntnivi-layout/servizi-aggiuntnivi.component';
import {ScioltoGasComponent} from '../routes/home/<USER>/sciolto-gas/sciolto-gas.component';
import {ScioltoService} from './services/sciolto/sciolto.service';
import {ScioltoLuceComponent} from '../routes/home/<USER>/sciolto-luce/sciolto-luce.component';
import {ScioltoFibraComponent} from '../routes/home/<USER>/sciolto-fibra/sciolto-fibra.component';
import {ScioltoMobileComponent} from '../routes/home/<USER>/sciolto-mobile/sciolto-mobile.component';
import {ScioltoTeleconsultoMedicoComponent} from '../routes/home/<USER>/sciolto-teleconsulto-medico/sciolto-teleconsulto-medico.component';
import {ScioltoFissoComponent} from '../routes/home/<USER>/sciolto-fisso/sciolto-fisso.component';
import {VoucherCardService} from './services/voucher-card/voucher-card.service';
import { ErrorWindowComponent } from './components/error-window/error-window.component';

@NgModule({
  imports: [
    ChartsModule,
    HttpClientModule,
    AngularCommon,
    MatTableModule,
    MatMenuModule,
    ModalModule.forRoot(),
    NgxEditorModule,
    FormsModule,
    ReactiveFormsModule,
    ToasterModule,
    RouterModule,
    MatButtonModule
  ],
  providers: [
    UserServicesService,
    SpinnerAction,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthHttpInterceptor,
      multi: true
    },
    EmailService, OptimaIconUtils, NotificationService,
    FibraService, CommunicationService, ConfigurazioneRouterService,
    ToasterService, ContractService, OffersService, UserDataService,
    EnergiaService, GasService, FissoService, PdfService, InvoiceService, ScioltoService,
    ServiziAttiviRouteService, ServiziAttiviService, IncidentEventService, MobileService, OtpService, AmazonPrimeService,
    PagamentoFlessibileService, ClientNotificationService, EtichettaLabelService, VoucherCardService
  ],
  declarations: [
    InputComponent,
    SelectComponent,
    SafeHtmlPipe,
    SpinnerComponent,
    EMailComponent,
    ActiveServicesComponent,
    ActiveServicesMobileComponent,
    MobileLayoutComponent,
    NotificationComponent,
    LuceLayoutComponent,
    GasLayoutComponent,
    FibraLayoutComponent,
    FissoLayoutComponent,
    ServiziAggiuntniviComponent,
    ScioltoLuceComponent,
    ScioltoGasComponent,
    ScioltoFibraComponent,
    ScioltoMobileComponent,
    ScioltoTeleconsultoMedicoComponent,
    ScioltoFissoComponent,
    AbsPipe,
    NgVarDirective,
    KeysPipe,
    DotNetDatePipe,
    DotNetDatePipe,
    FormInputComponent,
    ModalComponent,
    ChartComponent,
    InfoComponent,
    ServiceCardComponent,
    UserDataEditorComponent,
    AddressEditorComponent,
    NumberFormatPipe,
    ConfirmWindowComponent,
    PaginatorComponent,
    ModalWindowComponent,
    ModalWrapperComponent,
    NotificaMdpComponent,
    DialogModalWindowComponent,
    DialogModalWrapperComponent,
    MonthModalWrapperComponent,
    MonthModalWindowComponent,
    ErrorWindowComponent
  ],
    exports: [
        InputComponent,
        SelectComponent,
        SafeHtmlPipe,
        SpinnerComponent,
        EMailComponent,
        ActiveServicesComponent,
        ActiveServicesMobileComponent,
        MobileLayoutComponent,
        LuceLayoutComponent,
        GasLayoutComponent,
        FibraLayoutComponent,
        FissoLayoutComponent,
        ServiziAggiuntniviComponent,
        ScioltoLuceComponent,
        ScioltoGasComponent,
        ScioltoFibraComponent,
        ScioltoMobileComponent,
        ScioltoTeleconsultoMedicoComponent,
        ScioltoFissoComponent,
        NotificationComponent,
        FormInputComponent,
        AbsPipe,
        NgVarDirective,
        KeysPipe,
        DotNetDatePipe,
        ModalComponent,
        ChartComponent,
        ServiceCardComponent,
        UserDataEditorComponent,
        AddressEditorComponent,
        InfoComponent,
        NotificaMdpComponent,
        NumberFormatPipe,
        ConfirmWindowComponent,
        PaginatorComponent,
        ModalWindowComponent,
        ModalWrapperComponent,
        DialogModalWrapperComponent,
        DialogModalWindowComponent,
        MonthModalWrapperComponent,
        MonthModalWindowComponent,
        ErrorWindowComponent
    ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CommonModule {
  static forRoot(): ModuleWithProviders {
    return {
      ngModule: CommonModule,
      providers: [NumberFormatPipe]
    };
  }
}
