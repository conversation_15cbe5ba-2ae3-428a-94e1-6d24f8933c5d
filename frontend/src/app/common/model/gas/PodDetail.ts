export interface PodDetail {
  misuratore: string;
  numeroPdr: string;
  tipologiaPdr: string;
  inizioValidita: string;
  fineValidita?: null;
  inattivabile?: null;
  motivazioneScartoAttivazione?: null;
  disdettaInviata: boolean;
  dataInvioDisdetta: string;
  dataInizioPrelievoPrevista: string;
  dataEsportazione: string;
  matricolaCorrettore?: null;
  correttoreVolumetrico: boolean;
  matricolaContatore: string;
  descrizioneRemi: string;
  cifreContatore?: null;
  cifreCorrettore?: null;
  classeContatore?: null;
  tipoContatore: string;
  progConsumoAnnuo?: null;
  letturaSwitching?: null;
  pressioneFornitura?: null;
  pressioneFornituraDesc?: null;
  idcontratto: number;
  consumoflat: string;
  idPuntoGaso: string;
  idTipologiaPDR: string;
  idMotivazione: string;
  descMotivazione: string;
  idUsoEscProm?: null;
  descUsoEscProm?: null;
  percEsclusione?: null;
  percUsoEscProm?: null;
  lettura?: null;
  statoOperazione: string;
  classePdr: string;
  classeCont?: null;
  tipoPdrDescrizione: string;
  descrizioneContatore: string;
  matricolacontatareNChange: string;
  punto: string;
}

