import {IncidentEventCategory} from '../enum/IncidentEventCategory';

export class ChangePersonalDataRequest {
  clientId: string;
  incidentEventCategory: IncidentEventCategory;
  oldValue: string;
  value: string;
  addressToChange: string;
  newAddress: string;
}

export class ChangePasswordIdentificationRequest {
  customerId: string;
  change: Password;
}

export class Password {
  newPassword: string;

  constructor(newPassword: string) {
    this.newPassword = newPassword;
  }
}
