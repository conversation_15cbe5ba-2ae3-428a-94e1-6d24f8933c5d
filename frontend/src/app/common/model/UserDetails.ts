export interface UserDetails {
  id: number;
  consumer: boolean;
  crmGuiId: string;
  indirizzoEmail: NumeriRiferimentoEntityOrIndirizzoEmail;
  referenceNumber?: (NumeriRiferimentoEntityOrIndirizzoEmail)[] | null;
  name: string;
  sName: string;
  partitaIva: string;
  codiceFiscale: string;
  sedeLegale?: null;
  billingAddress: IndirizzoFatturazione;
  billingType: string;
  tipologiaContratto: string;
  tipoCliente: string;
}
export interface NumeriRiferimentoEntityOrIndirizzoEmail {
  value: string;
  key: string;
}

export interface IndirizzoFatturazione {
  id?: null;
  descrizione: string;
  tipoIndirizzo: number;
  cap: string;
  comune: Comune;
}
export interface Comune {
  value: string;
  key?: null;
}
