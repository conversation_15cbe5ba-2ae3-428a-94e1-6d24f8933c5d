import { ProductMapping } from './ProductMapping';
import { ProductOptionsEntity } from './ProductOptionsEntity';


export interface ProductRecord {
  activationPrice: number;
  allowedAsBonus: boolean;
  createdOn: string;
  id: number;
  name: string;
  parentId: number;
  productMapping: ProductMapping;
  productOptions?: (ProductOptionsEntity)[] | null;
  renewalPeriod?: null;
  renewalPrice: number;
  tariffPlan?: null;
  tariffPlanId?: null;
  validityPeriod: number;
  productIncompatibilities?: null;
}




