import { Component, Input } from '@angular/core';
import { Toast, ToasterConfig, ToasterService } from 'angular2-toaster';
import { select } from '@angular-redux/store';

@Component({
  selector: 'app-notification-component',
  templateUrl: './notification.component.html',
  styleUrls: ['./notification.component.scss']
})
export class NotificationComponent {

  toaster: any;
  toasterConfig: ToasterConfig = new ToasterConfig({
    positionClass: 'toast-bottom-right',
    showCloseButton: true
  });

  constructor(public toasterService: ToasterService) {

    this.toaster = {
      type: 'success',
      title: 'Title',
      text: 'Message'
    };

  }

  @Input()
  @select(['notification', 'toast'])
  set toast(toast: Toast) {
    if (toast) {
      this.pop(toast);
    }
  }

  pop(toast: Toast) {
    this.toasterService.pop(toast);
  }

}
