@import "~app/shared/styles/colors";

.main {
  border: 1px solid #b6cce3;
  border-radius: 10px;
  background-color: #f0f5f9;
  float: left;
  width: 100%;
}
.passa-tuto-title{
  padding-top: 2%;
  color: #e54c36;
  text-decoration: underline;
  font-size: 16px;
}
.top {
  background-color: white;
  border-radius: 10px 10px 0 0;
  border-bottom: 1px solid #b6cce3;
  padding: 5px;
  float: left;
  width: 100%;
}

.recontact-button-desktop {
  width: 100%;
  height: 91px;
  display: inline-block;
  margin-top: 2%;
  background: url("../../../../assets/img/assistant/dashbutton.png") no-repeat center;
  background-size: contain;
}
ul {
  list-style-type: none;
  float: left;
  width: 100%;
  padding-left: 20px;
  padding-right: 20px;
  li {
    border: 1px solid #b6cce3;
    border-radius: 10px;
    margin-top: 10px;
    padding: 0;
    text-align: left;
    background-color: white;
    color: #36749d;
    font-size: 18px;
    .disableText {
      margin: 0;
      display: block;
      width: 100%;
      height: 100%;
      padding: 5px 20px 5px 5px;
      color: #b6cce3;
    }
    .link {
      margin: 0;
      display: block;
      width: 100%;
      height: 100%;
      padding: 5px 20px 5px 5px;
      color: #36749d;
    }
  }

  .able {
    background-color: white;
    .link {
      color: #36749d;
    }
  }

  .noable {
   background-color: white;
    color: #36749d;
  }

  .link {
    text-decoration: none;
  }

    .show {
      .link {
        color: white;
        background-color:#36749d;
        border-radius: 10px;
      }
  }

  .show-inactive {
    .link {
      color: #36749d;
      background-color: white;
      border-radius: 10px;
    }
  }
  .active {
    background-color: #36749d;
    .link {
      color: white;
    }
  }
}

.text {
  padding-left: 25px;
}

.icon-amazon-prime {
  width: 120px;
  margin-left: 7%;
  margin-top: -8px;
}
.arrow-amzn {
  margin-top: -27px;
  display: block;
  float: right;
  font-size: 28px;
  color: #36749d;
  transform: rotate(90deg);
}
.reversed-amzn-arrow {
  margin-top: -27px;
  display: block;
  float: right;
  font-size: 28px;
  color: white;
  transform: rotate(270deg);
}
.arrow {
  display: block;
  float: right;
  font-size: 28px;
  color: #36749d;
  transform: rotate(90deg);
}

.dettaglio {
  color: #36749d;
  padding: 10px 12px;
  border: 1px solid #36749d;
  position: absolute;
  margin-top: -40px;
  right: 40px;
  font-size: 14px;
}

.active-sciolto {
  text-decoration: none;
  background-color: $green;
  border: 1px solid white;
  padding: 4px 7px;
  border-radius: 10px;
  float: right;
  font-size: 11px;
  color: white;
}

/*.button:focus {
  text-decoration: none;
  background-color: $green;
  border: 1px solid white;
  color: white;
}*/

.matryoshka {
  padding: 5px 25px 5px 0;
  background: url("../../../../assets/img/optima/Set_Icone_AreaClienti_Tutto-In-Uno.png") no-repeat center;
  background-size: contain;
  height: 60px;
  //margin-right: 12px;
}
 .priceBlock{
   border-left: 1px solid #b6cce3;
   margin-left: 12px;
   width: 39%;
 }
.service {
  text-align: center;
  width: 100%;
  color: #36749d;
  font-size: 10px;
  margin-top: 11px;
  line-height: 10px;
}

.price {
  text-align: center;
  width: 100%;
  font-weight: bold;
  font-size: 15px;
  line-height: 15px;
  color: #2d2d2d;
}
.col-md-5{
  padding: 0;
}




@media only screen and (max-width: 1600px) {
  .matryoshka {
    height: 50px;
  }
  .priceBlock{
    border-left: 1px solid #b6cce3;
    margin-left: 5px;
    width: 40%;
  }
  .service {
    font-size: 8.5px;
    margin-top: 13px;
    line-height: 8px;
  }

  .price {
    font-size: 12.5px;
    line-height: 10px;
      }

  ul {
    li {

      margin-top: 10px;
      font-size: 16px;
      .disableText {
        padding: 5px 20px 5px 5px;
      }
      .link {
        padding: 5px 20px 5px 5px;
      }
    }
  }

  .text {
    padding-left: 25px;
  }

  .button {
    color: $green;
    padding: 4px 7px;
    border: 1px solid $green;
    border-radius: 10px;
    float: right;
    font-size: 11px;
    text-decoration: none;
  }


}
@media only screen and (max-width: 1400px) {
  .priceBlock{
    margin-left: 5px;
    width: 40%;
  }
  .service {
    font-size: 8.5px;
    margin-top: 13px;
    line-height: 8px;
  }

  .price {
    font-size: 12.5px;
    line-height: 10px;
  }
}
@media only screen and (max-width: 1100px) {
  .matryoshka {

    margin:0 7px;
  }
  .priceBlock{
    width: 39%;
    margin-left: 0;
  }
  .service {
    font-size: 8px;
    margin-top: 13px;
    line-height: 8px;
  }

  .price {
    font-size: 12px;
    line-height: 10px;
  }

  ul {
    li {

      margin-top: 10px;
      font-size: 16px;
      .disableText {
        padding: 5px 20px 5px 5px;
      }
      .link {
        padding: 5px 20px 5px 5px;
      }
    }
  }

  .text {
    padding-left: 10px;
  }

  .button {

    padding: 4px 5px;
    right: 24px;
    font-size: 9px;
  }
}
@media only screen and (max-width: 991px) {
  .show {
    .menuBlock {
      border: none;
      border-bottom-right-radius: 5px;
      border-bottom-left-radius: 5px;
    }
  }
  .show-inactive {
    .menuBlock {
      border: none;
      border-bottom-right-radius: 5px;
      border-bottom-left-radius: 5px;
    }
  }
  .matryoshka {
    float: left;
    margin:0 20px;
    padding: 0 25px 0 0;
  }
  .priceBlock{
    border-left: none;
    margin-left: 5px;
    border-left: 1px solid #b6cce3;
    float: left;
    width: 39%;

  }
  .service {
    font-size: 10px;
    margin-top: 13px;
    line-height: 8px;
  }

  .price {
    font-size: 14px;
    line-height: 10px;
  }
  .text {
    padding-left: 5px;
  }

  .button {

    padding: 2px 2px;
    right: 24px;
    font-size: 8.5px;
  }
}
@media only screen and (max-width: 991px) {

  .service {
    font-size: 14px;
    line-height: 10px;
  }

  .price {
    font-size: 19px;
    line-height: 13px;
  }
  ul {
    li {

      margin-top: 10px;
      font-size: 18px;
      .disableText {
        padding: 7px 20px 7px 5px;
      }
      .link {
        padding: 7px 20px 7px 5px;
      }
    }
  }

  .text {
    padding-left: 30px;
  }

  .show .link {
    .arrow {
      color: #ffffff;
      transform: rotate(-90deg);
    }
  }

  .button {
    padding: 5px 5px;
    right: 30px;
    font-size: 11px;
  }
}
@media only screen and (max-width: 400px) {
  .passa-tuto-title{
    padding-top: 2%;
    color: #e54c36;
    text-decoration: underline;
    font-size: 16px;
  }
  .matryoshka {
    height: 50px;
    float: left;
    margin:0 7px;
    width: 10%;
  }
  .priceBlock{
    border-left: none;
    float: left;
    width: 35%;
    padding: 0;

  }
  .service {
    font-size: 9px;
    line-height: 8px;
  }

  .price {
    font-size: 12px;
    line-height: 10px;
  }

}
.addition{
  color: #a2a2a2;
  padding-right: 100px;
  line-height: 16px;
}
.show{
  .menuBlock{
    display: block;
  }
}
.show-inactive{
  .menuBlock{
    display: block;
  }
}
.menuBlock{
  display: none;
  background: white;
  border-bottom: 1px solid #b6cce3;
}

.view{
  background-color:#f0f5f9;
  .detail{
    display: block;
  }
}
.detail{
  padding: 15px 7px;
  display: none;
  background:#f0f5f9;
  border-top:1px solid #b6cce3;
}
.mainText{
  display: inline-block;
  font-size: 14px;
  color: #000;
  line-height: 10px;
  padding: 10px;
  width: 100%;
}

.nascondi{
  background: white;
}
.icon{
  float: left;
  width: 65px;
  height: 65px;
  margin: 10px 15px 0 auto;
}
.icons{
  margin-left: 15px;
}
.url{
  line-height: 18px;
  margin-top: 15px;
  a{
    padding:8px 0;
    color: #000;
  }
  a:hover{
    text-decoration:underline;
  }
}


