<div class="main">
  <div class="top"
       *ngIf="payData && !userData?.clienteConvertibile && (hasTutoInUnoActive | async)">
    <div class="col-md-1 matryoshka">
    </div>
    <div class="col-md-4 priceBlock">
      <p class="service">Canone Totale</p>
      <p class="price">€ {{payData.tuttoInUno}}</p>
    </div>
    <div class="col-md-4 priceBlock">
      <p class="service">Conto Stabilità</p>
      <p class="price">€ {{ payData.contoRelax}}</p>
    </div>
  </div>
  <div class="top pointer" *ngIf="!payData && userData?.clienteConvertibile" [routerLink]="'/passa-tutto-in-uno'">
    <div class="col-md-1 matryoshka">
    </div>
    <div class="col-md-10 passa-tuto-title">
      PASSA A TUTTO-IN-UNO
    </div>
  </div>
  <ul>
    <li *ngFor='let item of serviceMenu' class="{{item.active ? 'able':'noable'}} "
        [className]="item.active ?(activeName===item.title ? 'show' : 'able'): !item.active ?(activeName===item.title ? 'show-inactive' : 'able'): ''"
        (click)="show($event, item.title)">
      <div class='link' *ngIf='item.active'>
        <span class='text content'>{{item.title}} </span>
        <span class="fa fa-angle-right arrow right" aria-hidden="true"></span>
      </div>
      <div class='link'
           *ngIf='!item.active'>
        <span class="text">{{item.text}}</span>
        <a class='button' [className]="activeName===item.title ? 'active-sciolto' : 'button'" *ngIf="item.isScolitoPresent" (click)="showPromotion($event, item.title)">
          Scopri di più</a>
      </div>
      <div class="menuBlock">
        <div *ngIf="'MOBILE'==item.text && item.active">
          <app-mobile-layout></app-mobile-layout>
        </div>
        <div *ngIf="'MOBILE'==item.text && !item.active && item.isScolitoPresent">
          <app-sciolto-mobile></app-sciolto-mobile>
        </div>
        <div *ngIf="'LUCE'==item.text && item.active">
          <app-luce-layout></app-luce-layout>
        </div>
        <div *ngIf="'LUCE'==item.text && !item.active && item.isScolitoPresent">
          <app-sciolto-luce></app-sciolto-luce>
        </div>
        <div *ngIf="'GAS'==item.text && item.active">
          <app-gas-layout></app-gas-layout>
        </div>
        <div *ngIf="'GAS'==item.text && !item.active && item.isScolitoPresent">
          <app-sciolto-gas></app-sciolto-gas>
        </div>
        <div *ngIf="('FIBRA'==item.text || 'INTERNET'==item.text) && item.active">
          <app-fibra-layout></app-fibra-layout>
        </div>
        <div *ngIf="('FIBRA'==item.text || 'INTERNET'==item.text) && !item.active && item.isScolitoPresent">
          <app-sciolto-fibra></app-sciolto-fibra>
        </div>
        <div *ngIf="'FISSO'==item.text && item.active">
          <app-fisso-layout></app-fisso-layout>
        </div>
        <div *ngIf="'FISSO'==item.text && !item.active && item.isScolitoPresent">
          <app-sciolto-fisso></app-sciolto-fisso>
        </div>
      </div>
    </li>
    <li class="able" (click)="showAmazonDetails()" *ngIf="showServiziButton" [className]="showedDetails ? 'show' : 'able'">
      <div class="link">
        <div class="text">SERVIZI AGGIUNTIVI</div>
        <span class="fa fa-angle-right arrow-amzn right" *ngIf="!showedDetails" aria-hidden="true"></span>
        <span class="fa fa-angle-right reversed-amzn-arrow right" *ngIf="showedDetails" aria-hidden="true"></span>
      </div>
      <div class="menuBlock" *ngIf="showedDetails">
        <div>
          <app-servizi-aggiuntnivi></app-servizi-aggiuntnivi>
        </div>
      </div>
    </li>
    <li class="noable show-inactive" (click)="showAmazonDetails()" *ngIf="isActiveTeleconsultoMedico">
      <div class="link">
        <span class="text">TELECONSULTO MEDICO</span>
        <a class='button' [className]="activeName==='TELECONSULTO MEDICO' ? 'active-sciolto' : 'button'" (click)="showPromotion($event, 'TELECONSULTO MEDICO')">Scopri di più</a>
      </div>
      <div class="menuBlock" *ngIf="showedDetails">
        <div>
          <app-sciolto-teleconsulto-medico></app-sciolto-teleconsulto-medico>
        </div>
      </div>
    </li>
  </ul>
</div>
<div class="recontact-button-desktop" *ngIf="(userInfo | async)?.cluster &&
((userInfo | async)?.cluster.value === 'BUSINESS' || (userInfo | async)?.cluster.value === 'CONSUMER')" [routerLink]="['/faidate/recontact']">
</div>
