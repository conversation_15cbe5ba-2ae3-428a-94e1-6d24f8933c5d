<app-month-modal-window *ngIf="shouldShowMonthModal | async">

  <!--  <span *ngIf="dialogModal?.title && !dialogModal?.customTitle" class="title modal-title">{{dialogModal.title}}</span>-->
  <div class="icon text-center">
    <i class=" fa fa-check fa-4x"></i>
  </div>
  <div class="text modal-text"><b>{{dialogModal.text}}</b></div>

  <div class="data flex-container">

    <div class="month-flex">

      <div class="month-block" *ngFor="let monthEntity of dialogModal.data" placement="top" triggers="hover" [popover]="monthEntity.unableToSelect ? mesePopTemplate : null"
           [ngClass]="{
        'selected':monthEntity.selected,
        'disabled':!monthEntity.clickable
        }"
           (click)="selectMonth(monthEntity, dialogModal.data)">
        <div>
          {{monthEntity.title}}
        </div>

      </div>

    </div>

  </div>

  <div class="confirm-btn flex-container" *ngIf="dialogModal.data">
    <button class="month-btn" (click)="confirmMonthSelection()">SALVA</button>
  </div>

  <ng-template #mesePopTemplate>
    Questo mese non può essere selezionato!
  </ng-template>

</app-month-modal-window>
