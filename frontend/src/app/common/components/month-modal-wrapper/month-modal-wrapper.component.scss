@import "~app/shared/styles/colors";

.flex-container {
  display: flex;
  justify-content: center;
}

.month-flex {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;

  flex-basis: 600px;
  margin-top: 30px;
  margin-bottom: 30px;
}

.month-block {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  border: #00b9e2 solid 1px;
  border-radius: 7px;
  color: #00b9e2;
  width: 105px;
  height: 70px;
  margin: 5px;
  cursor: pointer;
}

.selected {
  background-color: #00b9e2;
  border: #00b9e2 solid 1px;
  color: white;
}

.disabled {
  background-color: #e5e5e5;
  border: #979797 solid 1px;
  color: #979797;
  cursor: not-allowed;
}


.month-block .unableSelectLabel {
  visibility: hidden;
  position: relative;
  bottom: 30px;
  left: 0;
  max-width: 100px;
  max-height: 70px;
}

.month-block:hover .unableSelectLabel {
  visibility: visible;
}

.month-block h3 {
  margin: 0;
}

.month-btn {
  background: #9BC641;
  border: #9BC641 solid 2px;
  border-radius: 5px;
  cursor: pointer;

  color: white;
  font-family: Lato-Regular, serif;
  font-size: 24px;

  /*min-height: 50px;*/
  min-width: 150px;
  margin-top: 10px;
}

.modal-image {
  text-align: center;
  margin: 3% auto auto;
  width: 50px;
  display: block;
}

.modal-title {
  margin: 4% auto auto;
  width: 60%;
  display: block;
  text-align: center;
  font-size: 18px;
  color: #36749d;
  font-weight: 700;
}

.custom-title {
  margin: 0;
  width: 40%;
  font-weight: 100;
  font-size: 22px;
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 70%;
  padding-top: 10px;
  font-size: 18px;
  margin: 4% auto auto;
}

.modal-fill-field {
  text-align: center;
  display: block;
  margin: 2% auto auto;
  width: 100px;
  height: 30px;
  color: $dark-blue;
  border: 1px solid $dark-blue;
  border-radius: 2px;
  font-size: 14px;
  background-color: #ffffff;
}

.modal-buttons {
  text-align: center;
  margin-top: 20px;
  margin-bottom: 2%;

  .cancel-button {
    color: $dark-blue;
    padding: 3px 8px;
    border: 1px solid $dark-blue;
    margin-right: 10px;
    border-radius: 2px;
    font-size: 14px;
    background-color: #ffffff;
    display: inline-block;
    cursor: pointer;
  }

  .continue-button {
    color: white;
    padding: 3px 8px;
    margin-left: 10px;
    border: 1px solid $dark-blue;
    border-radius: 2px;
    font-size: 14px;
    background-color: $dark-blue;
    display: inline-block;
    cursor: pointer;
  }
}

.form-input:disabled {
  border: none;
  width: 100%;
}

.accept-button {
  color: #5cb85c;
}

.no-padding {
  padding: 0 !important;
}

.confirm-description {
  color: #e54c36;
  text-align: center;
}

.user-data-table-block {
  align-items: center;
  width: 84%;
  display: block;
  margin: auto;
  color: $dark-blue;
  border: 1px solid $menu-border;
  border-radius: 5px;

  .material {
    border-radius: 5px;
  }

  .detail-row {
    display: inline-flex;
    padding-right: 0;
    padding-left: 0;

    div {
      padding-top: 10px;
      padding-bottom: 10px;
    }

    .data {
      font-size: 14px;
      font-weight: 400;

      .payment-info {
        display: block;
      }
    }

    .subject {
      font-weight: bold;
      border-right: 2px solid $menu-border;
      padding-right: 0;
    }
  }

}

.dropdown-container {
  margin: 0;
  width: 100%;
  padding: 0;

  .dropdown-select {
    width: 100%;
    background: white;
    height: 30px;
    border: 1px solid $menu-border;
    border-radius: 6px;
  }
}

select option[data-default] {
  color: #888;
}

.editable {
  width: 50px;
  height: 50px;
}

.italic-font {
  font-style: italic;
}

.straight-font {
  font-style: normal;
}

.input-wrong-code {
  text-align: center;
}

.input-wrong-field {
  float: left;
  font-size: 12px;
  color: #e54c36;
  width: 100%;
}

@media only screen and (max-width: 768px) {
  .custom-title {
    font-size: 16px;
  }
  .user-data-table-block {
    width: 90%;
    height: 85%;

    .detail-row {
      height: 57px;

      .data {
        font-size: 12px;
      }

      .subject {
        font-size: 13px;
      }
    }
  }
}

@media only screen and (max-width: 535px) {
  .custom-title {
    font-size: 12px;
  }
  .user-data-table-block {
    width: 90%;
    height: 85%;

    .detail-row {
      height: 47px;

      .data {
        font-size: 8px;
        padding: 14px 5px 5px;

        .dropdown-select {
          height: 18px;
        }
      }

      .subject {
        font-size: 9px;
        width: 45%;
        padding: 10px;
      }
    }

    .input-wrong-field {
      font-size: 8px;
    }
  }
  .modal-buttons {
    margin-top: 10px;
  }
}

@media only screen and (max-width: 535px) {
  .month-flex {
    width: 300px;
    height: auto;
  }

  .month-block {
    width: 70px;
    height: 45px;
    margin: 3px;
    text-align: center;
  }
}
