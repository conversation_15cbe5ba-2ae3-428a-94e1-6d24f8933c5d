import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import {Observable} from 'rxjs/Observable';
import {NotificaMdpInfo} from '../../model/notifica-mdp/NotificaMdpInfo';
import {a} from '@angular/core/src/render3';

@Component({
  selector: 'app-notifica-mdp',
  templateUrl: './notifica-mdp.component.html',
  styleUrls: ['./notifica-mdp.component.scss']
})
export class NotificaMdpComponent implements OnInit {

  @Input('mdpInfo') mdpInfo: Observable<NotificaMdpInfo>;

  mdpData: Array<NotificaMdpInfo> = [];

  constructor() {
  }

  ngOnInit() {
    this.mdpInfo.subscribe(a => {
      if (a){
        for (let i in a) {
          this.mdpData.push(a[i]);
        }
      }
    });
  }
}
