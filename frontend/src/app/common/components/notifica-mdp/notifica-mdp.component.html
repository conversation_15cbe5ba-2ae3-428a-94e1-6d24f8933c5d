<div *ngFor="let i of mdpData">
  <app-modal [show]="i.isDisplayedNotificationText === true">
    <modal-title>
      <div class="header-text" *ngIf="i.headerText">
        <span>{{i.headerText}}</span>
      </div>
    </modal-title>
    <modal-description>
      <div class="modal-description">
        <div *ngIf="!i.textMarker && !i.redirectUrl">
          <span class="no-redirect-url">{{i.notificationText}}</span>
        </div>
        <div *ngIf="i.textMarker && i.redirectUrl">
          <span>{{i.notificationTextFirstPart}}</span>
          <a [attr.href]="i.redirectUrl" class="redirect-url">
            {{i.textMarker}}
          </a>
          <span>{{i.notificationTextSecondPart}}</span>
        </div>
        <div *ngIf="!i.textMarker && i.redirectUrl">
          <a [attr.href]="i.redirectUrl" class="redirect-url-no-marker">
            {{i.notificationText}}
          </a>
        </div>
      </div>
    </modal-description>
  </app-modal>
</div>


