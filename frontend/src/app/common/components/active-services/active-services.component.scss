@import "~app/shared/styles/colors";

.content {
  margin-bottom: 20px;
}
.recontact-button-block, .recontact-menu-button, .banner-5g-block {
  display: block;
  margin-top: 3%;

  .recontact-button-desktop {
    padding-bottom: 15px;
    padding-top: 15px;
    background-size: cover;
    height: 100px;
    background: url("../../../../assets/img/assistant/dashbutton.png") no-repeat center;
    background-size: contain;
  }

  .recontact-button-desktop img {
    height: 150px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    cursor: pointer;
    // width: 50%;
  }

  .banner-5g {
    background: url("../../../../assets/img/home-page/5g_banner.png") no-repeat center;
    background-size: contain;
    height: 284px;
    cursor: pointer;
  }
}

.no-communication-message {
  padding: 15px;
  text-align: center;
  min-height: 150px;
  font-weight: bold;
  color: #36749C;
}
.main {
  border: 1px solid $menu-border;
  border-radius: 10px;
  background-color: $menu-background;
}

.fa-angle-right {
  font-size: 25px;
  float: right;
}

.amazon-button {
  width: 130px;
  height: 38px;
  margin-left: 15px;
}

.amazon-prime-link {
  display: flex;
}

.i-button {
  border-radius: 50%;
  width: 20px;
  height: 20px;
  border: 1px solid;
  display: block;
  position: absolute;
  right: 10px;
  padding-left: 6px;
  font-size: 12px;
  font-weight: 600;
  color: $dark-blue;
  cursor: pointer;
}

.top {
  background-color: white;
  border-radius: 10px 10px 0 0;
  border-bottom: 1px solid $menu-border;
  padding: 5px;
  margin-left: 0;
  margin-right: 0;
}

.btn-condomini {
  margin-top: 160px;
  width: 100%;
  height: 35px;
  background-color: white;
  color: #5489aa; //old:b6cce3
  border: 2px solid #5489aa; //old:b6cce3
  border-radius: 10px;
  font-size: 18px;
  padding-right: 5px;
  position: relative;
  @media screen and (max-width: 1200px) {
    font-size: 16px;
  }
  @media screen and (max-width: 1100px) {
    font-size: 15px;
  }
}

.btn-condomini-img {
  position: absolute;
  left: 2%;
  bottom: 24%;
  transform: rotate(90deg);
}

.ul-mar {
  margin-left: 10px;
  margin-right: 10px;;
}

.passa-tuto-title {
  padding-top: 5%;
  color: $coral;
  text-decoration: underline;
  font-size: 17px;
}

ul {
  list-style-type: none;
  padding-left: 0;
  margin-left: -10px;
  margin-right: -10px;

  li {
    border: 2px solid $menu-border;
    border-radius: 10px;
    margin-top: 10px;
    text-align: left;
    background-color: white;
    font-size: 18px;
    padding: 0;
    color: $dark-blue;
    cursor: pointer;
    font-weight: bold;

    .disableText {
      margin: 0;
      display: block;
      width: 100%;
      height: 100%;
      padding: 5px 20px 5px 5px;
      color: #b6cce3;
      text-decoration: none;
    }

    .link {
      margin: 0;
      display: block;
      width: 100%;
      height: 100%;
      padding: 5px 20px 5px 5px;
      color: $dark-blue;
    }
  }

  .unable {
    background-color: white;
    border-radius: 10px;

    :hover {
      background-color: #b8cce4;
      color: white;
    }

    :focus {
      background-color: #b8cce4;
      color: white;
    }
  }

  .able {
    background-color: white;

    .link {
      color: $dark-blue;
    }
  }

  .link {
    text-decoration: none;
  }

  .able:hover {
    background-color: $dark-blue;
    border-color: $dark-blue;

    .link {
      color: white;
    }
  }

  .active {
    background-color: $dark-blue;
    border-color: $dark-blue;

    .link {
      color: white;
    }
  }
}

.active-sciolto {
  background-color: $green;
  border: 1px solid white !important;
  color: white !important;
  text-decoration: none;
}

.text {
  padding-left: 25px;
}

.arrow {
  padding: 5px 30px 5px 0;
  background: url("https://www.freeiconspng.com/uploads/white-arrow-transparent-png-22.png") no-repeat center;
  background-size: contain;
  color: white;
  height: 26px;
  position: absolute;
  right: 30px;
}

.button {
  color: $green;
  padding: 4px 7px;
  border: 1px solid $green;
  border-radius: 8px;
  // position: absolute;
  float: right;
  right: 30px;
  font-size: 11px;
}

.button:hover {
  text-decoration: none;
/*  background-color: $menu-background;*/
}

.matryoshka {
  padding: 5px 25px 5px 0;
  background: url("../../../../assets/img/optima/colibri.png") no-repeat center;
  background-size: contain;
  height: 60px;
  width: 40px;
  //margin-right: 12px;
}

.priceBlock {
  border-left: 1px solid #b6cce3;
  margin-left: 6px;
  width: 39%;
  padding: 0;
}

.service {
  text-align: center;
  width: 100%;
  color: $dark-blue;
  font-size: 10px;
  margin-top: 11px;
  line-height: 10px;
}

.price {
  text-align: center;
  width: 100%;
  font-weight: bold;
  font-size: 15px;
  line-height: 15px;
  color: #2d2d2d;
}

.col-md-5 {
  padding: 0;
}


.communication {
  margin-left: -10px;
  margin-right: -10px;
  border: 1px solid #b6cce3;
  border-radius: 5px;
  margin-top: 30px;

  .icon {
    background: url("../../../../assets/img/optima/Set_Icone_AreaClienti_Comuncazioniperte.png") no-repeat;
    background-size: contain;
    height: 55px;
    position: absolute;
    float: left;
    margin: -10px 5px;
    width: 55px
  }

  .title {
    background-color: #f0f5f9;
    color: $dark-blue;
    text-align: center;
    font-size: 15px;
    line-height: 25px;
    border-radius: 5px 5px 0 0;
  }

  .items {
    width: 80%;
    margin: auto;
    padding: 10px 0;

    .item {
      list-style-type: disc;
      border: none;
      text-align: justify;
      font-size: 16px;
      padding-right: 5px;
      font-weight: normal;
      white-space: pre-wrap;
      cursor: default;
    }
    .item-title {
      list-style-type: none;
      border: none;
      text-align: justify;
      font-size: 16px;
      padding-right: 5px;
      font-weight: normal;
      white-space: pre-wrap;
    }
  }
}

@media only screen and (max-width: 1600px) {
  .matryoshka {
    height: 50px;
  }
  .priceBlock {
    border-left: 1px solid #b6cce3;
    margin-left: 5px;
    width: 40%;
  }
  .service {
    font-size: 8.5px;
    margin-top: 13px;
    line-height: 8px;
  }

  .price {
    font-size: 12.5px;
    line-height: 10px;
  }

  ul {
    li {

      margin-top: 10px;
      font-size: 16px;

      .disableText {
        padding: 5px 20px 5px 5px;
      }

      .link {
        padding: 5px 20px 5px 5px;
      }
    }
  }

  .text {
    padding-left: 25px;
  }

  .arrow {
    padding: 5px 24px 5px 0;
    height: 20px;
    right: 24px;
  }

  .button {

    padding: 4px 5px;
    right: 24px;
    font-size: 9px;
  }


}

@media only screen and (max-width: 1400px) {
  .priceBlock {
    margin-left: 5px;
    width: 40%;
  }
  .service {
    font-size: 12px;
    margin-top: 13px;
    line-height: 8px;
  }

  .price {
    font-size: 16px;
    line-height: 10px;
  }
}

@media only screen and (max-width: 1100px) {
  .matryoshka {

    margin: 0 7px;
  }
  .priceBlock {
    width: 39%;
    margin-left: 0;
  }
  .service {
    font-size: 8px;
    margin-top: 13px;
    line-height: 8px;
  }

  .price {
    font-size: 12px;
    line-height: 10px;
  }

  ul {
    li {

      margin-top: 10px;
      font-size: 16px;

      .disableText {
        padding: 5px 20px 5px 5px;
      }

      .link {
        padding: 5px 20px 5px 5px;
      }
    }
  }

  .text {
    padding-left: 6px;
  }

  .arrow {
    padding: 5px 24px 5px 0;
    height: 20px;
    right: 24px;
  }

  .button {

    padding: 4px 5px;
    right: 24px;
    font-size: 9px;
  }
}

@media only screen and (max-width: 991px) {
  .communication {
    margin-left: 0;
    margin-right: 0;
  }
  .passa-tuto-title {
    padding-top: 2%;
  }
  .main {
    margin: 0 0 15px 0;
  }
  .matryoshka {
    float: left;
    margin: 0 20px;
    padding: 0 25px 0 0;
  }
  .priceBlock {
    border-left: none;
    margin-left: 5px;
    width: 40%;
    float: left;
  }
  .service {
    font-size: 10px;
    margin-top: 13px;
    line-height: 8px;
  }

  .price {
    font-size: 14px;
    line-height: 10px;
  }

  ul {
    li {

      margin-top: 6px;
      font-size: 13px;

      .disableText {
        padding: 4px 20px 4px 5px;
      }

      .link {
        padding: 4px 20px 4px 5px;
      }
    }
  }

  .text {
    padding-left: 5px;
  }

  .arrow {
    padding: 4px 20px 4px 0;
    height: 13px;
    right: 23px;
  }

  .button {

    padding: 2px 2px;
    right: 24px;
    font-size: 8.5px;
  }
}

@media only screen and (max-width: 767px) {
  .matryoshka {
    float: left;
    margin: 0 20px;
    padding: 0 25px 0 0;
    width: 10%;
  }
  .priceBlock {
    border-left: 1px solid $menu-border;
    float: left;
    width: 39%;

  }
  .service {
    font-size: 14px;
    line-height: 10px;
  }

  .price {
    font-size: 19px;
    line-height: 13px;
  }
  ul {
    li {

      margin-top: 10px;
      font-size: 18px;

      .disableText {
        padding: 7px 20px 7px 5px;
      }

      .link {
        padding: 7px 20px 7px 5px;
      }
    }
  }

  .text {
    padding-left: 30px;
  }

  .able:hover {
    background-color: $dark-blue;

    .arrow {
      transform: rotate(90deg);
    }
  }
  .arrow {
    padding: 5px 30px 5px 0;
    height: 20px;
    right: 30px;
    transform: rotate(-90deg);
  }


  .button {
    padding: 5px 5px;
    right: 30px;
    font-size: 11px;
  }
}

@media only screen and (max-width: 400px) {
  .matryoshka {
    height: 50px;
    float: left;
    margin: 0 7px;
    width: 10%;
  }
  .priceBlock {
    border-left: 1px solid $menu-border;
    float: left;
    width: 35%;
    padding: 0;
  }
  .service {
    font-size: 9px;
    line-height: 8px;
  }

  .price {
    font-size: 12px;
    line-height: 10px;
  }

}

