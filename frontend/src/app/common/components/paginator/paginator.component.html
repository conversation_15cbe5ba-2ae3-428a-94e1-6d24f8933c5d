<div class="paginator">
  <span *ngIf="showLabel">
  <span>Visualizzare per pagina: {{chunk}}</span>
  <span>{{currentPage * chunk - chunk + 1}}-{{currentPage * chunk > _items.length ?
    _items.length : currentPage * chunk}} di {{_items.length}} items</span>
    </span>
  <span class="navigation">
        <i class="material-icons" (click)="firstPage()">first_page</i>
        <i class="material-icons" (click)="previousPage()">chevron_left</i>
        <i class="material-icons" (click)="nextPage()">chevron_right</i>
        <i class="material-icons" (click)="lastPage()">last_page</i>
      </span>
</div>
