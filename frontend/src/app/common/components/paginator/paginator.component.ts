import { Component, Input, OnInit } from '@angular/core';
import { BehaviorSubject } from 'rxjs/BehaviorSubject';

@Component({
  selector: 'app-paginator',
  templateUrl: './paginator.component.html',
  styleUrls: ['./paginator.component.scss']
})
export class PaginatorComponent implements OnInit {

  @Input('chunk') chunk = 10;

  _items: Array<any>;

  pages: number;

  currentPage = 1;

  @Input() showLabel: boolean = true;

  @Input('listener') listener: BehaviorSubject<Array<any>>;

  constructor() {

  }

  @Input('items')
  set items(items: Array<any>) {
    this._items = items;
    this.pages = Math.ceil(items.length / this.chunk);
    this.currentPage = 1;
    this.listener.next(this._items.slice(this.currentPage * 10 - 10, this.currentPage * 10));
  }

  ngOnInit() {
  }

  firstPage() {
    if (this.currentPage !== 1) {
      this.currentPage = 1;
      this.listener.next(this._items.slice(this.currentPage * 10 - 10, this.currentPage * 10));
    }
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.listener.next(this._items.slice(this.currentPage * 10 - 10, this.currentPage * 10));
    }
  }

  nextPage() {
    if (this.currentPage < this.pages) {
      this.currentPage++;
      this.listener.next(this._items.slice(this.currentPage * 10 - 10, this.currentPage * 10));
    }
  }

  lastPage() {
    if (this.currentPage !== this.pages) {
      this.currentPage = this.pages;
      this.listener.next(this._items.slice(this.currentPage * 10 - 10, this.currentPage * 10));
    }
  }

  reset() {
    this.pages = Math.ceil(this._items.length / this.chunk);
    this.currentPage = 1;
    this.listener.next(this._items.slice(this.currentPage * 10 - 10, this.currentPage * 10));
  }

}
