import { FormArray, FormControl, FormGroup } from '@angular/forms';

export class FormUtils {

  static setFormControlsAsTouched(group: FormGroup | FormArray) {
    group.markAsTouched();
    for (const i in group.controls) {
      if (group.controls[i] instanceof FormControl) {
        group.controls[i].markAsTouched();
      } else {
        this.setFormControlsAsTouched(group.controls[i]);
      }
    }
  }

  static setFormControlAsTouched(group: FormControl) {
    group.markAsTouched();
  }

  static disableForm(group: FormGroup | FormArray) {
    if (group) {
      for (const i in group.controls) {
        if (group.controls[i]) {
          group.controls[i].disable();
        } else {
          this.setFormControlsAsTouched(group.controls[i]);
        }
      }
    }
  }

}
