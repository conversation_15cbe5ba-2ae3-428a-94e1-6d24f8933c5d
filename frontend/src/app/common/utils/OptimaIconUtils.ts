import { Injectable } from '@angular/core';

@Injectable()
export class OptimaIconUtils {

  constructor() {

  }

  servicesIconsMap = {
    gas: 'icon-gas',
    mobile: 'icon-mobile',
    energia: 'icon-luce',
    adsl: 'icon-internet',
    voip: 'icon-fisso',
    wlr: 'icon-fisso',
    voce: 'icon-fisso',
    servizi: 'icon-servizi-aggiuntivi'
  };

  getServiceIconByName(iconName: string) {
    if (iconName) {
      return this.servicesIconsMap[iconName.toLowerCase()];
    }
  }
  getServiceMap() {
    return this.servicesIconsMap;
  }
}
