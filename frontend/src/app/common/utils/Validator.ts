import { AbstractControl, FormArray, FormControl, FormGroup } from '@angular/forms';

export default class Validator {

  static requiredIfFieldIsNotSet(fieldName: string) {
    return function (input: FormControl) {
      if (!input.root || !(<FormGroup>input.root).controls) {
        return null;
      }
      if (!(<FormGroup>input.root).controls[fieldName].value && !input.value) {
        return {required: true};
      }
      return null;
    };
  }

  static requiredIfFieldIs(fieldName: string, valueWhenRequired: string) {
    return function (input: FormControl) {
      if (!input.root || !(<FormGroup>input.root).controls) {
        return null;
      }
      if ((<FormGroup>input.root).controls[fieldName].value === valueWhenRequired
        && (input.value === null || input.value === '')) {
        return {required: true};
      }
      return null;
    };
  }

  static withLength(min: number, max: number) {
    return (c: AbstractControl) => {
      if (c.value) {
        const controlValueLength = c.value.toString().length;
        if (controlValueLength < min) {
          return {minLength: true};
        }
        if (controlValueLength > max) {
          return {maxLength: true};
        }
      }
      return null;
    };
  }

  static isGreaterThan(num: number) {
    return function (input: FormControl) {
      if (input.value > num){
        return {
          isGreater: true,
          errorMsg: `Il numero massimo di rate è ` + num
        };
      }
      return null;
    };
  }

  static requiredWithMessage(name: string, errorMsg: string) {
    return function (input: FormControl) {
      if (!input.value || input.value.length < 0){
        return {
          required: true,
          errorMsg: errorMsg
        };
      }
      return null;
    };
  }

  static isExist() {
    return function (input: FormControl) {
      if (!input.value){
        return {
          emptryValue: true,
          errorMsg: 'Select a value'
        };
      }
      return null;
    };
  }

  static requiredIfFieldIsArray(fieldName: string, valueWhenRequired: Array<string>) {
    return function (input: FormControl) {
      if (!input.root || !(<FormGroup>input.root).controls) {
        return null;
      }
      if (valueWhenRequired.indexOf((<FormGroup>input.root).controls[fieldName].value) !== -1
        && (input.value === null || input.value === '')) {
        return {required: true};
      }
      return null;
    };
  }

  static withExpression(expression: any) {
    return function (input: FormControl) {
      if (!input.root || !(<FormGroup>input.root).controls) {
        return null;
      }
      return expression(input, input.root);
    };
  }

  static withExpressionMessage(expression: any, message: string) {
    return function (input: FormControl) {
      if (!input.root || !(<FormGroup>input.root).controls) {
        return null;
      }
      if (expression(input, input.root)) {
        return {validationError: message};
      }
    };
  }

  static invalidIf(expression: any, validatorParameters: any, message: string) {
    return (input: FormControl) => {
      if (!input.root || !(<FormGroup>input.root).controls) {
        return null;
      }
      if (expression(input, validatorParameters)) {
        return {validationError: message};
      }
    };
  }


  static requiredCountCharacter(number: number, msgError) {
    if (number) {
      const message = msgError;
      return Validator.withExpression((input: FormControl) => {
        return input.value && (input.value.length < number) ? {countchar: message} : null;
      });
    }
  }

  static requiredIfFieldExpressionIs(fieldName: string, valueWhenRequired: string, expression: any) {
    return function (input: FormControl) {
      if (!input.root || !(<FormGroup>input.root).controls) {
        return null;
      }
      if (expression((<FormGroup>input.root).controls[fieldName].value, valueWhenRequired)
        && (input.value === null || input.value === '')) {
        return {required: true};
      }
      return null;
    };
  }

  static requiredIfFieldIsNot(fieldName: string, valueWhenRequired: string) {
    return function (input: FormControl) {
      if (!input.root || !(<FormGroup>input.root).controls) {
        return null;
      }
      if ((<FormGroup>input.root).controls[fieldName].value &&
        (<FormGroup>input.root).controls[fieldName].value !== valueWhenRequired
        && (input.value === null || input.value === '')) {
        return {required: true};
      }
      return null;
    };
  }

  static requiredIfFieldIsNotIgnoreCase(fieldName: string, valueWhenRequired: string) {
    return function (input: FormControl) {
      if (!input.root || !(<FormGroup>input.root).controls || !valueWhenRequired) {
        return null;
      }
      if ((<FormGroup>input.root).controls[fieldName].value &&
        (<FormGroup>input.root).controls[fieldName].value.toLowerCase() !== valueWhenRequired.toLowerCase()
        && (input.value === null || input.value === '')) {
        return {required: true};
      }
      return null;
    };
  }

  static requiredIfFieldIsNotInForDynamic(fieldName: string, valueWhenRequired: string) {
    return function (input: FormControl) {
      if (!input.parent || !(<FormGroup>input.parent).controls) {
        return null;
      }
      if (input.parent.controls[fieldName].value !== valueWhenRequired
        && (input.value === null || input.value === '')) {
        return {required: true};
      }
      return null;
    };
  }

  static listenOtherInputAndValidate(form: FormGroup, listen: string, dependent: Array<string>) {
    form.controls[listen].valueChanges.subscribe(() => {
      dependent.forEach(field => {
        form.get(field).reset(null);
        form.get(field).updateValueAndValidity();
      });
    });
  }

  static listenOtherInputAndSetRequired(form: FormGroup, listen: string, requiredValue: string, dependent: Array<string>) {
    form.controls[listen].valueChanges.subscribe(() => {
      dependent.forEach(field => {
        if (form.get(listen).value === requiredValue) {
          // form.get(dependent)
          // form.get(field).setValidators([Validators.required]);
          // form.get(field).updateValueAndValidity();
        }
      });
    });
  }

  static listenOtherInputValidateAndClearWhenEmpty(form: FormGroup, listen: string, dependent: Array<string>) {
    form.controls[listen].valueChanges.subscribe((input: string) => {
      if (!input) {
        dependent.forEach(field => {
          form.get(field).reset(null);
          form.get(field).updateValueAndValidity();
        });
      }
    });
  }

  static listenOtherInputAndValidateWithoutUpdate(form: FormGroup, listen: string, dependent: Array<string>) {
    form.controls[listen].valueChanges.subscribe(() => {
      dependent.forEach(field => {
        form.get(field).updateValueAndValidity();
      });
    });
  }

  static listenOtherInputAndValidateWithoutUpdateAndТestedАields(form: FormGroup, listen: string, dependent: Array<string>) {
    form.controls[listen].valueChanges.subscribe(() => {
      dependent.forEach(field => {
        (<FormArray>form.get(field)).controls.forEach(formGroup => {
          (<FormGroup>formGroup).controls.consumption.updateValueAndValidity();
        });
      });
    });
  }

  static listenOtherInputAndSetErrorsWithoutUpdate(form: FormGroup, listen: string, dependent: Array<string>, errors: object) {
    form.controls[listen].valueChanges.subscribe(() => {
      dependent.forEach(field => {
        form.get(field).setErrors(errors);
      });
    });
  }

  static listenOtherInputAndSetUpdateWithoutEmitting(form: FormGroup, listen: string, dependent: Array<string>, errors: object) {
    form.controls[listen].valueChanges.subscribe(() => {
      dependent.forEach(field => {
        form.get(field).setValue(null, {emitEvent: false});
      });
    });
  }

  static pattern(pattern: string, error: string) {
    if (pattern) {
      const regex = new RegExp(pattern);
      const message = error ? error : `Pattern ${pattern}`;
      return Validator.withExpression((input: FormControl) => {
        return input.value && !regex.test(input.value) ? {error: message} : null;
      });
    }
  }

  static increase(number: number) {
    if (number) {
      const message = `Increase should be a multiple of ${number} units`;
      return Validator.withExpression((input: FormControl) => {
        return input.value && (input.value % number) !== 0 ? {increase: message} : null;
      });
    }
  }

  static mimeTypes(mimeTypes: Array<String>) {
    return Validator.withExpression((input: FormControl) => {
      if (input.value) {
        const fileErrors = [];
        Object.keys(input.value).forEach((key) => {
          if (mimeTypes.indexOf(input.value[key].type) < 0) {
            fileErrors.push(`Il tipo "${input.value[key].name}" non è consentito.I tipi consentiti sono: ${mimeTypes}`);
          }
        });
        return fileErrors.length > 0 ? {error: fileErrors} : null;

      }
      return null;
    });
  }

  static notEmpty() {
    return (input: FormControl) => {
      if (!input.root || !(<FormGroup>input.root).controls) {
        return null;
      }
      return input.value && !input.value.trim() ? {error: 'Il campo non puo essere vuoto.'} : null;
    };
  }

  static onlyTextAllowed() {
    return this.pattern('^[a-zA-ZàèéìíîòóùúÀÈÉÌÍÎÒÓÙÚ\\s\\\']+$', 'Il valore inserito non è valido.');
  }

  static onlyTextNumberAllowed() {
    return this.pattern('^[0-9a-zA-ZàèéìíîòóùúÀÈÉÌÍÎÒÓÙÚ\\s\\\']+$', 'Il valore inserito non è valido.');
  }

  static digitsCommon(errorMessage) {
    return this.pattern('^[0-9]+$', errorMessage);
  }

  static digits() {
    return this.digitsCommon('Il carattere inserito non è valido');
  }
}
