import {NgModule} from '@angular/core';
import {FormsModule} from '@angular/forms';
import {Offer5gComponent} from './components/offer-5g/offer-5g.component';
import {RouterModule, Routes} from '@angular/router';
import {SharedModule} from "../../shared/shared.module";
import {OffersInAppService} from './service/offers-in-app.service';
import {CommonModule} from "../../common/common.module";
import {Deactivation5gComponent} from './components/deactivation-5g/deactivation-5g.component';
import {OptimaYoungService} from "../optima-young/service/optima-young.service";


const routes: Routes = [
  {path: '5G', component: Offer5gComponent},
  {path: '5G/:subscriptionId', component: Offer5gComponent},
  {path: 'deactivate-5G/:subscriptionId', component: Deactivation5gComponent}
];

@NgModule({
  imports: [
    RouterModule.forChild(routes),
    FormsModule,
    SharedModule,
    CommonModule
  ],
  declarations: [Offer5gComponent, Deactivation5gComponent],
  providers: [OptimaYoungService, OffersInAppService],
  exports: [
    RouterModule
  ],
})
export class OffersInAppsModule {
}
