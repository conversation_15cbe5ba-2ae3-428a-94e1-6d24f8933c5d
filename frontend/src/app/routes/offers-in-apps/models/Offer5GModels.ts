export class AddOn {
  codice: string;
  descrizione: string;
  codiceOfferta: string;
  costoAttivazione: number;
  canoneMese: number;
  numRichiesteAttivazioni: number;
  richiestaInCorso: boolean;
  dataInizioValidita: string
}

export class Sim {
  msisdnId: string;
  subscriptionId: string;
  dataProssimoRinnovo: string;
  addOnAviable: AddOn[];
  addOnActive: AddOn[];
}

export class Offer5GResponse {
  sim: Sim[];
}
