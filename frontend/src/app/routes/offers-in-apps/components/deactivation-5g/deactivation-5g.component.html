<div class="header">
  {{ pageTitle }}
</div>

<div class="content">
  <!-- Information page about 5G -->
  <div class="info-page" *ngIf="viewMode === ViewModeLayout.INFO">
    <div class="icon-container">
      <img src="assets/img/offers-in-app/5G.png" alt="5G" class="icon-5g">
    </div>
    <div class="benefits-title">Ecco i vantaggi del 5G:</div>
    <div class="benefits-box">
      <div class="benefit-item">
        <div class="benefit-title no-margin">Connessione Veloce fino a 1 Gbps:</div>
        <div class="benefit-text">Grazie alla rete 5G di Optima, potrai navigare con una velocità che supera fino a
          dieci volte quella del 4G.
        </div>
      </div>
      <div class="benefit-item">
        <div class="benefit-title">Navigazione Prioritaria:</div>
        <div class="benefit-text">Anche quando la rete è molto trafficata, la tua connessione internet avrà la massima
          priorità.
        </div>
      </div>
      <div class="benefit-item">
        <div class="benefit-title">Interazione Immediata:</div>
        <div class="benefit-text">Con la rapidità del 5G di Optima, il gaming online e le chat diventano incredibilmente
          fluidi, regalandoti un’esperienza coinvolgente e senza interruzioni.
        </div>
      </div>
    </div>
    <hr>
    <div class="sim-info">
      <div class="info-row">
        <span class="info-label">SIM selezionata</span>
        <span class="info-value font-weight-bold">{{ selectedSim?.msisdnId }}</span>
      </div>
      <div class="info-row">
        <span class="info-label">Stato</span>
        <span class="info-value">Attivo</span>
      </div>
      <div class="info-row">
        <span class="info-label">Data attivazione</span>
        <span class="info-value">{{ active5GAddOn?.dataInizioValidita | date : 'dd/MM/yyyy' }}</span>
      </div>
      <div class="info-row">
        <span class="info-label">Costo mensile</span>
        <span class="info-value">{{ active5GAddOn?.canoneMese }} €</span>
      </div>
      <div class="info-row">
        <span
          class="info-label">{{ active5GAddOn?.richiestaInCorso ? 'Data disattivazione prevista' : 'Data rinnovo' }}</span>
        <span class="info-value">{{ selectedSim?.dataProssimoRinnovo | date : 'dd/MM/yyyy' }}</span>
      </div>
    </div>

    <button *ngIf="!active5GAddOn?.richiestaInCorso" class="deactivate-button" (click)="startDeactivation()">
      DISATTIVA 5G
    </button>
  </div>

  <!-- Survey page -->
  <div class="survey-page" *ngIf="viewMode === ViewModeLayout.SURVEY">
    <div class="survey-title">Perché hai deciso di disattivare il servizio 5G?</div>

    <div class="survey-options">
      <div class="survey-option"
           *ngFor="let reason of deactivationReasons"
           [class.selected]="selectedReason === reason.label"
           (click)="selectReason(reason.label)">
        <div class="radio-button" [class.checked]="selectedReason === reason.label"></div>
        <span class="option-text">{{ reason.label }}</span>
      </div>
    </div>

    <div class="survey-buttons">
      <button class="proceed-button"
              [class.disabled]="!isProceedEnabled"
              [disabled]="!isProceedEnabled"
              (click)="proceedToConfirmation()">PROCEDI
      </button>
      <button class="cancel-button" (click)="cancel()">ANNULLA</button>
    </div>
  </div>

  <!-- Confirmation page -->
  <div class="confirmation-page" *ngIf="viewMode === ViewModeLayout.CONFIRMATION">
    <div class="confirmation-text">
      <p>Se disattivi il 5G, non potrai più usufruire della velocità di connessione più elevata.</p>
      <p>Sei sicuro di voler procedere?</p>
    </div>
    <div class="confirmation-buttons">
      <button class="proceed-button" (click)="confirmDeactivation()">PROCEDI</button>
      <button class="cancel-button" (click)="cancel()">ANNULLA</button>
    </div>
  </div>

  <!-- Successful deactivation page -->
  <div class="confirmation-info" *ngIf="viewMode === ViewModeLayout.SUCCESS">
    <div class="icon-container">
      <img src="/assets/img/icons/ok.png" alt="OK" class="icon-ok">
    </div>
    <div class="info-text">
      <div class="title-conformation">La richiesta di disattivazione 5G per la SIM {{ selectedSim?.msisdnId }} è stata
        presa in carico
      </div>
    </div>
    <div class="additional-info">
      <p class="renewal-date">Il servizio resterà disponibile fino al
        <b>{{ selectedSim?.dataProssimoRinnovo | date : 'dd/MM/yyyy' }}</b></p>
    </div>
  </div>
</div>

<app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>
<app-error-window *ngIf="showGeneralErrorWindow" [onClose]="reloadPage"></app-error-window>
