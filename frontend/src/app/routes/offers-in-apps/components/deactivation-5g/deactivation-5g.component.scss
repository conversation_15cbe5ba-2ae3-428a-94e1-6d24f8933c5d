:host {
  background-color: white;
  display: block;
  min-height: 100%;
  color: black;
}

.header {
  background-color: #05C6F2;
  color: white;
  height: 80px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.icon-container {
  margin: 20px 0;
}

.confirmation-info {
  padding-top: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.title-conformation {
  color: black;
  font-size: 22px;
  font-weight: bold;
  max-width: 85vw;
  margin-top: 40px;
}

.icon-ok {
  width: 140px;
  height: 140px;
}

.additional-info {
  margin: 20px 0;
  line-height: 1.5;
  font-size: 15px;
  max-width: 80vw;
}

.renewal-date {
  font-size: 20px;
  margin: 15px 0;
}

// Information page
.icon-5g {
  width: 70px;
  height: 70px;
}

.no-margin {
  margin: 0 !important;
}

.benefits-title {
  font-size: 18px;
  font-weight: bold;
  margin: 10px 0 10px 0;
  color: black;
}

.benefits-box {
  border-radius: 10px;
  padding: 20px;
  max-width: 410px;
}

.benefit-title {
  color: #05C6F2;
  font-weight: bold;
  font-size: 16px;
  margin-top: 15px;
}

.benefit-text {
  font-size: 14px;
  line-height: 1.4;
  color: black;
}

hr {
  border: none;
  border-top: 1px solid #c8c8c8;
  margin: 10px 0;
  width: 410px;
  max-width: 80vw;
}

.sim-info {
  width: 100%;
  max-width: 410px;
  padding: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
}

.info-label {
  font-size: 14px;
  color: black;
}

.info-value {
  font-size: 14px;
  color: black;
}

.deactivate-button, .cancel-button {
  background: transparent;
  color: #05C6F2;
  border: 1px solid #05C6F2;
  border-radius: 25px;
  padding: 6px 30px;
  cursor: pointer;
  margin: 20px 0;
  font-weight: bold;
  width: 155px;
}

// Survey page
.survey-title {
  font-size: 18px;
  font-weight: bold;
  margin: 40px 0 60px 0;
  color: black;
  max-width: 300px;
  line-height: 1.3;
}

.survey-options {
  width: 100%;
  max-width: 300px;
  margin-bottom: 60px;
}

.survey-option {
  display: flex;
  align-items: center;
  padding: 20px 0;
  cursor: pointer;

  &.selected {
    .radio-button {
      background-color: #05C6F2;
      border-color: #05C6F2;

      &::after {
        content: '';
        width: 8px;
        height: 8px;
        background-color: white;
        border-radius: 50%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}

.radio-button {
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-radius: 50%;
  margin-right: 15px;
  position: relative;
  flex-shrink: 0;
}

.option-text {
  font-size: 16px;
  color: black;
}

.survey-buttons, .confirmation-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.proceed-button {
  background: linear-gradient(270deg, #00D1FF 0%, #00B9E3 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 6px 30px;
  cursor: pointer;
  font-weight: bold;
  width: 155px;

  &.disabled {
    background: #b0eefc;
    cursor: not-allowed;
  }
}

// Confirmation page
.confirmation-text {
  margin: 60px 0;
  max-width: 300px;

  p {
    font-size: 16px;
    line-height: 1.4;
    margin-bottom: 20px;
    font-weight: bold;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Success page
.icon-success {
  width: 120px;
  height: 120px;
  background-color: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px auto;

  .checkmark {
    color: white;
    font-size: 60px;
    font-weight: bold;
  }
}

.success-text {
  margin: 40px 0;
  max-width: 350px;

  p {
    font-size: 16px;
    line-height: 1.4;
    margin-bottom: 20px;
    color: black;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
