import {Component, OnInit} from '@angular/core';
import {OffersInAppService} from '../../service/offers-in-app.service';
import {Offer5GResponse, Sim, AddOn} from '../../models/Offer5GModels';
import {select} from "@angular-redux/store";
import {Observable} from "rxjs/Observable";
import {ActivatedRoute} from "@angular/router";
import {IncidentEventService} from "../../../../common/services/incedentEvent/incident-event.service";

enum DeactivationViewMode {
  INFO = 'info',
  SURVEY = 'survey',
  CONFIRMATION = 'confirmation',
  SUCCESS = 'success'
}

@Component({
  selector: 'app-deactivation-5g',
  templateUrl: './deactivation-5g.component.html',
  styleUrls: ['./deactivation-5g.component.scss']
})
export class Deactivation5gComponent implements OnInit {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;

  viewMode: DeactivationViewMode = DeactivationViewMode.INFO;
  ViewModeLayout = DeactivationViewMode;

  selectedSim: Sim;
  showGeneralErrorWindow: boolean;

  selectedReason: string = '';
  deactivationReasons = [
    {value: 'expensive', label: 'Troppo costoso'},
    {value: 'connectivity', label: 'Problemi di connettività'},
    {value: 'other', label: 'Altro'}
  ];

  constructor(private readonly activatedRoute: ActivatedRoute,
              private readonly offerService: OffersInAppService,
              private readonly incidentEventService: IncidentEventService) {
  }

  ngOnInit() {
    const subscriptionId = this.activatedRoute.snapshot.params['subscriptionId'];
    this.loadOffer5GData(localStorage.getItem('clientId'), subscriptionId);
  }

  loadOffer5GData(clientId: string, subscriptionId: string) {
    this.offerService.getOffer5GData(clientId, subscriptionId).subscribe(
      (data: Offer5GResponse) => {
        this.selectedSim = data.sim.find(sim => sim.addOnActive && sim.addOnActive.some(
          addon => addon.codice === '5G'));
      },
      () => {
        this.showGeneralErrorWindow = true;
      }
    );
  }

  startDeactivation() {
    this.viewMode = DeactivationViewMode.SURVEY;
  }

  selectReason(reason: string) {
    this.selectedReason = reason;
  }

  proceedToConfirmation() {
    if (this.selectedReason) {
      this.viewMode = DeactivationViewMode.CONFIRMATION;
    }
  }

  confirmDeactivation() {
    this.incidentEventService.openIncidentForDeactivate5G(this.selectedSim.subscriptionId,
      this.active5GAddOn.codiceOfferta, this.active5GAddOn.canoneMese, this.selectedReason).subscribe(response => {
      if (response.status === 'OK') {
        this.viewMode = DeactivationViewMode.SUCCESS;
      } else {
        this.showGeneralErrorWindow = true;
      }
    });
  }

  cancel() {
    if (this.viewMode === DeactivationViewMode.SURVEY) {
      this.viewMode = DeactivationViewMode.INFO;
    } else if (this.viewMode === DeactivationViewMode.CONFIRMATION) {
      this.viewMode = DeactivationViewMode.SURVEY;
    }
  }

  get active5GAddOn(): AddOn | undefined {
    return this.selectedSim && this.selectedSim.addOnActive &&
      this.selectedSim.addOnActive.find(addon => addon.codice === '5G');
  }

  get isProceedEnabled(): boolean {
    return this.selectedReason !== '';
  }

  get pageTitle(): string {
    switch (this.viewMode) {
      case DeactivationViewMode.INFO:
        return '5G';
      case DeactivationViewMode.SURVEY:
      case DeactivationViewMode.CONFIRMATION:
        return 'Disattiva 5G';
      case DeactivationViewMode.SUCCESS:
        return 'Aggiungi 5G';
      default:
        return '5G';
    }
  }

  reloadPage() {
    this.showGeneralErrorWindow = false;
    window.location.reload();
  }
}
