import {Component, OnInit} from '@angular/core';
import {OffersInAppService} from '../../service/offers-in-app.service';
import {AddOn, Offer5GResponse, Sim} from '../../models/Offer5GModels';
import {select} from "@angular-redux/store";
import {Observable} from "rxjs/Observable";
import {IncidentEventService} from "../../../../common/services/incedentEvent/incident-event.service";
import {ActivatedRoute} from "@angular/router";

enum ViewMode {
  SELECT_NUMBER = 'selectNumber',
  ACTIVATION_WITH_DELAY = 'activationWithDelay',
  ACTIVATION_IMMEDIATE = 'activationImmediate',
  ACTIVATION_CONFIRMATION = 'activationConfirmation',
  ACTIVATION_ALREADY_IN_PROGRESS = 'activationAlreadyInProgress'
}

@Component({
  selector: 'app-offer-5g',
  templateUrl: './offer-5g.component.html',
  styleUrls: ['./offer-5g.component.scss']
})
export class Offer5gComponent implements OnInit {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;
  viewMode: ViewMode;
  ViewModeLayout = ViewMode;
  selectedSim: Sim;
  showModal: boolean;
  isClosing: boolean;
  showGeneralErrorWindow: boolean;
  simList: Sim[] = [];

  constructor(private readonly activatedRoute: ActivatedRoute,
              private readonly offerService: OffersInAppService,
              private readonly incidentEventService: IncidentEventService) {
  }

  ngOnInit() {
    const subscriptionId = this.activatedRoute.snapshot.params['subscriptionId'];
    this.loadOffer5GData(localStorage.getItem('clientId'), subscriptionId);
  }

  loadOffer5GData(clientId: string, subscriptionId: string) {
    this.offerService.getOffer5GData(clientId, subscriptionId).subscribe((data: Offer5GResponse) => {
      this.simList = data.sim;
      if (this.simList.length === 1) {
        this.selectSim(this.simList[0]);
      } else if (this.simList.length > 1) {
        this.viewMode = ViewMode.SELECT_NUMBER;
      }
    }, () => {
      this.showGeneralErrorWindow = true;
    });
  }

  proceed() {
    if (this.selectedSim) {
      this.selectSim(this.selectedSim);
    }
  }

  selectSim(sim: Sim) {
    this.selectedSim = sim;
    const currentAddOn = sim.addOnAviable.find(addon => addon.codice === '5G');
    if (currentAddOn) {
      if (currentAddOn.numRichiesteAttivazioni === 0) {
        this.viewMode = ViewMode.ACTIVATION_IMMEDIATE;
      } else {
        this.viewMode = ViewMode.ACTIVATION_WITH_DELAY;
      }
    }
  }

  get current5GAddOn(): AddOn | undefined {
    return this.selectedSim.addOnAviable.find(addon => addon.codice === '5G');
  }

  openModal(): void {
    if (this.current5GAddOn.richiestaInCorso) {
      this.viewMode = ViewMode.ACTIVATION_ALREADY_IN_PROGRESS;
      return;
    }
    this.showModal = true;
    this.isClosing = false;
  }

  closeModal(): void {
    this.isClosing = true;

    setTimeout(() => {
      this.showModal = false;
      this.isClosing = false;
    }, 500);
  }

  activateNow(): void {
    this.closeModal();
    this.incidentEventService.openIncidentEventoForActivate5GOffer(this.selectedSim.subscriptionId,
      this.current5GAddOn.codiceOfferta, this.current5GAddOn.canoneMese).subscribe(response => {
      if (response.status === 'OK') {
        this.viewMode = ViewMode.ACTIVATION_CONFIRMATION;
      } else {
        this.showGeneralErrorWindow = true;
      }
    });
  }

  reloadPage() {
    window.location.reload();
  }
}
