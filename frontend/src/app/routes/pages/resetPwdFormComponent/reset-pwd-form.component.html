<section class="navBar">
  <div class="col-xs-8 col-sm-6 col-md-4 col-lg-4">
    <div class="logo col-xs-8 col-sm-8 col-md-8 col-lg-8" routerLink="/login">

    </div>
  </div>


  <div class="wrapper">
    <div class="mt-xl containerBlock center-block">
      <section id="form">

        <div class="mainBlock">
          <header *ngIf="!success && !error">
            <b>Inserisci la password</b>
          </header>
          <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 formBlock">

            <section *ngIf="isTokenExpired && !success && !error">
              <header>
                <b>Oooops!</b>
              </header>
              <div class="mainBlock text-error">
                Sembra che il token sia stato utilizzato o scaduto.
              </div>
            </section>


            <section *ngIf="!isTokenExpired && !success && !error">
              <form [formGroup]="valForm" class="form-validate mb-lg inputs-group" role="form" name="loginForm"
                    novalidate=""
                    (submit)="submitForm($event, valForm.value)">

                <div formGroupName="passwords">
                  <div class="form-group has-feedback">
                    <input class="form-control" id="exampleInputPassword1" type="password" name="password"
                           placeholder="Nuova Password" formControlName="password1" required=""/>
                    <!--<span class="fa fa-lock form-control-feedback text-muted"></span>-->
                    <!--<span class="text-danger" *ngIf="valForm.passwords.controls['password1'].hasError('required') && (valForm.passwords.controls['password1'].dirty || valForm.passwords.controls['password1'].touched)">Campo obbligatorio</span>-->
                    <span class="text-danger"
                          *ngIf="valForm['controls'].passwords.controls['password1'].hasError('required') && (valForm['controls'].passwords.controls['password1'].dirty || valForm['controls'].passwords.controls['password1'].touched)">Campo obbligatorio</span>
                    <span class="text-danger"
                          *ngIf="valForm['controls'].passwords.controls['password1'].hasError('pattern') && (valForm['controls'].passwords.controls['password1'].dirty || valForm['controls'].passwords.controls['password1'].touched)">Le password devono contenere almeno 8 caratteri e almeno un numero, una lettera maiuscola e una minuscola</span>
                  </div>
                  <div class="form-group has-feedback">
                    <input class="form-control" id="exampleInputPassword2" type="password" name="password"
                           placeholder="Ripeti Password" formControlName="password2" required=""/>
                    <span class="text-danger"
                          *ngIf="valForm['controls'].passwords.controls['password2'].hasError('required') && (valForm['controls'].passwords.controls['password2'].dirty || valForm['controls'].passwords.controls['password2'].touched)">Campo obbligatorio</span>
                    <span class="text-danger"
                          *ngIf="valForm['controls'].passwords.controls['password2'].hasError('pattern') && (valForm['controls'].passwords.controls['password2'].dirty || valForm['controls'].passwords.controls['password2'].touched)">Le password devono contenere almeno 8 caratteri e almeno un numero, una lettera maiuscola e una minuscola</span>
                    <div><span class="text-danger" *ngIf="valForm['controls'].passwords?.errors?.mismatch">
    Le password non coincidono
      </span></div>
                  </div>
                </div>
                <div class="button-position">
                  <span *ngIf="errorComparative" class="text-danger padding-bottom">La password deve essere diversa da quella precedente</span>
                  <button class="submit-button" type="submit">PROSEGUI</button>
                </div>
<!--                <button class="submit mt-lg" type="submit">
            <span class="submitArrow">
            <i class="fa fa-arrow-right fa-2x" aria-hidden="true"> </i>
            </span>
                  &lt;!&ndash;<span class="prosegui">prosegui</span>&ndash;&gt;
                </button>-->
              </form>
            </section>

            <section *ngIf="(success || error)">
              <div *ngIf="success">
                <header>
                  <b>La password è cambiata con successo.</b>
                </header>
                <div class="mainBlock button-position">
                  <button class="submit-button" routerLink="/login">Accedi all'area clienti
<!--            <span class="submitSuccess">

            <i class="fa fa-arrow-right fa-2x" aria-hidden="true"> </i>
            </span>-->
                    <!--<span class="prosegui">prosegui</span>-->
                  </button>
                </div>

              </div>
              <div *ngIf="error">
                <header>
                  <b>Oooops!</b>
                </header>
                <div class="mainBlock text-error">
                  Qualcosa è andato storto. Riprova più tardi.
                </div>
              </div>
            </section>


          </div>
        </div>
      </section>

    </div>
  </div>

</section>
<app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>
