import {Component, OnInit} from '@angular/core';
import {MatDialog} from '@angular/material';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {SettingsService} from '../../../core/settings/settings.service';
import {LoginAlertComponent} from '../login/login.component';
import {UserActions} from '../../../redux/user/actions';
import {HttpService} from '../../../services/http.service';
import {UserDetails} from '../../../common/model/UserDetails';
import {AuthService} from '../../../services/auth/auth.service';
import {APP_LOCAL_STORAGE_KEYS} from '../../../shared/app.constant';

@Component({
  selector: 'app-admin-login',
  templateUrl: './admin-login.component.html',
  styleUrls: ['./admin-login.component.scss']
})
export class AdminLoginComponent implements OnInit {

  isShowSpinner = false;
  valForm: FormGroup;
  router: Router;
  public mess;

  constructor(

    public dialog: MatDialog,
    public settings: SettingsService,
    fb: FormBuilder,
    private _router: Router,
    private httpService: HttpService,
    private userActions: UserActions,
    private authService: AuthService
  ) {
    this.router = _router;
    this.valForm = fb.group({
      'username': [null, Validators.required],
      'password': [null, Validators.required],
      'userId' : [null, Validators.required]
    });

  }


  login(fields) {
    this.isShowSpinner = true;
    this.authService.loginAdmin(fields).subscribe(
      (data: any) => {
        localStorage.setItem(APP_LOCAL_STORAGE_KEYS.accessToken, data.accessToken);
        this.userActions.setUerDetails(data as UserDetails);
        this.isShowSpinner = false;
      },
      (error: any) => {
        this.openDialog();
        this.isShowSpinner = false;
      }
    );
  }

  openDialog(): void {
    const dialogRef = this.dialog.open(LoginAlertComponent, {
      width: '250px',
      data: 'Test text'
    });
    dialogRef.afterClosed().subscribe(result => {
    });
  }


  submitForm($ev, value: any) {
    $ev.preventDefault();

    Object.keys(this.valForm.controls).forEach((c) => {
      this.valForm.controls[c].markAsTouched();
    });

    if (this.valForm.valid) {
      this.settings.app.username = value.username;
      localStorage.clear();
      localStorage.setItem('clientUserName', value.username);
      this.login(value);

    }
  }

  ngOnInit() {
    this.httpService.get('/isAdmin', '') .subscribe(
      (respose: any) => {
      },
      (error: any) => {
        this._router.navigate(['/login']);
      }
    );
  }

}
