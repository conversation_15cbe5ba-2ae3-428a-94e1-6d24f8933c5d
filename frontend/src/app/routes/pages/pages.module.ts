import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {HttpClientModule} from '@angular/common/http';
import {SharedModule} from '../../shared/shared.module';
import {LoginAlertComponent, LoginComponent} from './login/login.component';
import {LoginV2Component} from './login-v2/login-v2.component';
import {LoginV3Component} from './login-v3/login-v3.component';
import {RecoverComponent} from './recover/recover.component';
import {LockComponent} from './lock/lock.component';
import {MaintenanceComponent} from './maintenance/maintenance.component';
import {Error404Component} from './error404/error404.component';
import {Error500Component} from './error500/error500.component';
import {HttpService} from '../../services/http.service';
import {PaymentFailedComponent} from './payment/payment-failed/payment-failed.component';
import {NavigationComponent} from '../../layout/navigation/navigation.component';
import {UserActions} from '../../redux/user/actions';
import {CommonModule} from '../../common/common.module';
import {ResetPwdComponent} from './reset-pwd/reset-pwd.component';
import {ForgotPasswordService} from '../../common/services/forgotPassword/forgot-password.service';
import {AdminLoginComponent} from './admin-login/admin-login.component';
import {NotificaMdpService} from '../../common/services/notifica-mdp/notifica-mdp.service';
import {DialogModalEntity} from '../../common/model/dialogModal/DialogModalEntity';
import {OkPageComponent} from './paypal/ok-page/ok-page.component';
import {KoPageComponent} from './paypal/ko-page/ko-page.component';
import {ResetPwdFormComponent} from './resetPwdFormComponent/reset-pwd-form.component';
import {ProspectRegisterComponent} from './prospect/prospect-register/prospect-register.component';
import {ProspectLoginComponent} from './prospect/prospect-login/prospect-login.component';
import {ProspectUserService} from '../../common/services/prospect-user/prospect-user.service';
import {RegistrationStatusComponent} from './prospect/registration-status/registration-status.component';
import {ProspectResetPasswordComponent} from './prospect/prospect-reset-password/prospect-reset-password.component';
import {ProspectEnterNewPasswordComponent} from './prospect/prospect-enter-new-password/prospect-enter-new-password.component';

/* Use this routes definition in case you want to make them lazy-loaded */
const routes: Routes = [
  {path: 'login', component: LoginComponent},
  {path: 'login/v2', component: LoginV2Component},
  {path: 'login/v3', component: LoginV3Component},
  {
    path: 'payment', component: NavigationComponent,
    children:
      [
        {
          path: '', component: PaymentFailedComponent
        }
      ]
  },
  {path: 'paypal/ok', component: OkPageComponent},
  {path: 'paypal/ko', component: KoPageComponent},
  {
    path: 'forgotPassword', component: ResetPwdComponent,
    children:
      [
        {
          path: ':email', component: ResetPwdComponent
        }
      ]
  },
  {path: 'admin', component: AdminLoginComponent},
  {path: 'api/resetPassword/:token', component: ResetPwdFormComponent},
  {
    path: 'prospect',
    children: [
      {
        path: 'registration', component: ProspectRegisterComponent
      },
      {
        path: 'login', component: ProspectLoginComponent
      },
      {
        path: 'forgotPassword', component: ProspectResetPasswordComponent
      },
      {
        path: 'registration-status', component: RegistrationStatusComponent
      },
      {
        path: 'resetPassword/:token', component: ProspectEnterNewPasswordComponent
      }
    ]
  }
];

@NgModule({
  imports: [
    CommonModule,
    HttpClientModule,
    SharedModule,
    RouterModule.forChild(routes),
  ],
  providers: [HttpService, UserActions, ForgotPasswordService, NotificaMdpService, DialogModalEntity, ProspectUserService],
  declarations: [
    LoginComponent,
    LoginAlertComponent,
    LoginV2Component,
    RecoverComponent,
    LockComponent,
    MaintenanceComponent,
    Error404Component,
    Error500Component,
    PaymentFailedComponent,
    ResetPwdComponent,
    ResetPwdFormComponent,
    AdminLoginComponent,
    LoginV3Component,
    OkPageComponent,
    KoPageComponent,
    ProspectRegisterComponent,
    ProspectRegisterComponent,
    ProspectLoginComponent,
    RegistrationStatusComponent,
    ProspectResetPasswordComponent,
    ProspectEnterNewPasswordComponent
  ],
  entryComponents: [
    LoginComponent,
    LoginAlertComponent,
    LoginV2Component
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [
    RouterModule,
    LoginComponent,
    LoginAlertComponent,
    LoginV2Component,
    RecoverComponent,
    LockComponent,
    MaintenanceComponent,
    Error404Component,
    Error500Component,
    ResetPwdFormComponent,
    PaymentFailedComponent
  ]
})
export class PagesModule {
}
