.navBar {
  background-color: white;
}

.wrapper {
  background: transparent linear-gradient(91deg, #00B8FF 0%, #0035AD 100%) 0 0 no-repeat padding-box;
}

.logo {
  height: 50px;
  width: 50%;
  background: url("../../../../assets/img/logo/optima_new_main_logo.svg") no-repeat top left;
  background-size: contain;
  cursor: pointer;
  margin: 15px 25%;
}

header {
  color: #00A9EA;
  margin-bottom: 5px;
  font-size: 20px;
  text-align: center;
  padding: 3% 20px 0;

}

.mainBlock {
  background: white;
  border-radius: 5px;
  display: inline-block;
  padding: 30px 10px;
  width: 100%;
}

.containerBlock {
  width: 670px;
}

.text {
  color: #6e6e6e;
  text-align: center;
  font-size: 19px;
  padding: 5%;
}

label {
  clear: both;
  color: #36749D;
}

.hide {
  display: none;
}

.successText {
  color: #36749D;
  width: 70%;
  margin: 20px auto;
  text-align: center;
  font-size: 15px;
}

.inputs-group {
  width: 55%;
  margin: auto;
}

.input-section {
  border-radius: 5px;
  height: 40px;
  margin-bottom: 25px
}

.captcha-block {
  text-align: -webkit-center;
  margin-bottom: 25px;
}

.submit-button {
  background: #97B825 0 0 no-repeat padding-box;
  border: 1px solid #FFFFFF;
  border-radius: 100px;
  width: 40%;
  height: 40px;
  font-size: 14px;
  color: white;
  margin: 0 auto;
  display: block;
}

.text-danger {
  padding-left: 3%;
}

.position {
  margin-bottom: -20px;
}

@media only screen and (max-width: 768px) {
  .containerBlock {
    width: 90%;
  }
  .text {
    font-size: 17px;
  }
  .submit-button {
    font-size: 10px;
    width: 50%;
  }
  .inputs-group {
    width: 65%;
  }
}

@media only screen and (max-width: 570px) {
  .captcha-block {
    transform: scale(0.75);
  }
}

@media only screen and (max-width: 525px) {
  .captcha-block {
    margin-right: 185px;
  }
}

@media only screen and (max-width: 440px) {
  .captcha-block {
    margin-right: 195px;
    transform: scale(0.69);
  }
}

@media only screen and (max-width: 376px) {
  .form-control {
    width: 112%;
  }
}

@media only screen and (max-width: 321px) {
  .captcha-block {
    transform: scale(0.59);
  }
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 64%;
  margin: auto;
  padding-top: 25px;
  font-size: 20px;
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  min-height: 300px;
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 30px;
  border: 2px solid #b1c5df;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 560px;
}

.display {
  display: block;
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 50px;
  top: 15px;
  color: #36749d;
  cursor: pointer;
}

.users {
  border: 2px solid #b1c5df;
  border-radius: 10px;
  text-align: center;
  width: 50%;
  color: #36749d;
  cursor: pointer;
  margin: 5% auto auto;
  padding: 3%;
  font-size: 15px;
}

.modal-image {
  text-align: center;
  background-color: white;
  margin: 3% auto auto;
  width: 20%;
  display: block;
}
