import { Component, OnInit, Injector } from '@angular/core';
import { SettingsService } from '../../../core/settings/settings.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-lock',
  templateUrl: './lock.component.html',
  styleUrls: ['./lock.component.scss']
})
export class LockComponent implements OnInit {

  valForm: FormGroup;
  router: Router;

  constructor(public settings: SettingsService, fb: FormBuilder, public injector: Injector) {

    this.valForm = fb.group({
      'password': [null, Validators.required]
    });

  }

  submitForm($ev, value: any) {
    $ev.preventDefault();
    Object.keys(this.valForm.controls).forEach((key) => {
      this.valForm.controls[key].markAsTouched();
    });
    if (this.valForm.valid) {
      this.router.navigate(['home']);
    }
  }

  ngOnInit() {
    this.router = this.injector.get(Router);
  }

}
