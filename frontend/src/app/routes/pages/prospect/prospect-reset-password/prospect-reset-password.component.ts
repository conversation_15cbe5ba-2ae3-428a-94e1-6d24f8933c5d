import {Component, OnInit} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {FormUtils} from '../../../../common/utils/FormUtils';
import {ForgotPasswordRequest} from '../../../../common/model/forgotPassword/forgotPasswordRequest';
import {ProspectUserService} from '../../../../common/services/prospect-user/prospect-user.service';

declare let grecaptcha: any;

@Component({
  selector: 'app-prospect-reset-password',
  templateUrl: './prospect-reset-password.component.html',
  styleUrls: ['./prospect-reset-password.component.scss']
})
export class ProspectResetPasswordComponent implements OnInit {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;
  emailForm: FormGroup;
  captchaError = false;
  noSuchEmail: boolean;
  showSuccessWindow = false;

  constructor(fb: FormBuilder, private prospectUserService: ProspectUserService) {
    this.emailForm = fb.group({
      'email': [null, Validators.required],
    });
  }

  ngOnInit() {
  }

  hideError() {
    this.captchaError = false;
  }

  submitForm($ev) {
    $ev.preventDefault();
    this.captchaError = grecaptcha.getResponse().length === 0;
    if (this.emailForm.valid && !this.captchaError) {
      const request = new ForgotPasswordRequest();
      request.email = this.emailForm.controls.email.value;
      this.prospectUserService.restorePassword(request).subscribe(response => {
        if (response.status === 200) {
          this.showSuccessWindow = true;
        } else {
          this.noSuchEmail = true;
        }
      });
    } else {
      FormUtils.setFormControlsAsTouched(this.emailForm);
    }
  }

  hideDialogModal() {
    this.noSuchEmail = false;
    this.showSuccessWindow = false;
  }
}
