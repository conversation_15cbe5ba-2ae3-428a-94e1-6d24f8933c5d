.navBar{
  background-color: white;
}
.wrapper{
  background: transparent linear-gradient(91deg, #00B8FF 0%, #0035AD 100%) 0 0 no-repeat padding-box;
}
.logo {
  height: 50px;
  width: 50%;
  background: url("/assets/img/logo/optima_new_main_logo.svg") no-repeat top left;
  background-size: contain;
  cursor: pointer;
  margin: 15px 25%;
}
header{
  color: #00A9EA;
  margin-bottom: 5px;
  font-size: 20px;
  text-align: center;
  padding: 3% 20px 5%;
}
.mainBlock{
  background: white;
  border-radius: 5px;
  display: inline-block;
  padding: 30px 10px;
  width: 100%;
}

.containerBlock{
  width: 670px;
}
.text{
  color: #36749D;
}

.text-error{
  color: #6e6e6e;
  text-align: center;
  font-size: 18px;
  margin-top: -5%;
}
label{
  clear: both;
  color: #36749D;
}
.fa-arrow-right{
  color: white;
  background-color:#36749D;
  border-radius: 50px;
  padding: 8px 10px;
}
button{
  background: none;
  border: none;
  float: right;
}
.prosegui{
  padding-left: 10px;
  color: #36749D;
}
.submitArrow:after {
  line-height: 30px;
  content: "Salva!";
  color: #36749D;
  padding-left: 10px;
}
.submitSuccess:after {
  line-height: 30px;
  content: "login";
  color: #36749D;
  padding-left: 10px;
}
.hide{
  display: none;
}

.successText{
  color: #36749D;
  width: 70%;
  margin: 10px auto;
  text-align: center;
}

.inputs-group {
  width: 75%;
  margin: auto;
}

.form-group{
  margin-bottom: 20px;
}

.submit-button {
  background: #97B825 0 0 no-repeat padding-box;
  border: 1px solid #FFFFFF;
  border-radius: 100px;
  width: 40%;
  height: 40px;
  font-size: 14px;
  color: white;
  margin: 0 auto;
  display: block;
}

.button-position{
  display: flex;
  justify-content: center;
}

@media only screen and (max-width: 768px) {
  .containerBlock{
    width: 90%;
  }
  .text{
    width: 100%;
  }
  .mainBlock{
    width: 100%;
  }
  .formBlock{
    border: none;
    width: 100%;
  }
  .submit-button {
    font-size: 10px;
    width: 50%;
  }
}
