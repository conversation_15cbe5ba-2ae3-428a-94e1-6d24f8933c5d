<section class="navBar">
  <div class="col-xs-8 col-sm-6 col-md-4 col-lg-4">
    <div class="logo col-xs-8 col-sm-8 col-md-8 col-lg-8" routerLink="/login">
    </div>
  </div>

  <div class="wrapper">
    <div class="mt-xl containerBlock center-block">
      <section id="form">

        <div class="mainBlock">
          <header *ngIf="!success && !error">
            <b>Inserisci la password</b>
          </header>
          <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 formBlock">

            <section *ngIf="isTokenExpired && !success && !error">
              <header>
                <b>Oooops!</b>
              </header>
              <div class="mainBlock text-error">
                Sembra che il token sia stato utilizzato o scaduto.
              </div>
            </section>


            <section *ngIf="!isTokenExpired && !success && !error">
              <form [formGroup]="passwordsForm" class="form-validate mb-lg inputs-group" role="form" name="loginForm"
                    novalidate=""
                    (submit)="submitForm($event)">

                <div formGroupName="passwords">
                  <div class="form-group has-feedback">
                    <input class="form-control" id="exampleInputPassword1" type="password" name="password"
                           placeholder="Nuova Password" formControlName="password1"/>
                    <span class="text-danger"
                          *ngIf="passwordsForm.get('passwords.password1').hasError('required') && (passwordsForm.get('passwords.password1').dirty
                          || passwordsForm.get('passwords.password1').touched)">Campo obbligatorio</span>
                    <span class="text-danger"
                          *ngIf="passwordsForm.get('passwords.password1').hasError('pattern') && (passwordsForm.get('passwords.password1').dirty
                          || passwordsForm.get('passwords.password1').touched)">
                      Le password devono contenere almeno 8 caratteri e almeno un numero, una lettera maiuscola e una minuscola</span>
                  </div>
                  <div class="form-group has-feedback">
                    <input class="form-control" id="exampleInputPassword2" type="password" name="password"
                           placeholder="Ripeti Password" formControlName="password2"/>
                    <span class="text-danger"
                          *ngIf="passwordsForm.get('passwords.password2').hasError('required') && (passwordsForm.get('passwords.password2').dirty
                          || passwordsForm.get('passwords.password2').touched)">Campo obbligatorio</span>
                    <span class="text-danger"
                          *ngIf="passwordsForm.get('passwords.password2').hasError('pattern') && (passwordsForm.get('passwords.password2').dirty
                          || passwordsForm.get('passwords.password2').touched)">
                      Le password devono contenere almeno 8 caratteri e almeno un numero, una lettera maiuscola e una minuscola</span>
                    <div>
                      <span class="text-danger" *ngIf="passwordsForm['controls'].passwords?.errors?.mismatch">Le password non coincidono</span>
                    </div>
                  </div>
                </div>
                <div class="button-position">
                  <button class="submit-button" type="submit">PROSEGUI</button>
                </div>
              </form>
            </section>

            <section *ngIf="(success || error)">
              <div *ngIf="success">
                <header>
                  <b>La password è cambiata con successo.</b>
                </header>
                <div class="mainBlock button-position">
                  <button class="submit-button" routerLink="/prospect/login">Accedi all'area clienti</button>
                </div>

              </div>
              <div *ngIf="error">
                <header>
                  <b>Oooops!</b>
                </header>
                <div class="mainBlock text-error">
                  Qualcosa è andato storto. Riprova più tardi.
                </div>
              </div>
            </section>


          </div>
        </div>
      </section>

    </div>
  </div>
</section>
<app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>
