<div class="wrapper-block">

  <form class="login-form" [formGroup]="formGroupLogin"
        (submit)="login($event)">
    <img class="img-logo" src="/assets/img/logo/optima_new_main_logo.svg" alt="Logo">
    <div class="text-header">Accedi per seguire tutti gli aggiornamenti, in tempo reale, sullo stato di lavorazione del
      tuo contratto
      Optima.
    </div>

    <label>Codice Fiscale o Partita IVA</label>
    <input type="text" formControlName="username"/>
    <span class="text-danger" *ngIf="formGroupLogin.get('username').hasError('required')
      && (formGroupLogin.get('username').dirty || formGroupLogin.get('username').touched)">Campo obbligatorio</span>

    <label>Password</label>
    <input type="password" formControlName="password"/>
    <span class="text-danger" *ngIf="formGroupLogin.get('password').hasError('required')
      && (formGroupLogin.get('password').dirty || formGroupLogin.get('password').touched)">Campo obbligatorio</span>
    <span class="text-danger show error-text"
          *ngIf="showLoginError">Codice Fiscale o Partita IVA o password errati</span>
    <span class="text-danger show error-text"
          *ngIf="showNoSuchContract">Non abbiamo trovato alcun contratto relativo al specificato Codice Fiscale o Partita IVA</span>

    <button class="btn-submit" type="submit">ACCEDI</button>
  </form>
</div>

<div class="modal-div show" *ngIf="showChooseContractModalWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow()"></i>
    <div class="modal-text">Abbiamo più contratti in lavorazione associati al tuo indirizzo-email, con quale vuoi
      procedere?
    </div>
    <div class="modal-block" *ngFor="let contract of contractsDetails" [formGroup]="formGroupContract">
      <input type="radio" name="option" formControlName="option" value="{{contract.idGruppo}}"/>
      <div>
        <div class="text-inline">
          <div>ID Contratto: <b>{{contract.idGruppo}}</b></div>
          <div style="margin-left: 20px;">Data stipula: <b>{{contract.dataStipula | date:'dd/MM/yyyy'}}</b></div>
        </div>
        <div class="container-images">
          <img *ngIf="contract.luce" class="img-icon" src="assets/img/small-icons-services/icon_luce.png" alt="Luce"/>
          <img *ngIf="contract.gas" class="img-icon" src="assets/img/small-icons-services/icon_gas.png" alt="Gas"/>
          <img *ngIf="contract.fisso" class="img-icon" src="assets/img/small-icons-services/icon_fisso.png"
               alt="Fisso"/>
          <img *ngIf="contract.internet" class="img-icon" src="assets/img/small-icons-services/icon_internet.png"
               alt="Internet"/>
          <img *ngIf="contract.mobile" class="img-icon" src="assets/img/small-icons-services/icon_mobile.png"
               alt="Mobile"/>
          <img *ngIf="contract.teleconsulto" class="img-icon" src="assets/img/small-icons-services/icon_teleconsulto.png"
               alt="Teleconsulto"/>
          <img *ngIf="contract.assistenza" class="img-icon" src="assets/img/small-icons-services/icon_assistenza.png"
               alt="Assistenza"/>
          <img *ngIf="contract.consulenzaLegale" class="img-icon" src="assets/img/small-icons-services/icon_consulenza_legale.png"
               alt="Consulenza legale"/>
        </div>
      </div>
    </div>
    <span class="text-danger show text-center" style="margin-bottom: 15px" *ngIf="formGroupContract.get('option').hasError('required')
      && (formGroupContract.get('option').dirty || formGroupContract.get('option').touched)">Campo obbligatorio</span>
    <button class="btn-modal" (click)="chooseContract()">PROSEGUI</button>
  </div>
</div>
<app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>
