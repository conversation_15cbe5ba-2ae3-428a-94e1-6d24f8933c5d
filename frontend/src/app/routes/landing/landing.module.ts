import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MyoptimaLayoutComponent } from './containers/myoptima-layout/myoptima-layout.component';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [{
  path: '',
  component: MyoptimaLayoutComponent
}];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes)
  ],
  declarations: [MyoptimaLayoutComponent]
})
export class LandingModule {
}
