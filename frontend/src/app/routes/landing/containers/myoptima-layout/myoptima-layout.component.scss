.imgRight {
  object-fit: contain;
  object-position: top left;
  float: right;
  width: 57.8%;
  height: auto;
}

.imgLeft {
  object-fit: contain;
  object-position: top right;
  float: left;
  width: 100%;
  height: auto;
}

.container {
  width: 100%;
}

@media (max-width: 640px) {
  .imgRight {
    float: center;
    width: 100%;
    height: auto;
  }
  .imgLeft {
    object-position: center;
    float: center;
    width: 100%;
    height: auto;
  }
}

area {
  cursor: pointer;
}
