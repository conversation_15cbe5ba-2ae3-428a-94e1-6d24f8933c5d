<div class="product-title coral">
  <div class="icon-{{(routerParams | async)?.service}}-blu service-icon night-blue"></div>
  Attiva il servizio {{(routerParams | async)?.service?.toUpperCase()}} con Optima!
</div>
<p class="product-description night-blue">
  <span>Con Optima avrai una sola bolletta chiara e semplice con il dettaglio di tutti i tuoi consumi di casa o ufficio.</span>
  <span>Dimentica la moltitudine di fogli e bollettini sparsi per casa o attaccati al frigo.</span>
  <span>Per tutti i tuoi servizi riceverai una sola bolletta e avrai Optima come unico interlocutore.</span>
  <span class="activation-suggestion">
    <span class="font-weight-bold">Attivare un nuovo servizio è semplicissimo:</span>
    ti basterà un click per essere ricontattato e per ricevere tutti i dettagli su come procedere.</span>
</p>

<p class="product-question font-weight-bold night-blue">
  CHE ASPETTI? <span class="coral">PASSA ALLA FORNITURA OPTIMA PER TUTTE LE TUE UTENZE!</span></p>

<p class="font-weight-bold button-label night-blue">Scopri come attivare tutti i tuoi servizi con Optima!</p>
<!--<p class="text-align-middle">-->
  <!--<button class="btn btn-success request-button" (click)="sendIntegratedSolutionRequest()">RICHIEDI RICONTATTO</button>-->
<!--</p>-->

