@import "src/app/shared/styles/colors";

.padding {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  min-height: 37vh;
}

label {
  font-weight: 100;
  font-size: 15px;
}

.form-control {
  font-weight: bold;
  color: $dark-blue;
  border: 1px none;
  border-bottom-style: solid;
  padding-left: 1px;
}

select {
  -webkit-appearance: none; /*Removes default chrome and safari style*/
  -moz-appearance: none;
  background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
  background-position-x: 95%;
}

.tumbler {
  width: 70px;
  height: 34px;
  background: #F4F4F4 0 0 no-repeat padding-box;
  box-shadow: inset 0 3px 6px #00000029;
  border-radius: 50px;
  padding: 2px;
  cursor: pointer;
}

.on {
  width: 30px;
  float: right;
}

.off {
  width: 30px;
}

.line {
  border: 0.5px solid #B0C7DD;
  background-color: #B0C7DD;
  margin: 25px 0 25px 15px;
}

.payment-method {
  display: flex;
  align-items: center;

  .payment-icon {
    width: 50px;
    margin-right: 10px;
  }

  .text-position {
    margin-right: 25px;
  }

  .color-text {
    color: black;
  }

  .action-icons {
    width: 45px;
    cursor: pointer;
  }
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.95);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  min-height: 370px;
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 30px;
  border: 2px solid #b1c5df;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 560px;
}

.display {
  display: block;
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 50px;
  top: 15px;
  color: #36749d;
  cursor: pointer;
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 85%;
  margin: auto;
  font-size: 20px;
}

.header-modal-text {
  font-size: 30px
}

button {
  border-radius: 10px;
  border: 1px solid #36749A;
  padding: 5px 15px;
  color: white;
  display: inline-block;
  margin-top: 2%;
  background: #36749A;
  font-size: 12px;
}

.flex-modal-inside {
  display: flex;
  flex-direction: column;
  align-items: center;

  .auto-ricarica-img {
    width: 64px;
    margin-top: 15px;
    margin-bottom: 35px;
  }

  .flex-buttons {
    display: flex;
    margin-top: 50px;

    .modal-window-confirm-button {
      font-size: 15px;
      width: 115px;
      height: 40px;
    }

    .modal-window-annulla-button {
      font-size: 15px;
      width: 100px;
      height: 40px;
      background-color: white;
      color: #36749A;
      margin-right: 40px;
    }
  }
}
