import {Component, OnInit} from '@angular/core';
import {TopUpSimWithCreditCardLayoutComponent} from '../top-up-sim-with-credit-card-layout/top-up-sim-with-credit-card-layout.component';
import {AbstractControl, FormControl, FormGroup, Validators} from '@angular/forms';
import Validator from '../../../../../common/utils/Validator';
import {FormUtils} from '../../../../../common/utils/FormUtils';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {ContractRecord} from '../../../../../common/model/mobile/contract-record/ContractRecord';
import {TopUpSimWithVoucherCodeRequest} from '../../../../../common/model/mobile/TopUpSimWithVoucherCodeRequest';
import {DialogModalEntity} from '../../../../../common/model/dialogModal/DialogModalEntity';

@Component({
  selector: 'app-top-up-sim-sisal-layout',
  templateUrl: './top-up-sim-sisal-layout.component.html',
  styleUrls: ['./top-up-sim-sisal-layout.component.scss']
})
export class TopUpSimSisalLayoutComponent extends TopUpSimWithCreditCardLayoutComponent implements OnInit {

  @select(['mobile'])
  $clientMobileContracts: Observable<any>;
  clientMobileContracts: Array<ContractRecord>;

  formGroupSisal: FormGroup;

  ngOnInit() {
    this.formGroupSisal = this.buildForm();
    this.$clientMobileContracts.subscribe(response => {
      this.clientMobileContracts = response.contractRecords;
    });
  }

  buildForm() {
    const formGroup = this.fb.group({
      msisdnId: [null, [Validators.required]],
      optimaNumber: [null, [Validators.required, Validator.withLength(6, 10)]],
      voucherCode: [null, [Validators.required, Validators.pattern( /^[0-9]{12}$/)]]
    });
    this.setCommonFormGroupConfiguration(formGroup);
    return formGroup;
  }

  replenishAccount() {
    if (this.formGroupSisal.valid) {
      const formData = this.formGroupSisal.value;
      if (formData.msisdnId) {// msisdnId is sim number
        const currentContract = this.clientMobileContracts.filter(record => record.msisdnId === formData.msisdnId)[0];
        this.sendRequestToTopUpWithVoucher(localStorage.getItem('clientId'), currentContract.id, formData.voucherCode);
      } else {
        this.mobileService.loadContractRecordsBySimNumber('39' + formData.optimaNumber).subscribe(contractRecords => {
          if (contractRecords && contractRecords.length) {
            this.sendRequestToTopUpWithVoucher(localStorage.getItem('clientId'), contractRecords[0].id, formData.voucherCode);
          } else {
            this.formGroupSisal.controls['optimaNumber'].setErrors({'numberExistence': true});
            return;
          }
        });
      }
    } else {
      FormUtils.setFormControlsAsTouched(this.formGroupSisal);
    }
  }

  sendRequestToTopUpWithVoucher(idCliente, idSubscription, idVoucher) {
    const request = new TopUpSimWithVoucherCodeRequest();
    request.idCliente = idCliente;
    request.idSubscription = idSubscription;
    request.idVoucher = idVoucher;
    this.mobileService.topUpSimWithVoucherCode(request).subscribe(response => {
      if (response !== null) {
        if (response === 0) {
          this.dialogModalActions
            .showDialogModal({title: 'La Ricarica è stata effettuata.', img: '/assets/img/icons/ok.png'} as DialogModalEntity);
        } else if (response === 1) {
          this.formGroupSisal.controls['voucherCode'].setErrors({'invalidVoucher': true});
        } else if (response === 4) {
          this.formGroupSisal.controls['voucherCode'].setErrors({'usedVoucher': true});
        }
      }
    });
  }
}
