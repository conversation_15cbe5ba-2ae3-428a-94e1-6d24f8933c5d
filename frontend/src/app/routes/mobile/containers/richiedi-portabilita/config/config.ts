import {DialogModalEntity} from '../../../../../common/model/dialogModal/DialogModalEntity';

export function infoModalRequestNoSiSendEmailRichiestaPortabilita(email: string): DialogModalEntity {
  return {
    text: `Non è possibile procedere con la richiesta di portabilità nel caso in cui la SIM è stata rubata o smarrita.`,
    img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png',
  } as DialogModalEntity;
}

export function infoModalRequestSiSiSendEmailRichiestaPortabilita(email: string): DialogModalEntity {
  return {
    text: `Non è possibile procedere con la richiesta di portabilità nel caso in cui la SIM è stata rubata o smarrita.`,
    img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png',
  } as DialogModalEntity;
}

export function infoModalRequestSiSiRichiestaPortabilita(): DialogModalEntity {
  return {
    text:
      `Non è possibile procedere con la richiesta di portabilità nel caso in cui la SIM è stata rubata o smarrita.`,
    img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione_Dark.png',
  } as DialogModalEntity;
}

export function infoModalRequestNoNoSendEmailRichiestaPortabilita(email: string): DialogModalEntity {
  return {
    text:
      `Per procedere alla richiesta di portabilità è necessario inoltrare al Servizio Clienti Optima il “Modulo ` +
      `MNP” e il documento d’identità dell’attuale intestatario del numero da portare. Abbiamo inoltrato il modulo al tuo indirizzo e-mail: ${email}`,
    img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png',
  } as DialogModalEntity;
}

export function infoModalRequestNoNoRichiestaPortabilita(): DialogModalEntity {
  return {
    text:
      `Per procedere alla richiesta di portabilità è necessario inoltrare al Servizio Clienti Optima il ` +
      `“Modulo MNP” disponibile nella sezione “Moduli” della tua Area Clienti e allegare il documento d’identità dell’attuale ` +
      `intestatario del numero da portare.`,
    img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione_Dark.png',
  } as DialogModalEntity;
}

export function infoModalRequestRichiestaPortabilita(): DialogModalEntity {
  return {
    text: `Gentile Cliente, sulla SIM selezionata risulta già una richiesta di portabilità.`,
    img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png',
  } as DialogModalEntity;
}

export function infoModalRequestNoSiRichiestaPortabilita(): DialogModalEntity {
  return {
    text:
      `Per procedere alla richiesta di portabilità è necessario inoltrare al Servizio Clienti Optima il ` +
      `“Modulo MNP” disponibile nella sezione “Moduli” della tua Area Clienti e allegare il documento d’identità dell’attuale ` +
      `intestatario del numero, insieme alla denuncia di furto/smarrimento del numero da portare.`,
    img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione_Dark.png',
  } as DialogModalEntity;
}


