<div class="container">
  <form [formGroup]="formGroup">
    <div class="row row-right small-row col-lg-6 col-md-6 col-sm-12">
      <div class="recharge-sim-title col-lg-12 col-md-12 col-sm-12">
        Dati dell’attuale intestatario del numero da portare in Optima
      </div>
      <div class="label-position col-lg-8 col-md-8 col-sm-12">Nome</div>
      <div *ngIf="!isMobile" class="label-position col-lg-4 col-md-4 col-sm-12" style="left: -33px;">Cognome</div>
      <div class="filled-fields-left col-lg-5 col-md-5 col-sm-12">{{userData?.deputy?.firstName}}</div>
      <div *ngIf="isMobile" class="label-position col-lg-8 col-md-8 col-sm-12">Cognome</div>
      <div class="filled-fields-right col-lg-5 col-md-5 col-sm-12">{{userData?.deputy?.lastName}}</div>
      <div class="label-position col-lg-12 col-md-12 col-sm-12">Codice Fiscale</div>
      <div class="filled-fields-left col-lg-12 col-md-12 col-sm-12">{{userData?.deputy?.fiscalCode}}</div>
<!--      <div *ngIf="showAdditionalFieldsToBusinessUser()">
        <div class="filled-fields-left col-lg-5 col-md-5 col-sm-12">{{userData?.nameInInvoice}}</div>
        <div class="filled-fields-right col-lg-5 col-md-5 col-sm-12">P. I. {{userData?.vatNumber}}</div>
      </div>-->
      <div class="recharge-sim-title col-lg-12 col-md-12 col-sm-12">Documento</div>
      <div class="label-position col-lg-12 col-md-12 col-sm-12">Allega foto documento <b>fronte</b></div>
      <div class="filled-fields-left col-lg-6 col-md-6 col-sm-12">{{isFirstFileUpload && !isFirstFileWithWrongSize
        && !isFirstFileWithWrongExtension ? firstFileAfterValidation.name : 'Nessun file selezionato'
        }}</div>
      <div class="col-lg-1 col-md-1 col-sm-12"></div>
      <label class="upload-button col-lg-3 col-md-3 col-sm-12" for="files-upload" (change)="logEvent($event)">
        <img src="assets/img/icons/clip.png" alt="clip" style="width: 25px">
        SCEGLI FILE
        <input type="file"
               (change)="onFirstFileChange($event)"
               accept="application/pdf,image/jpeg,image/png"
               id="files-upload"
               name="frontDocumentPhoto">
      </label>
      <div class="col-lg-2 col-md-2 col-sm-12" (click)="removeFirstFileFromForm()" *ngIf="isFirstFileUpload">
        <img src="assets/img/icons/delete_icon.png" alt="clip" class="remove-file">
      </div>
      <div *ngIf="isFirstFileWithWrongExtension" class="text-danger col-lg-12 col-md-12 col-sm-12">
        L’estensione del file deve essere PDF, PNG, JPG o JPEG
      </div>
      <div *ngIf="isFirstFileWithWrongSize" class="text-danger col-lg-12 col-md-12 col-sm-12">
        La dimensione del file deve essere inferiore a 5 MB
      </div>
      <div *ngIf="formGroup.controls['frontDocumentPhoto'].hasError('required') &&
            (formGroup.controls['frontDocumentPhoto'].dirty || formGroup.controls['frontDocumentPhoto'].touched)"
           class="text-danger col-lg-12 col-md-12 col-sm-12">
        Campo obbligatorio
      </div>

      <div class="label-position col-lg-12 col-md-12 col-sm-12">Allega foto documento <b>retro</b></div>
      <div class="filled-fields-left col-lg-6 col-md-6 col-sm-12">{{isSecondFileUpload && !isSecondFileWithWrongSize
      && !isSecondFileWithWrongExtension ? secondFileAfterValidation.name : 'Nessun file selezionato'
        }}</div>
      <div class="col-lg-1 col-md-1 col-sm-12"></div>
      <label class="upload-button col-lg-3 col-md-3 col-sm-12" for="files-upload-2" (change)="logEvent($event)">
        <img src="assets/img/icons/clip.png" alt="clip" style="width: 25px">
        SCEGLI FILE
        <input type="file"
               (change)="onSecondFileChange($event)"
               accept="application/pdf,image/jpeg,image/png"
               id="files-upload-2"
               name="backDocumentPhoto">
      </label>
      <div class="col-lg-2 col-md-2 col-sm-12" (click)="removeSecondFileFromForm()" *ngIf="isSecondFileUpload">
        <img src="assets/img/icons/delete_icon.png" alt="clip" class="remove-file">
      </div>
      <div *ngIf="isSecondFileWithWrongExtension" class="text-danger col-lg-12 col-md-12 col-sm-12">
        L’estensione del file deve essere PDF, PNG, JPG o JPEG
      </div>
      <div *ngIf="isSecondFileWithWrongSize" class="text-danger col-lg-12 col-md-12 col-sm-12">
        La dimensione del file deve essere inferiore a 5 MB
      </div>
      <div *ngIf="formGroup.controls['backDocumentPhoto'].hasError('required') &&
            (formGroup.controls['backDocumentPhoto'].dirty || formGroup.controls['backDocumentPhoto'].touched)"
           class="text-danger col-lg-12 col-md-12 col-sm-12">
        Campo obbligatorio
      </div>

      <div class="label-position col-lg-12 col-md-12 col-sm-12">Tipologia SIM</div>
      <select class="select-border no-left-margin" formControlName="typeSim" name="typeSim">
        <option class="select-white text-select" *ngFor="let typesSim of typesSim"
                [attr.value]="typesSim" formControlName="typeContract" ngDefaultControl
                [selected]="typesSim===formGroup?.controls['typesSim']?.value">
          {{typesSim}}
        </option>
      </select>
      <div *ngIf="formGroup.controls['typeSim'].hasError('required') &&
            (formGroup.controls['typeSim'].dirty || formGroup.controls['typeSim'].touched)"
           class="text-danger col-lg-12 col-md-12 col-sm-12">
        Campo obbligatorio
      </div>
      <div *ngIf="formGroup.controls['typeSim'].value === 'SIM tradizionale'" class="label-position col-lg-12 col-md-12 col-sm-12">Allega foto <b>SIM</b> del gestore attuale</div>
      <div *ngIf="formGroup.controls['typeSim'].value === 'E-SIM'" class="label-position col-lg-12 col-md-12 col-sm-12">Allega foto <b>QR Code</b></div>

      <div class="filled-fields-left col-lg-6 col-md-6 col-sm-12">{{isThirdFileUpload && !isThirdFileWithWrongSize
      && !isThirdFileWithWrongExtension ? thirdFileAfterValidation.name : 'Nessun file selezionato'
        }}</div>
      <div class="col-lg-1 col-md-1 col-sm-12"></div>
      <label class="upload-button col-lg-3 col-md-3 col-sm-12" for="files-upload-3" (change)="logEvent($event)">
        <img src="assets/img/icons/clip.png" alt="clip" style="width: 25px">
        SCEGLI FILE
        <input type="file"
               (change)="onThirdFileChange($event)"
               accept="application/pdf,image/jpeg,image/png"
               id="files-upload-3"
               name="photoSim">
      </label>
      <div class="col-lg-2 col-md-2 col-sm-12" (click)="removeThirdFileFromForm()" *ngIf="isThirdFileUpload">
        <img src="assets/img/icons/delete_icon.png" alt="clip" class="remove-file">
      </div>
      <div *ngIf="isThirdFileWithWrongExtension" class="text-danger col-lg-12 col-md-12 col-sm-12">
        L’estensione del file deve essere PDF, PNG, JPG o JPEG
      </div>
      <div *ngIf="isThirdFileWithWrongSize" class="text-danger col-lg-12 col-md-12 col-sm-12">
        La dimensione del file deve essere inferiore a 5 MB
      </div>
      <div *ngIf="formGroup.controls['photoSim'].hasError('required') &&
            (formGroup.controls['photoSim'].dirty || formGroup.controls['photoSim'].touched)"
           class="text-danger col-lg-12 col-md-12 col-sm-12">
        Campo obbligatorio
      </div>
<!--      <div class="filled-fields-left col-lg-5 col-md-5 col-sm-12" *ngIf="documentTypeDocumento()">{{documentType}}</div>
      <div class="filled-fields-left col-lg-5 col-md-5 col-sm-12"
           *ngIf="!documentTypeDocumento()">{{userData?.deputy.documentType}}</div>
      <div class="filled-fields-right col-lg-5 col-md-5 col-sm-12"
           *ngIf="documentTypeDocumento()">{{documentNumber}}</div>
      <div class="filled-fields-right col-lg-5 col-md-5 col-sm-12"
           *ngIf="!documentTypeDocumento()">{{userData?.deputy.documentNumber}}</div>-->

<!--      <div class="form-group col-lg-12 col-md-12 col-sm-12 col-xs-12 checkbox-block">
        <div class="row select-button">
          <label class="checkbox-inline checkbox-container">
            <input type="radio" value="validDocument" formControlName="conditionDocument" class="form-checkbox">
            <span class="mark"></span>
            <div class="recharge-sim-title button-text">Documento valido</div>
          </label>
        </div>
        <div class="row select-button">
          <label class="checkbox-inline checkbox-container" (change)="showLostDocumentModal()">
            <input type="radio" value="lostDocument" formControlName="conditionDocument" class="form-checkbox">
            <span class="mark"></span>
            <div class="recharge-sim-title button-text">Documento smarrito/scaduto</div>
          </label>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12">
          <span class="text-danger" *ngIf="formGroup.controls['conditionDocument'].hasError('required') &&
            (formGroup.controls['conditionDocument'].dirty || formGroup.controls['conditionDocument'].touched
            || showAllUnCheckedField)">Campo obbligatorio.
          </span>
        </div>
      </div>-->
    </div>

    <div class="col-lg-6 col-md-6 col-sm-12 optima-number-block row-left small-row-bottom">
      <div class="margin-header recharge-sim-title center-sim-title col-lg-12 col-md-12 col-sm-12">
        Dati della SIM del numero da portare in Optima
      </div>
      <div class="form-inline">
        <input id="simNumberTemplate" class="form-control form-group" type="text" value="+39" disabled>
        <input id="simNumber" class="fields" placeholder="Numero da portare" type="text"
               formControlName="optimaNumber" name="optimaNumber" (change)="logEvent($event)"
               [style.color]="formGroup.controls['optimaNumber'].invalid ? 'red' : '#36749d'">
        <div class="col-lg-12 col-md-12 col-sm-12">
          <span class="text-danger" *ngIf="formGroup.controls['optimaNumber'].hasError('minLength') &&
            (formGroup.controls['optimaNumber'].dirty || formGroup.controls['optimaNumber'].touched)">
            Lunghezza minima 6 caratteri
          </span>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12">
          <span class="text-danger" *ngIf="formGroup.controls['optimaNumber'].hasError('maxLength') &&
            (formGroup.controls['optimaNumber'].dirty || formGroup.controls['optimaNumber'].touched)">
            Lunghezza massima 13 caratteri
          </span>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12" style="padding-left: 30px">
          <span class="text-danger" *ngIf="formGroup.controls['optimaNumber'].hasError('required') &&
            (formGroup.controls['optimaNumber'].dirty || formGroup.controls['optimaNumber'].touched || showAllUnCheckedField)">
            Campo obbligatorio
          </span>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12">
          <span class="text-danger" *ngIf="formGroup.controls['optimaNumber'].hasError('error') &&
            (formGroup.controls['optimaNumber'].dirty || formGroup.controls['optimaNumber'].touched)">
            {{formGroup.controls['optimaNumber'].errors.error}}
          </span>
        </div>
      </div>

      <div class="col-lg-12 col-md-12 col-sm-12">
        <div class="margin-left-part label-position label-position-right-part col-lg-12 col-md-12 col-sm-12">Operatore attuale</div>
        <select *ngIf="mobileOperators != null" (change)="logEvent($event)" name="operator"
                class="select-border" formControlName="operator"
                [style.color]="formGroup.controls['operator'].invalid &&
                formGroup.controls['operator'].untouched ? 'gray' : '#36749d'">
          <option class="hidden-select" [ngValue]="null" disabled selected hidden>Operatore attuale</option>
          <option class="select-white text-select" *ngFor="let mobileOperator of mobileOperators"
                  [selected]="mobileOperator.description===formGroup.controls['operator'].value"
                  [attr.value]="mobileOperator.description">
            {{mobileOperator.description}}
          </option>
        </select>

        <div class="col-lg-12 col-md-12 col-sm-12">
         <span class="text-danger" *ngIf="formGroup.controls['operator'].hasError('required') &&
          (formGroup.controls['operator'].dirty || formGroup.controls['operator'].touched
          || showAllUnCheckedField === true)">Campo obbligatorio
         </span>
        </div>
      </div>

      <div class="col-lg-12 col-md-12 col-sm-12 form-group">
        <div class="label-position label-position-right-part col-lg-12 col-md-12 col-sm-12">Tipologia contratto</div>
        <select *ngIf="mobileOperators != null" (change)="logEvent($event)" class="select-border"
                name="typeContract"
                formControlName="typeContract" [style.color]="formGroup?.controls['typeContract']?.invalid &&
                formGroup?.controls['typeContract']?.untouched ? 'gray' : '#36749d'">
          <option class="hidden-select" [ngValue]="null" disabled selected hidden>Tipologia contratto</option>
          <option class="select-white text-select" *ngFor="let typeContract of typesContract"
                  [attr.value]="typeContract" formControlName="typeContract" ngDefaultControl
                  [selected]="typeContract===formGroup?.controls['typeContract']?.value">
            {{typeContract}}
          </option>
        </select>
        <div class="col-lg-12 col-md-12 col-sm-12">
          <!--          <span class="text-danger" *ngIf="formGroup.controls['typeContract'].hasError('required')">-->
          <!--            Required-->
          <!--          </span>-->
          <!--          <span class="text-danger" *ngIf="formGroup.controls['typeContract'].dirty">-->
          <!--            Dirty-->
          <!--          </span>-->
          <!--          <span class="text-danger" *ngIf="formGroup.controls['typeContract'].touched">-->
          <!--            Touched-->
          <!--          </span>-->
          <!--          <span class="text-danger" *ngIf="showAllUnCheckedField">-->
          <!--            ShowAllUnCheckedField-->
          <!--          </span>-->
          <span class="text-danger" *ngIf="formGroup?.controls['typeContract'].hasError('required') &&
            (formGroup?.controls['typeContract']?.dirty || formGroup?.controls['typeContract']?.touched || showAllUnCheckedField)">
            Campo obbligatorio
         </span>
        </div>
      </div>
<!--      <div class="form-inline">
        <input id="simNumberTemplateICCD" class="form-control-width form-group" type="text" value="ICCID: 8939"
               disabled>
        <input id="simNumberICCD" class="fields-width" placeholder="" formControlName="simNumberICCD"
               [style.color]="formGroup.controls['simNumberICCD'].invalid ? 'red' : '#36749d'">
        <div class="col-lg-12 col-md-12 col-sm-12">
          <div class="col-lg-12 col-md-12 col-sm-12">
            <span class="text-danger" *ngIf="formGroup.controls['simNumberICCD'].hasError('minLength') &&
              (formGroup.controls['simNumberICCD'].dirty || formGroup.controls['simNumberICCD'].touched)">
             Lunghezza minima 15 caratteri
           </span>
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12">
            <span class="text-danger" *ngIf="formGroup.controls['simNumberICCD'].hasError('maxLength') &&
               (formGroup.controls['simNumberICCD'].dirty || formGroup.controls['simNumberICCD'].touched)">
              Lunghezza massima 16 caratteri
            </span>
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12">
            <span class="text-danger" *ngIf="formGroup.controls['simNumberICCD'].hasError('required')
              && (formGroup.controls['simNumberICCD'].dirty || formGroup.controls['simNumberICCD'].touched
              || showAllUnCheckedField === true)">Campo obbligatorio
            </span>
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12">
            <span class="text-danger" *ngIf="formGroup.controls['simNumberICCD'].hasError('error') &&
            (formGroup.controls['simNumberICCD'].dirty || formGroup.controls['simNumberICCD'].touched)">
                  {{formGroup.controls['simNumberICCD'].errors.error}}
            </span>
          </div>
        </div>
      </div>-->

      <div *ngIf="showRichiestaTrasferimentoCredito" class="form-inline-top">
        <div>
          <input class="form-width form-group" type="text" value="Richiesta trasferimento credito" disabled>
          <select class="form-control-select" (change)="logEvent($event)" name="trasferimentoCredito"
                  formControlName="trasferimentoCredito">
            <option *ngFor="let isTransferCredit of transferCredit" [attr.value]="isTransferCredit"
                    formControlName="trasferimentoCredito" ngDefaultControl
                    [selected]="isTransferCredit===formGroup?.controls['trasferimentoCredito']?.value">{{isTransferCredit}}
            </option>
          </select>
        </div>
      </div>
      <input type="text" class="form-control-date" placeholder="Data di attivazione del servizio di portabilità"
             name="Data di attivazione del servizio di portabilita" (bsValueChange)="logEvent($event)"
             bsDatepicker [minDate]="today" formControlName="date" (click)="dateSelectChange()"/>
      <div class="col-lg-12 col-md-12 col-sm-12">
          <span class="text-danger" *ngIf="formGroup.controls['date'].hasError('required') &&
            (formGroup.controls['date'].dirty || formGroup.controls['date'].touched || showAllUnCheckedField)">
            Campo obbligatorio
          </span>
      </div>
      <!--      <input class="form-control-date" type="date" formControlName="date"-->
      <!--             placeholder="Data di attivazione del servizio di portabilità" (change)="applyFilter()"-->
      <!--             (click)="dateSelectChange()"/>-->

      <div *ngIf="dateSelect" class="row select-button">
        <label class="checkbox-inline checkbox-container">
          <input [checked]="isAgree" (change)="changeAgreement()" type="checkbox" name="agreement"
                 formControlName="agreement" class="form-checkbox">
          <span class="mark"></span>
          <div class="recharge-sim-title radio-button-text">
            Il sottoscritto prende atto del fatto che la data di
            riferimento indicata non potrà essere inferiore al
            periodo di realizzazione. Tale data si intende
            automaticamente sostituita con la prima data
            utile per la prestazione. La data di attivazione del
            servizio MNP potrà in ogni caso differire da
            quella indicata ed, in particolare, essere
            anticipata, subire ritardi o essere rifiutata per le
            cause indicate nelle condizioni generali di
            contratto.
          </div>
        </label>
        <div class="col-lg-12 col-md-12 col-sm-12">
          <span class="text-danger" *ngIf="formGroup.controls['agreement'].hasError('required') &&
          (formGroup.controls['agreement'].dirty || formGroup.controls['agreement'].touched || showAllUnCheckedField)">
            Campo obbligatorio
          </span>
        </div>
      </div>
      <div class="col-lg-12 col-md-12 col-sm-12 button-applica">
        <button class="button button-applica colour" type="submit" name="Applica Variazione"
                [style.backgroundColor]="formGroup.valid? '#9BC641FF' : 'gray'" (click)="onSubmit()">
          APPLICA VARIAZIONE
        </button>
      </div>
    </div>
  </form>
</div>

<div class="modal-div display" *ngIf="showFinalModal">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <div class="title-modal">Riepilogo dati</div>
    <div class="modal-text">
      Una volta avviata la procedura, la richiesta di portabilità non può essere revocata.
      In caso di ripensamento, potrai chiedere di portare il numero verso l’operatore che
      stai lasciando o verso qualunque altro operatore.
    </div>
    <div class="row-data">
      Tipologia di cliente
      <div class="value">{{userData?.cluster.value}}</div>
    </div>
    <div *ngIf="userData?.cluster.value === 'BUSINESS'">
      <div class="row-data">
        Ragione sociale
        <div class="value">{{userData?.nameInInvoice}}</div>
      </div>
      <div class="row-data">
        P.IVA
        <div class="value">{{userData?.vatNumber}}</div>
      </div>
    </div>
    <div class="row-data">
      Nome
      <div class="value">{{userData?.deputy.firstName}}</div>
    </div>
    <div class="row-data">
      Cognome
      <div class="value">{{userData?.deputy.lastName}}</div>
    </div>
    <div class="row-data">
      Codice Fiscale
      <div class="value">{{userData.deputy.fiscalCode}}</div>
    </div>
    <div class="row-data">
      Indirizzo e-mail
      <div class="value">{{userData?.email}}</div>
    </div>
    <div class="row-data">
      Numero di telefono
      <div class="value">+39 {{formGroup.value.optimaNumber}}</div>
    </div>
    <div class="row-buttons">
      <button class="button-modifica" name="Modifica" (click)="hideDialogModal();">MODIFICA</button>
      <button class="button" name="Procedi" (click)="sendOTP();">PROCEDI</button>
    </div>
  </div>
</div>

<div class="modal-div display" *ngIf="showOTPModal">
  <div class="inner-modal-div">
    <form [formGroup]="formGroupOTP">
      <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
      <div class="modal-text-otp">
        Per procedere inserisci nel campo in basso il codice che hai ricevuto sul recapito:
        {{number.length === 0 || !number.startsWith("3") ||
      number.slice(0, 4) === "00393" ? userData.email : number}}
      </div>
      <div class="row">
        <div class="text-danger text-danger-modal" *ngIf="showCodiceErrato">Codice errato</div>
        <input type="text" formControlName="otp" class="input-otp">
      </div>
      <div class="row-buttons-otp">
        <button class="button-modifica" name="Annula" (click)="hideDialogModal();">ANNULA</button>
        <button class="button" name="Procedi" (click)="checkOTP()">PROCEDI</button>
      </div>
    </form>
  </div>
</div>
<div class="modal-div display" *ngIf="showSuccsessModal">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="redirectToFaiDaTe()"></i>
    <img class="image modal-image" src="/assets/img/icons/ok.png" alt="Ok">
    <div class="title modal-title">Abbiamo preso in carico la tua richiesta.</div>
    <div class="title modal-title-text">
      Entro 72 ore troverai un aggiornamento nella sezione “Fai da te/Le tue segnalazioni”.
    </div>
  </div>
</div>
<div class="modal-div display" *ngIf="showSendMailInfoModalLostDocument">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideSuccessDialogModal()"></i>
    <img class="image modal-image" src="/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png">
    <div class="text modal-text-custom">
      Per procedere alla richiesta di portabilità è necessario inoltrare al Servizio Clienti Optima il “Modulo MNP” e il
      documento d’identità valido. Abbiamo inoltrato il modulo al tuo indirizzo e-mail: {{userData.email}}
    </div>
  </div>
</div>
<div class="modal-div display" *ngIf="showInfoModalLostDocument">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideSuccessDialogModal()"></i>
    <img class="image modal-image" src="/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png">
    <div class="text modal-text-custom">
      Per procedere alla richiesta di portabilità è necessario inoltrare al Servizio Clienti Optima il
      “Modulo MNP” disponibile nella sezione “Moduli” della tua Area Clienti e allegare il documento d’identità valido.
    </div>
  </div>
</div>
