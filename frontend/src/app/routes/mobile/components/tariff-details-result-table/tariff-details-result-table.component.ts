import {AfterViewInit, ChangeDetectorRef, Component, Input, OnChanges, OnInit, SimpleChanges, ViewChild} from '@angular/core';
import { MatPaginator, MatTableDataSource } from '@angular/material';
import TariffDetail from '../../../../common/model/mobile/TariffDetail';

@Component({
  selector: 'app-tariff-details-result-table',
  templateUrl: './tariff-details-result-table.component.html',
  styleUrls: ['./tariff-details-result-table.component.scss']
})
export class TariffDetailsResultTableComponent implements OnInit, AfterViewInit, OnChanges {

  @ViewChild(MatPaginator) paginator: MatPaginator;

  displayedColumns = ['service', 'calledNumber', 'startDate', 'endDate', 'duration', 'cost'];

  dataSource: any = new MatTableDataSource<any>([]);

  constructor(private cdRef: ChangeDetectorRef) {
  }

  ngOnInit() {}

  @Input('tariffDetails')
  set tariffDetails(tariffDetails: Array<TariffDetail>) {
    this.dataSource = new MatTableDataSource<any>(tariffDetails);
    this.dataSource.paginator = this.paginator;
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.cdRef.detectChanges();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if(this.dataSource.data[0] && this.dataSource.data[0].service === 'DATA - (OPT)') {
      this.displayedColumns = ['service', 'calledNumber', 'startDate', 'endDate', 'duration', 'consumption', 'cost']
    } else this.displayedColumns = ['service', 'calledNumber', 'startDate', 'endDate', 'duration', 'cost'];
  }
}
