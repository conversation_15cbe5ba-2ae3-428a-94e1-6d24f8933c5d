<div class="app--tariff-details-result-table">

  <mat-table #table
    [dataSource]="dataSource">

    <ng-container matColumnDef="service">
      <mat-header-cell class="middle" *matHeaderCellDef>Servizio</mat-header-cell>
      <mat-cell class="middle" *matCellDef="let element">
        <span class="mobile-label">Servizio</span>
        <span class="align-top">{{element.service}}</span>
      </mat-cell>
    </ng-container>

    <ng-container matColumnDef="calledNumber">
      <mat-header-cell class="big" *matHeaderCellDef>Numero chiamato</mat-header-cell>
      <mat-cell class="big" *matCellDef="let element">
        <span class="mobile-label">Numero chiamato</span>
        <span class="align-top">{{element.calledNumber.slice(0, -3) + '***'}}</span>
      </mat-cell>
    </ng-container>

    <ng-container matColumnDef="startDate">
      <mat-header-cell class="big" *matHeaderCellDef>Inizio</mat-header-cell>
      <mat-cell class="big" *matCellDef="let element">
        <span class="mobile-label">Inizio</span>
        <span class="align-top">{{element.answerTime | date:"dd/MM/yyyy HH:mm:ss"}}</span>
      </mat-cell>
    </ng-container>

    <ng-container matColumnDef="endDate">
      <mat-header-cell class="big" *matHeaderCellDef> Fine</mat-header-cell>
      <mat-cell class="big" *matCellDef="let element">
        <span class="mobile-label">Fine</span>
        <span class="align-top">{{element.disconnectTime | date:"dd/MM/yyyy HH:mm:ss"}}</span>
      </mat-cell>
    </ng-container>

    <ng-container matColumnDef="duration">
      <mat-header-cell lass="small" *matHeaderCellDef>Durata sec</mat-header-cell>
      <mat-cell class="small" *matCellDef="let element">
        <span class="mobile-label">Durata sec</span>
        <span class="align-top">{{element.duration}}</span>
      </mat-cell>
    </ng-container>

    <ng-container matColumnDef="consumption">
      <mat-header-cell class="mb-col" lass="small" *matHeaderCellDef>MB</mat-header-cell>
      <mat-cell class="small" *matCellDef="let element">
          <span class="mobile-label">MB</span>
          <span class="align-top">{{element.usedMegabytes ? element.usedMegabytes : 0 }} {{element.suffix}}</span>
      </mat-cell>
    </ng-container>

    <ng-container matColumnDef="cost">
      <mat-header-cell  lass="small" *matHeaderCellDef>Costo €</mat-header-cell>
      <mat-cell class="small" *matCellDef="let element">
        <span class="mobile-label">Costo €</span>
        <span class="align-top">{{element.amount}}</span>
      </mat-cell>
    </ng-container>

    <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
    <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
  </mat-table>

  <mat-paginator #paginator
    [pageSize]="10"
    [showFirstLastButtons]="true">
  </mat-paginator>

</div>
