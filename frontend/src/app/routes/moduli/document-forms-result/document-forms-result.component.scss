@import "~app/shared/styles/colors";

.active-layout {
  font-family: 'Lato-Regular';
  width: 100%;
  margin-top: 7%;
  margin-left: auto;
  margin-right: auto;
  padding: 0;

}

/*.document-collapse {
  width: 100%;
}*/

.mat-expansion-panel:not([class*=mat-elevation-z]) {
  box-shadow: none;
}

.mat-expansion-panel {
  text-align: left;
  padding-left: 0;
  padding-right: 0;

  .cluster-title {
    font-size: 1em;
    color: $dark-blue;
    font-weight: 700;
  }

  .service-title {
    color: $dark-blue;
    font-weight: 600;
    font-family: Lato-Regular;
  }

  .form-document {
    .document-link {
      color: $light-blue;
      text-decoration: underline;
      padding-left: 1%;

      &:hover {
        cursor: pointer;
      }
    }
  }
}

::ng-deep .mat-expansion-panel-header-title {
  margin-left: 20px;
  margin-right: 0;
}

::ng-deep .mat-expansion-panel-body {
  padding-left: 0;
  padding-right: 0;
}

::ng-deep .mat-expansion-indicator {
  margin-right: 25px;
}

.mat-expansion-panel-header {
  padding: 0;
}

.form-control {
  border: 2px solid $menu-border;
  border-radius: 7px;
  color: $dark-blue;
  font-size: 1em;
  font-weight: 700;
  height: 50px;
  padding: 0 0 0 0;
}

.app-header {
  text-align: left;
  font-size: 1em;
  font-weight: 600;
  color: $dark-blue;
  height: 45px;
  display: flex;
  align-items: center;
  padding-left: 0;
  padding-right: 0;
}


.app-first-expansion-panel {
  border: 2px solid $menu-border;
  border-top-left-radius: 7px;
  border-top-right-radius: 7px;
  padding-right: 22px;
  padding-left: 12px;
}

.mat-expansion-panel {
  width: 100%;
}

.no-padding {
  padding: 0;
}

.col-margin-top {
  margin-top: 10px;
}

/deep/ .mat-expansion-indicator::after, .mat-expansion-panel-header-description {
  color: $dark-blue;
}

.col-padding-left {
  padding-left: 15px;
}

/*
  ##Device = Desktops
  ##Screen = 1200px to higher resolution desktops
*/
@media (min-width: 1200px) {
  .bordered-block {
    min-height: 80vh;
    overflow: auto;
    border: 2px solid $menu-border;
    border-radius: 7px;
    padding-right: 0;
    margin-top: 1%;
    padding-top: 15px;
    padding-left: 15px;
  }
  .col-padding-btw {
    padding-left: 15px;
  }

  .page-title {
    .title-image {
      background: url("/assets/img/optima/Set_Icone_AreaClienti_Moduli.png") no-repeat center;
      background-size: contain;
      width: 68px;
      height: 58px;
      float: left;
    }
  }
}

/*
  ##Device = Laptops, Desktops
  ##Screen = B/w 992px to 1199px
*/
@media (min-width: 992px) and (max-width: 1199px) {
  .bordered-block {
    min-height: 80vh;
    overflow: auto;
    border: 2px solid $menu-border;
    border-radius: 7px;
    padding-right: 0;
    margin-top: 30px;
    padding-top: 15px;
    padding-left: 15px;
  }
  .col-padding-btw {
    padding-left: 15px;
  }

  .page-title {
    .title-image {
      background: url("/assets/img/optima/Set_Icone_AreaClienti_Moduli.png") no-repeat center;
      background-size: contain;
      width: 68px;
      height: 58px;
      float: left;
    }
  }
}

/*
  ##Device = Tablets, Ipads (landscape)
  ##Screen = B/w 768px to 991px
*/
@media (min-width: 768px) and (max-width: 991px) and (orientation: landscape) {
  .bordered-block {
    min-height: 80vh;
    overflow: auto;
    padding-right: 0;
    margin-top: 30px;
    padding-top: 15px;
    padding-left: 15px;
  }
  .page-title {
    display: none;
  }
}

/*
  ##Device = Low Resolution Tablets, Mobiles (Landscape)
  ##Screen = B/w 481px to 767px
*/
@media (min-width: 481px) and (max-width: 767px) {
  .bordered-block {
    min-height: 80vh;
    overflow: auto;
    padding-right: 0;
    margin-top: 30px;
    padding-top: 15px;
    padding-left: 15px;
  }
  .page-title {
    display: none;
  }
}

/*
  ##Device = Most of the Smartphones Mobiles (Portrait)
  ##Screen = B/w 320px to 479px
*/
@media (max-width: 480px) {
  .bordered-block {
    min-height: 80vh;
    overflow: auto;
    padding-right: 0;
    margin-top: 30px;
    padding-top: 15px;
    padding-left: 15px;
  }
  .page-title {
    display: none;
  }
}
