import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { DocumentFormsResultService } from './document-forms-result.service';
import { FormDocument } from './form-document.model';

@Component({
  selector: 'app-document-forms-result',
  templateUrl: './document-forms-result.component.html',
  styleUrls: ['./document-forms-result.component.scss']
})
export class DocumentFormsResultComponent implements OnInit {
  groupDocumentsData: any;
  businesDocuments: any = {};
  consumerDocuments: any = {};
  formGroup: FormGroup;
  isExpanded = false;

  private documents: Array<FormDocument>;

  private url = 'http://www.optimaitalia.com/downloadFile.php?relUri=';

  constructor(private documentFormsResultService: DocumentFormsResultService, private fb: FormBuilder) {
    this.init();
    this.formGroup.controls.filter.valueChanges.subscribe(filter => {
      if (filter) {
        const groupDocumentsData: any = this.documentFormsResultService.filterDocuments(this.documents, filter);
        this.businesDocuments.Business = groupDocumentsData && groupDocumentsData.Business;
        this.consumerDocuments.Consumer = groupDocumentsData && groupDocumentsData.Consumer;
        this.isExpanded = true;
      } else {
        this.businesDocuments.Business = this.groupDocumentsData && this.groupDocumentsData.Business;
        this.consumerDocuments.Consumer = this.groupDocumentsData && this.groupDocumentsData.Consumer;
        this.isExpanded = false;
      }
    });
  }

  init() {
    this.formGroup = this.fb.group({
      filter: []
    });
    this.documentFormsResultService.loadDocumentsFormsData().subscribe(data => {
      this.documents = data;
      const groupDocumentsData: any = this.documentFormsResultService.groupDocumentsData(data);
      this.groupDocumentsData = groupDocumentsData;
      this.businesDocuments.Business = groupDocumentsData && groupDocumentsData.Business;
      this.consumerDocuments.Consumer = groupDocumentsData && groupDocumentsData.Consumer;
    });
  }

  ngOnInit() {
  }


  objectKeys(object: object): Array<string> {
    return object ? Object.keys(object) : null;
  }

  download(fileName: string) {
    window.open(this.url + fileName, 'tab');

  }

}
