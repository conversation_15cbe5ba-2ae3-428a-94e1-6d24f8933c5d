@import '../../invoice.common';

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}
.display {
  display: block;
}
.inner-modal-div {
  min-height: 300px;
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 20px;
  border: 2px solid #36749d;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 600px;
}
.fa-times {
  position: absolute;
  right: 35px;
  font-size: 39px;
  top: 25px;
  color: #36749d;
  cursor: pointer;
}
.modal-text {
  text-align: center;
  color: #36749d;
  width: 70%;
  margin: auto;
  padding-top: 10px;
  font-style: italic;
}
.modal-text-payment{
  text-align: center;
  color: #36749d;
  width: 70%;
  margin: auto;
  padding-top: 10px;
  font-size: 152%;
}
.container-flex {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: stretch;
  margin: 8%;
  .button-with-icon {
    text-align: center;
    color: #36749C;
    padding: 16px 11px 10px;
    font-size: 18px;
    background-color: white;
    border: 1px solid #36749C;
    border-radius: 13px;
    width: 42%;
  }
  .button-with-icon:hover {
    cursor: pointer;
  }
  .bolder {
    font-weight: bold;
    margin-top: -4%;
  }
  .icon-mastercard {
    background: white url("../../../../../assets/img/payment/icon_mastercard_visa.png") no-repeat center;
    background-size: contain;
    height: 37px;
    margin-top: 5%;
    margin-bottom: 7%;
  }
  .icon-paypal {
    background: white url("../../../../../assets/img/payment/icon_paypal.png") no-repeat center;
    background-size: contain;
    height: 32px;
    margin-bottom: 7%;
    margin-top: 6%;
  }
}



.night-blue-bold {
  color: #36749d;
  font-weight: 600;
}

.deferments-layout {
  margin-top: 5%;
  color: $dark-blue;

  hr {
    border: none;
    height: 2px;
    color: $dark-blue;
    background-color: $menu-background;
  }

  .delay-details-block, .rate-details-block {
    border: 1px solid $menu-border;
    background-color: $menu-background;
    border-radius: 5px;
    padding-top: 5px;
    padding-bottom: 5px;
    display: flex;
    align-items: center;
    min-height: 50px;
  }

  .delay-details-block {
    margin-bottom: 3%;
    text-align: center;

  }

  .rate-details-block {
    margin-bottom: 1%;
  }

  .details-title {
    color: $dark-blue;
    font-size: 18px;
  }

  .app-button {
    padding: 3px 40px;
    float: right;
  }

  .active {
    background-color: $dark-blue;

    a {
      color: #ffffff;
    }
  }

  .table-row:nth-child(2n) {
    background-color: $menu-background;
  }

  .invoice-table {
    .table-header, .table-body {
      width: 18%;
    }

    .checkbox-block {
      width: 10%;
    }
  }
}

.info-modal {
  ::ng-deep .modal-window {
    .modal-header {
      border-bottom: 0;
    }
  }

  .modal-message {
    text-align: center;
    padding: 20px;
    margin-bottom: 40px;
    font-size: 18px;
    color: $dark-blue;
  }
}

@media screen and (max-width: 991px) {
  .deferments-layout {
    .delay-details-block {
      display: block;
    }

    .delay-details-block {
      text-align: left;
      padding-top: 10px;
      padding-bottom: 10px;

      .delay-detail {
        margin-top: 1%;
      }
    }

    .rate-details-block, .details-title {
      padding: 0;

    }

    .details-title {
      margin-left: 10px;
    }

    .paga-button {
      padding: 0;
      margin-right: 10px;
    }

    .invoice-table {
      margin-bottom: 5%;
    }

    .app-button {
      text-align: center;
      padding: 3px 30px;
    }
  }
}

@media screen and (max-width: 767px) {
  .deferments-layout {
    margin-top: 8%;
    padding: 0;

    .delay-details-block {
      padding-left: 5px;
      padding-right: 5px;

      .details-title {
        margin: 0;
      }

      .delay-detail, .details-title {
        padding-left: 5px;
        padding-right: 5px;
      }
    }

    .paga-button {
      padding: 0;
      display: flex;
      margin-top: 2%;

      .app-button {
        margin: auto;
      }
    }

    .rate-details-block {
      display: block;
      padding: 5px 10px;

      div {
        margin-left: 0;
        padding-left: 5px;
        float: left;
      }
    }

    .invoice-table {
      .checkbox-block {
        width: 5%;
      }

      .clickable-block {
        width: 95%;
      }

      .table-body-mobile {
        .table-body {
          width: 50%;
        }
      }
    }

    .table-row:nth-child(2n) {
      background: none;
    }
  }
}

@media screen and (max-width: 420px) {
  .deferments-layout {
    margin-top: 11%;
  }
  .invoice-table {
    .clickable-block {
      .table-body {
        .fa {
          float: right;
        }
      }
    }
  }
}
