<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 deferments-layout">
  <h4 class="night-blue-bold">La tua dilazione</h4>
  <div>Qui trovi il dettaglio della tua dilazione, l'elenco delle rate che la compongono e lo stato dei pagamenti.
  </div>
  <div>Puoi selezionare e pagare direttamente nel pannello sottostante</div>
  <hr/>

  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 delay-details-block">
    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12 details-title">
      <span class="night-blue-bold">Dettaglio dilazione</span>
    </div>
    <div class="col-lg-2 col-md-2 col-sm-4 col-xs-4 delay-detail">
      <div class="">Data inizio</div>
      {{dilazioneData.dataInizio | date: "dd/MM/yyyy"}}
    </div>
    <div class="col-lg-2 col-md-2 col-sm-4 col-xs-4 delay-detail">
      <div class="">Data fine</div>
      {{dilazioneData.dataFine | date: "dd/MM/yyyy"}}
    </div>
    <div class="col-lg-2 col-md-2 col-sm-4 col-xs-4 delay-detail">
      <div class="">Totale importo</div>
      {{dilazioneData.totaleImporto}}
    </div>
    <div class="col-lg-2 col-md-2 col-sm-4 col-xs-4 delay-detail">
      <div class="">Numero rate</div>
      {{dilazioneData.numeroRate}}
    </div>
    <div class="col-lg-2 col-md-2 col-sm-4 col-xs-7 delay-detail">
      <div class="">Fatture in dilazione</div>
      <span *ngFor="let item of dilazioneData.fatture">
          N {{item.numeroDocumento}},
        </span>
    </div>
  </div>

  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 rate-details-block">
    <div class="col-lg-2 col-md-3 col-sm-3 col-xs-12 details-title">
      <span class="night-blue-bold">Dettaglio rate</span>
    </div>
    <div class="col-lg-7 col-md-5 col-sm-5 col-xs-12">
      Seleziona qui in basso le rate de pagare
    </div>
    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-12 paga-button">
      <span class="app-button {{pagaList.length!==0&&'active'}}">
        <a target="_blank" type="button" [class.disabled]="(pagaList.length==0)"
           (click)="openPaymentForSelectedRate(pagaList)">Paga le rate selezionate</a>
      </span>
    </div>
  </div>

  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 invoice-table bordered">
    <div class="table-header checkbox-block"></div>
    <div class="table-header night-blue-bold">IdRata</div>
    <div class="table-header night-blue-bold">Data scadenza</div>
    <div class="table-header night-blue-bold">Importo rata</div>
    <div class="table-header night-blue-bold">Data pagamento</div>
    <div class="table-header night-blue-bold">Importo pagato</div>

    <div class="line"></div>
    <div class="table-row" *ngFor="let item of dilazioneData.rate">
      <div class="mobile-header">
        <div class="table-body checkbox-block">
          <label class="checkbox-container">
            <input name="referencePeriod" type="checkbox"
                   [value]="item" (change)="handleChange(item,$event)"
                   [disabled]="item.importoPagato === item.importoRata && item.dataPagamento"/>
            <span class="mark {{item.importoPagato === item.importoRata && item.dataPagamento&&'disabled'}}"></span>
          </label>
        </div>
        <div class="clickable-block" (click)="item.open=!item.open">
          <div class="table-body arrows-mobile">
            <span class="fa fa-angle-right" *ngIf="!item.open"></span>
            <span class="fa fa-angle-down" *ngIf="item.open"></span>
          </div>
          <div class="table-body invoice-id">{{item.idRata}}</div>
        </div>
      </div>
      <div class="table-body-mobile" *ngIf="!isMobile || item.open">
        <div class="table-body">
          <span class="table-body-header">Data scadenza</span>
          <span>{{item.dataScadenza | date: "dd/MM/yyyy"}}</span>
        </div>
        <div class="table-body">
          <span class="table-body-header">Importo rata</span>
          <span>{{item.importoRata ? item.importoRata.toFixed(2) : item.importoRata}}</span>
        </div>
        <div class="table-body">
          <span class="table-body-header">Data pagamento</span>
          <span *ngIf="item.dataPagamento">{{item.dataPagamento  | date: "dd/MM/yyyy"}}</span>
          <span *ngIf="!item.dataPagamento">-</span>
        </div>
        <div class="table-body">
          <span class="table-body-header">Importo pagato</span>
          <span>{{item.importoPagato ? item.importoPagato.toFixed(2): item.importoPagato}}</span>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="modal-div display" *ngIf="showPagamentoModalSelectedRate">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <div class="modal-text-payment">Scegli la modalità di pagamento</div>
    <div class="container-flex">
      <div class="button-with-icon" (click)="openPaymentForSelectedRateByCreditCart(pagaList)">
        <div class="icon-mastercard"></div>
      </div>
      <div class="button-with-icon" (click)="openPaymentForSelectedRateByPayPal(pagaList)">
        <div class="icon-paypal"></div>
      </div>
    </div>
  </div>
</div>
