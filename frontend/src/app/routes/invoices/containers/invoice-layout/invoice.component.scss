@import "~app/shared/styles/colors";
@import "../../invoice.common";

.app-input {
  padding-left: 6px;
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.subject {
  border-right: 2px solid $menu-border;
  padding-right: 0;
  font-size: 15px;
  color: #36749C;
}

.bold {
  font-weight: bold;
}

.checkbox-container {
  top: 1px;
}

.modal-image {
  text-align: center;
  margin: 3% auto auto;
  width: 50px;
  display: block;
}

.display {
  display: block;
}

.padding {
  padding: 6px;
}

.inner-modal-div {
  min-height: 300px;
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 30px;
  border: 2px solid #bed2de;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 600px;
}

.invoice-layout {
  color: $dark-blue;
  margin-top: 6%;

  .accountant {
    .accountant-message-block, .app-button {
      margin-top: 1%;
    }

    .accountant-message-block {
      margin-bottom: 1%;

      .app-button {
        margin-right: 0.2%;

        //&:last-child {
        //  margin-right: 0;
        //}
      }

      .paga {
        margin: 0 auto;
        border: 1px solid #9BC641;
        background: #9BC641;
        color: white;
      }

      .alegga-button {
        margin-top: 0;
        margin-left: 10px;
        background-color: #36749a;
        color: white;
      }

      .clip {
        width: 20px;
        transform: rotate(225deg);
        margin-top: -2px;
      }

      .scaduto-label {
        font-size: 16px;
        margin-top: 5px;
      }

    }

    .name {
      font-weight: bold;
      font-size: 20px;
    }
  }

  .line {
    border: 1px solid $menu-border;
    height: 1px;
    float: left;
    width: 100%;
    margin-top: 30px;
  }

  .control-panel {
    margin-top: 2%;
    border: 2px solid $menu-border;
    background-color: $menu-background;
    border-radius: 10px;
    min-height: 60px;
    display: flex;
    align-items: center;

    label {
      margin-right: 5px;
    }

    .control-panel-item {
      display: inline-block;
      float: left;

      label {
        margin-bottom: 0;
      }
    }

    .search-button {
      background-color: $green;
      border: 1px solid $green;
      color: #ffffff;
      padding: 3px 15px;
      border-radius: 3px;
    }

    .search-title {
      padding: 0 30px;
      font-size: 18px;
    }

    .search-by-invoice {
      display: flex;
      align-items: center;
      margin-right: 30px;

      .search-label {
        margin: 0;
        width: 90%;
        font-size: 16px;
      }

      .search-input {
        border: 1px solid #B3C9DE;
        border-radius: 5px;
        background-color: white;
        margin-top: 3px;
      }
    }
    .search-by-date {
      padding-right: 30px;
      padding-top: 3px;
    }

    .pay-button {
      margin-left: auto;
      margin-right: 3%;

      .app-button {
        border: 2px solid $menu-border;
        padding: 3px 50px;
        background-color: #ffffff;
        text-decoration: none;
        cursor: pointer;

        a {
          pointer-events: none;
        }
      }
    }
  }

  /* Tooltip container */
  .custom-tooltip {
    position: relative;
    display: inline-block;

    .info-circle {
      border-radius: 50%;
      border: 1px solid;
      height: 17px;
      width: 17px;
      line-height: 15px;
      font-weight: 700;
      font-size: 13px;
    }
  }

  /* Tooltip text */
  .custom-tooltip .tooltiptext {
    visibility: hidden;
    text-align: center;

    /* Position the tooltip text */
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    padding: 10px 10px;
    color: #444444;
    background-color: #ffffff;
    font-weight: normal;
    font-size: 13px;
    border-radius: 8px;
    min-width: 130px;
    width: 150px;
    max-width: 320px;
    box-sizing: border-box;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.5);

    /* Fade in tooltip */
    opacity: 0;
    transition: opacity 0.3s;
  }

  /*!* Tooltip arrow *!
  .custom-tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent transparent transparent;
  }*/

  /* Show the tooltip text when you mouse over the tooltip container */
  .custom-tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
  }


}

.pdf-icon {
  height: 40px;
  cursor: pointer;
}

.saldo-value {
  color: #E1513D;
  font-size: 24px;
}

.i-button {
  border-radius: 50%;
  width: 20px;
  height: 20px;
  border: 1px solid;
  right: 10px;
  font-size: 12px;
  font-weight: 600;
  color: #36749C;
  cursor: pointer;
  display: block;
  margin: auto;
  padding: initial;
}

.modal-footer {
  border-top: none;
  display: flex;
  justify-content: space-evenly;
}

.confirm-window {
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);

  .close {
    color: #36749d;
    opacity: 1;
  }

  .modal-dialog {
    margin-top: 10%;
  }

  .modal-header {
    border-bottom: none;
  }
}

.accept-modal, .decline-modal {
  border: 1px solid #36749d;
  background-color: #ffffff;
  color: #36749d;
}

.modal-window {
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);

  .close {
    color: #36749d;
    opacity: 1;
  }

  .modal-dialog {
    margin-top: 10%;
  }

  .modal-footer {
    border-top: 1px solid #b6cce3;
  }

  .modal-header {
    border-bottom: 1px solid #b6cce3;
  }
}

.info-message {
  width: 60%;
  margin: auto;
}

.modal-body-content {
  text-align: center;
}

.back {
  position: fixed;
  z-index: 1100;
  background-color: rgba(255, 255, 255, 0.9);
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  overflow: hidden;
}

.spinner {
  background: url("/assets/img/logo/optima_new_main_logo.svg") no-repeat;
  width: 200px;
  height: 150px;
  background-size: 100%;

  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -100px;
  margin-left: -75px;
  z-index: 900;

  opacity: 0.2;
  -webkit-animation: example 1s linear infinite;
  -moz-animation: example 1s linear infinite;
  animation: example 1s linear infinite;

}

@media only screen and (max-width: 450px) {
  .spinner {
    left: 43%;
  }
}

/* Safari 4.0 - 8.0 */
@-webkit-keyframes example {

  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}

/* Standard syntax */
@keyframes example {
  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}

@media screen and (max-width: 1135px) {
  .invoice-layout {
    .control-panel {
      .search-title {
        padding: 0 15px;
      }

      .pay-button {
        margin-right: 5px;

        .app-button {
          padding: 3px 40px;
        }
      }

      .search-by-invoice, .search-by-date {
        padding-right: 10px;
      }
    }
  }
}

@media screen and (max-width: 1325px) {
  .paga-button {
    margin-left: 40px;
  }

  .grid-container {
    .search-title-small {
      font-size: 16px !important;
    }
  }
}

@media screen and (max-width: 1040px) {
  .invoice-layout {
    .control-panel {
      .pay-button {
        .app-button {
          padding: 3px 15px;
        }
      }
    }
  }
}

@media screen and (max-width: 991px) {
  .invoice-layout {
    .control-panel {
      display: block;
      padding-left: 15px;

      .search-title {
        padding-left: 0;
        width: 100%;
      }

      .control-panel-item {
        margin-top: 10px;
      }

      .search-by-invoice, .search-by-date {
        padding-top: 0;
      }

      .search-by-invoice {
        width: 43%;
      }

      .pay-button {
        width: 100%;
        margin-bottom: 10px;
      }

    }

    .accountant {
      .accountant-message-block {
        .scaduto-label {
          font-size: 14px;
        }
      }
    }
  }

  .billing-centre-container {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .grid-container {
    grid-template-columns: 1fr !important;

    .search-title-small {
      font-size: 18px !important;
    }
  }
}

@media screen and (max-width: 767px) {
  .invoice-layout {
    margin-top: 8%;
    padding: 0;

    .paga-button {
      margin-left: 70px;
    }

    .accountant-message-block.no-padding.ng-star-inserted > div:nth-child(2) {
      margin-top: 2%;
    }

    .custom-tooltip {
      margin-left: 10px;
    }

    .info-circle {
      margin-left: 4%;
    }

    .control-panel {
      .search-by-invoice, .search-by-date {
        width: 80%;

        label {
          width: 30%;
        }
      }
    }
  }

  .billing-centre-container {
    grid-template-columns: repeat(1, 1fr) !important;
  }

  .grid-container {
    .search-title-small {
      font-size: 15px !important;
    }
  }
}

@media screen and (max-width: 505px) {
  .invoice-layout {
    margin-top: 8%;

    .accountant {
      .accountant-message-block .app-button {
        float: none;
        text-align: center;
        margin-right: 0;
      }
    }
  }

  .grid-container {
    .control-panel-small {
      height: 130px !important;
      flex-direction: column;
    }
  }
}

@media screen and (max-width: 480px) {
  .invoice-layout {
    margin-top: 10%;

    .control-panel {
      .search-by-invoice {
        width: 100%;

        label {
          width: auto;
          margin-right: 10px;
        }
      }

      .search-by-date {
        width: 70%;

        label {
          width: auto;
          margin-right: 10px;
        }
      }
    }

  }
}

@media screen and (max-width: 410px) {
  .invoice-layout {
    margin-top: 12%;
  }
}

.modal-title {
  margin: 4% auto auto;
  width: 60%;
  display: block;
  text-align: center;
  font-size: 18px;
  color: #36749d;
  font-weight: 700;
}

.custom-title {
  margin: 0;
  width: 40%;
  font-weight: 100;
  font-size: 22px;
}

.fa-times {
  position: absolute;
  right: 35px;
  font-size: 39px;
  top: 25px;
  color: #36749d;
  cursor: pointer;
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 70%;
  margin: auto;
  padding-top: 10px;
  font-style: italic;
}

.modal-title {
  margin: 4% auto auto;
  width: 60%;
  display: block;
  text-align: center;
  font-size: 18px;
  color: #36749d;
  font-weight: 700;
}

.custom-title {
  margin: 0;
  width: 40%;
  font-weight: 100;
  font-size: 22px;
}

.fa-times {
  position: absolute;
  right: 35px;
  font-size: 39px;
  top: 25px;
  color: #36749d;
  cursor: pointer;
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 70%;
  margin: auto;
  padding-top: 10px;
  font-style: italic;
}

.modal-text-allega {
  text-align: center;
  color: #36749d;
  width: 75%;
  margin: auto;
  font-size: 18px;
  padding-top: 5px;
}

.margin-bottom-allega {
  margin-bottom: 35px !important;
}

.flex-buttons {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: stretch;
  margin-top: 35px;

  .annulla {
    background-color: white;
    color: #36749d;
    border-radius: 10px;
    border: 2px solid #36749C;
    width: 105px;
    height: 45px;
    margin-right: -35px;
  }

  .procedi {
    background-color: #36749d;
    color: white;
    border-radius: 10px;
    border: 2px solid #36749C;
    width: 105px;
    height: 45px;
    margin-left: -35px;
  }
}

.flex-button {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  width: 95%;

  .invia {
    background-color: #36749d;
    color: white;
    border-radius: 10px;
    border: 2px solid #36749C;
    width: 105px;
    height: 45px;
  }
}

select {
  -webkit-appearance: none; /*Removes default chrome and safari style*/
  -moz-appearance: none;
  background: url('/../../../../../assets/img/icons/arrow-down_16x16.png') no-repeat right white;
  background-position-x: 96%;
}

option {
  font-weight: bold;
}

.header-text {
  font-size: 25px;
  text-align: center;
  font-weight: bold;
  margin-bottom: 50px;
}

.margin-top-bottom {
  margin-top: 65px;
  margin-bottom: 30px;
}

.grid-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 15px;

  .control-panel-small {
    display: flex;
    background: #F6F9FB;
    border: 2px solid $menu-border;
    align-items: center;
    justify-content: space-around;
    height: 65px;
    border-radius: 10px;
    padding: 5px;
  }

  .search-title-small {
    font-size: 18px;
  }

  .input-container {
    position: relative;
    width: 147px;
  }

  .input-year {
    width: 100%;
    height: 30px;
    padding-right: 20px;
    box-sizing: border-box;
    border: 1px solid #B3C9DE;
    border-radius: 5px;
    color: black;
    padding-left: 10px;
  }

  /* Chrome, Safari, Edge, Opera */
  .input-year::-webkit-inner-spin-button,
  .input-year::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  .input-year {
    -moz-appearance: textfield;
  }

  .input-container .calendar-icon {
    position: absolute;
    top: 7.5px;
    right: 10px;
    width: 14px;
    height: 15px;
  }

  .input-pod {
    width: 168px;
    height: 30px;
    border: 1px solid #B3C9DE;
    border-radius: 5px;
    text-align: center;
  }

  .input-pod::placeholder {
    text-align: center;
  }

  .cerca-button {
    height: 30px;
    background: #36749A;
    border-radius: 5px;
    border: 0;
    font-size: 16px;
    color: white;
    padding: 0 10px;
  }

  .download-excel-button {
    width: 212px;
    height: 30px;
    cursor: pointer;
  }
}


.billing-centre-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 15px;
  margin-top: 32px;
}

.billing-block {
  cursor: pointer;
  box-shadow: 0 3px 6px #00000029;
  border: 1px solid #F6F9FB;
  border-radius: 10px;
  padding: 20px;
  position: relative;

  &:hover {
    .billing-block-fatture-label-container {
      font-weight: bold;
      color: #36749A;
    }
  }

  .billing-block-header {
    display: flex;
    align-items: center;
    color: #01A8EA;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .billing-block-logo {
    width: 29px;
    height: 19px;
    margin-right: 8px
  }

  .billing-block-label {
    color: #92A9BF;
    font-size: 11px;
  }

  .billing-block-cuu-value {
    font-size: 19px;
    color: #36749A;
    font-weight: bold;
  }

  .billing-block-line {
    margin-top: 15px;
    margin-bottom: 8px;
  }

  .billing-block-fatture-label-container {
    display: flex;
    color: #92A9BF;
    font-size: 11px;
    letter-spacing: 0.55px;
    margin-top: 8px;
    width: 100%;
    justify-content: space-between;
  }
}

.expanded-content {
  position: absolute;
  z-index: 100;
  left: 0;
  right: 0;
  background: white;
  padding: 5px 20px 20px;
  box-shadow: -6px 0 6px -6px #00000029,
  6px 0 6px -6px #00000029,
  0px 5px 5px -5px rgba(0, 0, 0, 0.3);
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.associated-bills-container {
  display: flex;
  align-items: center;
  margin-top: 15px;

  .small-icon {
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }
}

.flex-inputs {
  display: flex;
  flex-direction: column;
  align-items: center;

  .select-options {
    width: 90%;
  }

  .form-control {
    border: 2px solid #82a8c0;
    border-radius: 10px;
    font-weight: bold;
    color: #36749CFF;
    cursor: pointer;
    height: 40px;
    font-size: 15px;
  }

  .description-allega {
    border: 2px solid #82a8c0;
    opacity: 1;
    border-radius: 10px;
    width: 90%;
    height: 90px;
    padding-left: 10px;
    padding-bottom: 50px;
    padding-top: 7px;
    color: #36749CFF;
    margin-top: 15px;
    overflow: hidden;
    resize: none
  }

  ::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: #36749CFF;
    opacity: 1;
    font-size: 15px;
  }

  :-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: #36749CFF;
    font-size: 15px;
  }

  ::-ms-input-placeholder { /* Microsoft Edge */
    color: #36749CFF;
    font-size: 15px;
  }
}

.alegga-button {
  margin-top: 0;
  margin-left: 10px;
  background-color: #36749a;
  color: white;
  font-weight: 100;
}

.clip {
  width: 20px;
  transform: rotate(225deg);
  margin-top: -2px;
}

.flex-allega-pagamento {
  display: flex;
  margin-top: 15px;

  .margin-left-allega {
    margin-left: 32px;
  }

  #files-upload {
    opacity: 0;
    position: absolute;
    z-index: -1;
  }

  .left-part-upload-file {
    width: 350px;
    text-align: center;
    overflow: hidden;

    .file-name {
      display: inline-block;
      margin-top: 6px;
      color: #36749CFF;
    }

    .file-wrong {
      float: left;
      margin-left: 10px;
      color: $coral;
      max-width: 90%;
      overflow: hidden;
      margin-top: 6px;
    }

    .remove-file-button {
      margin-top: 1.5%;
      border: none;
      font-size: 14px;
      background: none;
    }
  }
}

.modal-image {
  text-align: center;
  background-color: white;
  width: 15%;
  display: block;
  margin: 3% auto 30px;
}

.modal-title {
  margin: 4% auto auto;
  width: 60%;
  display: block;
  text-align: center;
  font-size: 18px;
  color: #36749d;
  font-weight: 700;
}

.custom-title {
  margin: 0;
  width: 40%;
  font-weight: 100;
  font-size: 22px;
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 70%;
  padding-top: 10px;
  font-size: 18px;
  margin: 4% auto auto;
}

.fa-times {
  position: absolute;
  right: 35px;
  font-size: 39px;
  top: 25px;
  color: #36749d;
  cursor: pointer;
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 70%;
  margin: auto;
  padding-top: 10px;
  font-style: italic;
}

.modal-text-payment {
  text-align: center;
  color: #36749d;
  width: 70%;
  margin: auto;
  padding-top: 10px;
  font-size: 26px;
}

.container-flex {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: stretch;
  margin: 8%;

  .button-with-icon {
    text-align: center;
    color: #36749C;
    padding: 16px 11px 10px;
    font-size: 18px;
    background-color: white;
    border: 1px solid #36749C;
    border-radius: 13px;
    width: 42%;
  }

  .button-with-icon:hover {
    cursor: pointer;
  }

  .icon-mastercard {
    background: white url("../../../../../assets/img/payment/icon_mastercard_visa.png") no-repeat center;
    background-size: contain;
    height: 37px;
    margin-top: 5%;
    margin-bottom: 7%;
  }

  .icon-paypal {
    background: white url("../../../../../assets/img/payment/icon_paypal.png") no-repeat center;
    background-size: contain;
    height: 32px;
    margin-bottom: 7%;
    margin-top: 6%;
  }
}
