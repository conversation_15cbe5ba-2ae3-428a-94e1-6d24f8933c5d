import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {HttpService} from '../../services/http.service';
import {InvoiceService} from './invoice.service';
import {SharedModule} from '../../shared/shared.module';
import {SelectModule} from 'ng2-select';
import {FilterPipeModule} from 'ngx-filter-pipe';
import {MatTableModule} from '@angular/material';
import {CdkTableModule} from '@angular/cdk/table';
import {CommonModule} from '../../common/common.module';
import {InvoiceComponent} from './containers/invoice-layout/invoice.component';
import {DefermentsInvoiceLayoutComponent} from './containers/deferments-invoice-layout/deferments-invoice-layout.component';
import {DilazioneActions} from '../../redux/dilazione/actions';
import {NumberFormatPipe} from '../../common/pipes/number-pipe/number.pipe';
import {OffersService} from '../../common/services/offers/offers.service';
import {PagamentoFlessibileService} from '../../common/services/pagamento-flessibile/pagamento-flessibile.service';
import {PayPalService} from '../../common/services/paypal/pay-pal-service.service';
import {AttachmentService} from '../fai-da-te/services/attachment/attachment.service';

const routes: Routes = [
  {
    path: 'all',
    component: InvoiceComponent,
  },
  {
    path: 'deferments',
    component: DefermentsInvoiceLayoutComponent
  }
];

@NgModule({
  imports: [
    RouterModule.forChild(routes),
    CommonModule,
    SharedModule,
    SelectModule,
    FilterPipeModule,
    MatTableModule,
    CdkTableModule
  ],
  providers: [HttpService, InvoiceService, DilazioneActions, NumberFormatPipe, OffersService, PagamentoFlessibileService,
  PayPalService, AttachmentService],
  declarations: [InvoiceComponent, DefermentsInvoiceLayoutComponent],
  exports: [
    RouterModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class InvoiceModule {
}




