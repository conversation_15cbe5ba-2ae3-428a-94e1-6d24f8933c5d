import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule as AngularCommon } from '@angular/common';
import { SharedModule } from '../../shared/shared.module';
import { AutoletturaComponent } from './containers/autolettura/autolettura.component';
import { HttpService } from '../../services/http.service';
import { IncidentEventComponent, ConfirmDialogComponent } from './components/incident-event/incident-event.component';
import { NotificationComponent } from './components/notification/notification.component';
import { ToasterService } from 'angular2-toaster';
import { UserGuideAutoletturaComponent } from './components/user-guide/user-guide.component';
import { CommonModule } from '../../common/common.module';

@NgModule({
  imports: [
    AngularCommon,
    SharedModule,
    CommonModule
    // RouterModule.forChild(routes),
  ],
  providers: [HttpService, ToasterService],
  declarations: [AutoletturaComponent, IncidentEventComponent, NotificationComponent, UserGuideAutoletturaComponent, ConfirmDialogComponent],
  entryComponents: [ConfirmDialogComponent],
  exports: [IncidentEventComponent, ConfirmDialogComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AutoletturaModule {
}
