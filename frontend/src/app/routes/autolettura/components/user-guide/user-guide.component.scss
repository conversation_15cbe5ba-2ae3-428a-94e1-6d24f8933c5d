  ul{
    transition:0.5s linear all;
    list-style: none;
    width: 40%;
    float: left;
    position: absolute;
    top: 250px;
    left:30%;


    li{
      margin-top: 40px;
      width: 100%;
    }
  }
  input{
    width:100%;
  }
  .mainBlock{
    float: left;
    width: 20%;
    margin-left: 25%;
    position: absolute;
    top: 150px;
    left:30%;
    //z-index: 2550;
  }

.fixedBackground {
  width: 100%;
  top: 0;
  height: 100vh;
  position: fixed;
  overflow-y: hidden;
  z-index: 2600;
  background-color: rgba(1, 1, 1, 0.4);
  left: 0;

  .fa-times {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 20px;
    color: rgba(1, 1, 1, 0.5);
  }
}
//.mda-checkbox > em:before {
//  background: white;
//}
.containerMain {
  position: fixed;
  top: 150px;
  left:20%;
  width: 70%;
  z-index: 2500;
  .panel {
    height: 81vh;
  }
}
 .block{
   position: absolute;
   top: 250px;
   left:30%;
   width: 70%;
   z-index: 2550;
 }
.checkBlock{
  height: 30px;
}
div.tooltips {
  position: relative;
  display: inline;
}
div.tooltips span {
  position: absolute;
  width:140px;
  color: #000000;
  background: #ffffff;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 6px;
  transition:0.5s linear all;

}
div.tooltips span:after {
  content: '';
  position: absolute;


}

.left span :after{
  left: 100%;
  border-left: 8px solid #ffffff;
  width: 0; height: 0;
  top: 50%;
  margin-top: -8px;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
}
.right span:after{
  right: 100%;
  border-right: 8px solid #ffffff;
  margin-top: -8px;
  width: 0; height: 0;
  top: 50%;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
}

  .energiaBox {
    z-index: 2550;
  }
  .gasBox {
    z-index: 2550;
  }
.energiaBox span, .gasBox span{
  visibility: hidden;
  opacity: 0.8;
  margin-top: -15px;
  margin-left: 15px;
  z-index: 999;
}
  .gasBox span{
    visibility: hidden;
    opacity: 0.8;
    margin-top: -15px;
    margin-left: 15px;
    z-index: 999;
  }
  .ee span {
    margin-left: 180px;
    margin-top: -30px;
    visibility: hidden;
  }
.f span{
  margin-left: 20px;
  visibility: hidden;
}
  .eesend span{
    margin-left: 100px;
    margin-top: -30px;
    visibility: hidden;
  }
.energiaMainBlock{
  top: 250px;
  left: 50%;
  position: absolute;
  z-index: 2500;
  display: none;
}
 .f1{
   margin-top: 100px;
 }
  .f2{
    margin-top: 180px;
  }
  .f3{
    margin-top: 260px;
  }
  .eeSend {
    margin-top: 340px;
  }
