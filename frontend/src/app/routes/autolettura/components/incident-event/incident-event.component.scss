@import "~app/shared/styles/colors";

.panel {
  box-shadow: none;
  border-radius: 0;
}

.app--autolettura {
  .app-select {
    width: 100%;
    font-weight: bold;
  }

  ::ng-deep .app-input {
    font-weight: bold;
  }

  .contract-type-select {
    margin-right: 2px;
    width: 99%;
  }

  .autolettura-info {
    padding-left: 1px !important;

    .info-circle {
      height: 22px;
      width: 22px;
      line-height: 20px;
      font-size: 18px;
    }

  }

  .page-title {
    background: #ffffff;
    border: 2px solid $menu-border;
    border-radius: 5px;
    height: 45px;

    .title-image {
      background: url("/assets/img/optima/Set_Icone_AreaClienti_Autolettura2.png") no-repeat center;
      background-size: contain;
      width: 68px;
      height: 58px;
      float: left;
    }

    .title-text {
      color: $dark-blue;
      margin-top: 11px;
      float: left;
    }
  }

  .autoletura-card {
    margin-top: 2%;
  }

  .app--panel-block {
    border: 1px solid $menu-border;
    border-radius: 5px;
  }

  .panel-heading {
    text-align: center;
    color: $dark-blue;
    font-size: 20px;
    font-weight: bold;
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .app--panel-body {
    border-top: 1px solid $menu-border;
    border-left: none;
    border-right: none;
    background-color: $menu-background;
    min-height: 468px;
    height: 469px;
  }

  .app--panel-footer {
    background-color: #ffffff;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
  }

  .app--icon {
    height: 60px;
    width: 60px;
  }

  .app--icon-aut1 {
    background: url("../../../../../assets/img/autolettura/aut1.jpg") no-repeat center;
    background-size: contain;
  }

  .app--icon-aut2 {
    background: url("../../../../../assets/img/autolettura/aut2.png") no-repeat center;
    background-size: contain;
  }

  .app--icon-contatoreGas {
    background: url("../../../../../assets/img/autolettura/contatoreGas.jpg") no-repeat center;
    background-size: contain;
  }

  .app--icon-contatoreGasDigit {
    background: url("../../../../../assets/img/autolettura/contatoreGasDigit.jpg") no-repeat center;
    background-size: contain;
  }

  .app--form-group {
    label {
      font-size: 12px;
      color: $dark-blue;
    }

    .form-control {
      border-radius: 5px;
    }
  }

  .app--btn-inserisci {
    display: block;
    margin: 0 auto;
    border: 1px solid $green;
    background-color: $green;
    color: white;
    padding: 5px 15px;
    border-radius: 5px;
  }

  .app--form-group-popup {
    .app--input-group-addon-hide {
      visibility: hidden;
    }

    .input-group-addon {
      padding: 0 10px;
      background-color: transparent;
      border: none;
      vertical-align: bottom;
      padding-bottom: 8px;
    }

    .title-image-upload {

      text-align: center;

      .ee-image-upload, .gas-image-upload {
        width: 100%;
      }

      .upload-image {
        margin-top: 15px;
        width: 50%;
        height: 10%;
      }

      input {
        display: none;
      }

      .file-name {
        display: inline-block;
        margin-top: 10px;
        max-width: 90%;
        overflow: hidden;
      }

      .file-wrong {
        display: inline-block;
        margin-top: 10px;
        color: $coral;
        max-width: 90%;
        overflow: hidden;
      }

      .remove-file-button {
        margin-top: 0.5%;
        border: none;
        font-size: 14px;
        background: none;
      }
    }
  }


  .app--popup-icon {
    display: inline-block;
  }

  .app--popup-title {
    display: inline-block;
    vertical-align: top;
    font-weight: 500;
  }
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  width: 600px;
  height: 430px;
  margin: auto;
  top: 20vh;
  background: white;
  border: 2px solid #B0C7DD;
  border-radius: 30px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.fa-times {
  position: absolute;
  right: 30px;
  font-size: 48px;
  top: 25px;
  color: #36749d;
  cursor: pointer;
}

.modal-image {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
}

.modal-text {
  font-size: 25px;
  text-align: center;
  color: #36749A;
  margin-top: 20px;
  width: 80%;
}


@media screen and (max-width: 991px) {
  .app--autolettura {
    .page-title {
      display: none;
    }

    .panel-heading {
      padding-top: 25px;
      padding-bottom: 25px;
      font-weight: 400;
    }

    .app--panel-body {
      min-height: 300px;
    }

    .app--panel-body, .app--panel-footer {
      background-color: #ffffff;
    }

    .panel-footer {
      border-top: none;
    }

  }
}

@media screen and (max-width: 500px) {
  .app--autolettura {

    .panel-body {
      padding: 15px;
    }

    .app--form-group-popup {
      .input-group-addon {
        padding-right: 0;
      }
    }
  }
  .inner-modal-div {
    width: 95%;
  }
  .modal-text {
    font-size: 20px;
  }
}
