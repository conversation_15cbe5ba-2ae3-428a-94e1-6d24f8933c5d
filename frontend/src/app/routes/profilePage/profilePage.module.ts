import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {SelectModule} from 'ng2-select';

import {SharedModule} from '../../shared/shared.module';
import {UserDataComponent} from './containers/userData/userData.component';
import {UserContractComponent} from './userContract/userContract.component';
import {UserComunicazioniComponent} from './containers/userComunicazioni/user-comunicazioni.component';
import {GroupByPipe, UserTuttoInUnoComponent} from './containers/userTuttoInUno/user-tutto-in-uno.component';


import {HomeService} from '../home/<USER>/home/<USER>';
import {HttpService} from '../../services/http.service';
import {UserServicesService} from './userServices/userServices.service';
import {CdkTableModule} from '@angular/cdk/table';
import {MatTableModule} from '@angular/material';
import {CommonModule} from '../../common/common.module';
import {RouteService} from '../../layout/submenu/route.service';
import {LayoutModule} from '../../layout/layout.module';
import {ChartsModule} from 'ng2-charts';
import {ProfileLayoutComponent} from './containers/profile-layout/profile-layout.component';
import {ShipmentComponent} from './shipment/shipment.component';
import {ShipmentService} from './shipment/shipment.service';
import {HistoricalUtilitiesComponent} from './containers/historical-utilities/historical-utilities.component';
import {ConfirmPaymentComponent} from './containers/confirm-payment/confirm-payment.component';
import {ResolutionCheckerGuard} from './utils/resolution-checker.guard';
import {ScontiComponent} from './containers/sconti/sconti.component';
import {ConfirmDeactivateGuard} from '../../services/confirm-deactivate.guard';
import {PaymentService} from '../../common/services/payment/payment.service';
import { EnergyCardComponent } from './containers/energy-card/energy-card.component';
import { MobileCardComponent } from './containers/mobile-card/mobile-card.component';

const routes: Routes = [
  {
    path: '', component: ProfileLayoutComponent, children: [
      {path: '',  canActivate: [ResolutionCheckerGuard]},
      {path: 'data', component: UserDataComponent},
      {path: 'data/:actionParam', component: UserDataComponent},
      {path: 'confirm-payment', component: ConfirmPaymentComponent},
      {path: 'shipment', component: ShipmentComponent},
      {path: 'contract', component: UserContractComponent},
      {path: 'comunicazioni', component: UserComunicazioniComponent},
      {path: 'tutto-in-uno', component: UserTuttoInUnoComponent, canDeactivate: [ConfirmDeactivateGuard]},
      {path: 'tutto-in-uno/sconti', component: ScontiComponent},
      {path: 'storico/utenze', component: HistoricalUtilitiesComponent},
      {path: 'card/energy', component: EnergyCardComponent},
      {path: 'card/mobile', component: MobileCardComponent}
    ]
  }
];

@NgModule({
  imports: [
    LayoutModule,
    SharedModule,
    RouterModule.forChild(routes),
    SelectModule,
    MatTableModule,
    CdkTableModule,
    CommonModule,
    ChartsModule
  ],
  providers: [
    HttpService,
    UserServicesService,
    RouteService, ShipmentService,
    HomeService,
    ResolutionCheckerGuard,
    ConfirmDeactivateGuard,
    PaymentService
  ],
  declarations: [
    UserDataComponent,
    UserContractComponent,
    UserComunicazioniComponent,
    UserTuttoInUnoComponent,
    ProfileLayoutComponent,
    ShipmentComponent,
    HistoricalUtilitiesComponent,
    ConfirmPaymentComponent,
    ScontiComponent,
    GroupByPipe,
    EnergyCardComponent,
    MobileCardComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [
    RouterModule,
  ]
})
export class ProfilePageModule {
}
