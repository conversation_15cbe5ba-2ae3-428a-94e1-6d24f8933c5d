<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 shipment-layout">

  <div class="col-lg-12 col-md-12 page-title">
    <div class="title-image"></div>
    <div class="text">CONTROLLA STATO E DETTAGLIO DELLE TUE SPEDIZIONI</div>
  </div>

  <div *ngIf="!shipments || shipments.length===0" class="col-lg-12 col-md-12 no-result">
    <h4>Non risulta alcuna spedizione associata al tuo codice cliente.</h4>
  </div>

  <div class="shipments" *ngFor="let transaction of shipments">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 creation-date">
      <div class="col-lg-8 col-md-8 col-sm-6 col-xs-6 no-padding date-creazione"><b style="color: #e2513b">Data
        Creazione</b></div>
      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-6 no-padding date" style="color: #e2513b">
        {{transaction.startDate | date : "dd/MM/y" }}
      </div>
      <div class="shipment-layout-desktop">
        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 shipment-info no-padding column-desktop">
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 header cf-pi">CF/PI</div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 header indirizzo">Indirizzo di spedizione</div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 table-body cf-pi bottom-left-radius-5">
            <span *ngIf="transaction.cf"> {{transaction.cf}}</span>
            <span *ngIf="transaction.piva"> {{transaction.piva}} </span>
          </div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 table-body indirizzo bottom-right-radius-5">
          <span *ngIf="transaction.shipmentAddress.address">
            {{transaction.shipmentAddress.address}}
          </span>
          </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 shipment-location no-padding column-desktop">
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 header border-right-mobile">CAP</div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 header comune">Comune</div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 table-body bottom-left-radius-5 border-right-mobile">
          <span *ngIf="transaction.shipmentAddress.postalCode">
            {{transaction.shipmentAddress.postalCode}}
          </span>
          </div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 table-body comune-body bottom-right-radius-5">
          <span *ngIf="transaction.shipmentAddress.common">
            {{transaction.shipmentAddress.common}}
          </span>
          </div>
        </div>
      </div>
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 element-type no-padding">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 header type">Tipo di articolo</div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 table-body bottom-left-radius-5 bottom-right-radius-5">
          <span *ngIf="transaction.shipmentHeader.articleType">{{transaction.shipmentHeader.articleType}}</span>
        </div>
      </div>
      <div class="shipment-data-status-aligner">
        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 shipment-data no-padding">
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 header mac">Matricola</div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 header">Quantità</div>
          <div class="form-aligner">
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 table-body mac bottom-left-radius-5" style="height: auto">
              <span *ngIf="transaction.shipmentHeader.number">{{transaction.shipmentHeader.number}}</span>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 table-body " style="height: auto">
              <span *ngIf="transaction.listArticoli?.length">{{transaction.listArticoli[0].qta}}</span>
            </div>
          </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 shipment-status no-padding">
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 header status">Stato spedizione</div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 header">Data modifica stato</div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 table-body status">
            <span *ngIf="transaction.shipmentHeader.status">{{transaction.shipmentHeader.status}}</span>
          </div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 bottom-right-radius-5 table-body">
         <span *ngIf="transaction.shipmentHeader.modificationDate">
           {{transaction.shipmentHeader.modificationDate| date : "dd/MM/y"}}
         </span>
          </div>
        </div>
      </div>
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding tracking-info">
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 casual header border-right-mobile">Causale</div>
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 tracking-number header">Tracking number</div>
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 table-body bottom-left-radius-5 border-right-mobile">
          <span *ngIf="!transaction.shipmentHeader.causal">-</span>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 table-body bottom-right-radius-5">
        <span *ngIf="transaction.shipmentHeader.trackingNr">
          {{transaction.shipmentHeader.trackingNr}}
        </span>
        </div>
      </div>
    </div>
  </div>
</div>
