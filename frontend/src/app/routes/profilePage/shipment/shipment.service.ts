import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import {Shipment} from './shipment.model';

import 'rxjs/add/operator/delay';
import 'rxjs/add/operator/do';

@Injectable()
export class ShipmentService {

  readonly url = 'api/shipment';
  constructor(private http: HttpClient) {  }

  public getInvoiceData(): Observable<Shipment[]> {
    const headers = new HttpHeaders({'clientid': localStorage.getItem('clientId')});
    return this.http.get<Shipment[]>(this.url, {headers: headers})
      ;
  }

}
