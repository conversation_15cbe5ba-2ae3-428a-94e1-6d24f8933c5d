@import "~app/shared/styles/colors";

.shipment-layout {
  color: $dark-blue;

  .shipment-layout-desktop {
    width: 100%;
  }

  .no-result {
    margin-top: 2%;
  }

  .date-creazione {
    padding-bottom: 5px;
  }

  .page-title {
    background: #ffffff;
    border: 2px solid $menu-border;
    border-radius: 5px;
    height: 45px;

    .title-image {
      background: url("/assets/img/optima/Set_Icone_AreaClienti_Spediziono.png") no-repeat center;
      background-size: contain;
      width: 68px;
      height: 50px;
      float: left;
    }

    .text {
      color: $dark-blue;
      margin-top: 11px;
      float: left;
    }
  }

  .shipments {
    margin-top: 2%;
    float: left;
    width: 100%;
  }

  .creation-date {
    padding-top: 10px;
    padding-bottom: 10px;
    border: 1px solid $menu-border;
    border-radius: 5px;
    background-color: #ffffff;

    .date {
      text-align: right;
    }
  }

  .shipment-info, .shipment-location, .element-type {
    margin-top: 2%;
  }

  .shipment-data-status-aligner {
    display: flex;
    border: 0.1px solid transparent;
  }

  .form-aligner {
    display: flex;
    border: 0.1px solid transparent;
    position: relative;
    height: calc(100% - 41px);
  }

  .shipment-data, .shipment-status {
    height: auto;
  }

  .shipment-info, .shipment-location, .element-type, .shipment-data, .shipment-status, .tracking-info {
    border: 1px solid $menu-border;
    text-align: center;
    margin-bottom: 0;

    .header {
      background: $menu-background;
      padding-top: 10px;
      padding-bottom: 10px;

    }

  }

  .indirizzo, .comune, .comune-body {
    border-left: 1px solid $menu-border;
  }

  .element-type {
    border-bottom: none;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    margin-top: 2%;

    .header {
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
    }
  }

  .shipment-data, .shipment-status {
    border-top: none;

    .mac, .status {
      border-right: 1px solid $menu-border;
    }
  }

  .shipment-data {
    border-bottom-left-radius: 5px;
  }

  .shipment-status {
    border-left: none;
    border-bottom-right-radius: 5px;
  }

  .table-body {
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .header {
    font-weight: 600;

    &.cf-pi {
      border-top-left-radius: 5px;
    }

    &.comune {
      border-top-right-radius: 5px;
    }
  }

  .shipment-info {
    border-bottom-left-radius: 5px;
    border-top-left-radius: 5px;
    border-right: none;
  }

  .shipment-location {
    border-bottom-right-radius: 5px;
    border-top-right-radius: 5px;
  }

  .tracking-info {
    margin-top: 2%;
    border-radius: 5px;

    .casual {
      border-top-left-radius: 5px;
    }

    .tracking-number {
      border-top-right-radius: 5px;
    }
  }
}

@media screen and (min-width: 992px) {
  .shipment-layout {
    .shipments {
      .shipment-layout-desktop {
        display: inline-flex;
        border: 1px solid #c8d9e9;
        border-radius: 5px;
      }

      .shipment-layout-desktop > .column-desktop {
        display: table-cell;
        width: 50%;
      }

      .shipment-layout-desktop {
        .shipment-info {
          margin-top: 0;
          border: none;
          border-right: 1px solid #c8d9e9;

          .header {
            padding-right: 0;
            padding-left: 0;
          }
        }

        .shipment-location {
          margin-top: 0;
          border: none;

          .comune-body {
            height: calc(100% - 41px);
          }

          .header {
            padding-right: 0;
            padding-left: 0;
          }
        }
      }
    }

    .creation-date {
      margin-bottom: 2%;
    }
  }

}

@media screen and (max-width: 991px) {
  .shipment-layout {
    padding: 0;

    .page-title {
      display: none;
    }

    .table-body {
      background-color: #ffffff;
    }

    .shipment-info, .shipment-location,
    .element-type{
      border-radius: 5px;
      border: 1px solid $menu-border;

      .header {
        &:nth-child(1) {
          border-top-left-radius: 5px;
        }

        &:nth-child(2) {
          border-top-right-radius: 5px;
        }
      }
    }

    .shipment-data, .shipment-status {
      margin-top: 2%;
      border: 1px solid $menu-border;

      .header {
        &:nth-child(1) {
          border-top-left-radius: 5px;
        }

        &:nth-child(2) {
          border-top-right-radius: 5px;
        }
      }
    }

    .shipment-data{
      border-top-left-radius: 5px;
    }

    .shipment-status{
      border-top-right-radius: 5px;
    }

    .bottom-left-radius-5 {
      border-bottom-left-radius: 5px;
    }

    .bottom-right-radius-5 {
      border-bottom-right-radius: 5px;
    }

    .border-right-mobile {
      border-right: 1px solid $menu-border;
    }

  }
}

@media screen and (max-width: 450px) {
  .shipment-layout {
    .header, .table-body {
      font-size: 12px;
      padding-right: 5px;
      padding-left: 5px;
    }
  }
}
