import {Component, OnInit, ViewChild} from '@angular/core';
import * as R from 'ramda';
import {Contract} from '../../../common/model/contract/userContract.model';
import {ContractService} from '../../../common/services/contracts/userContract.service';
import {PdfService} from '../../../common/services/pdf/pdf.service';
import {select} from "@angular-redux/store";
import {Observable} from "rxjs/Observable";
import {UserData} from "../../../common/model/userData.model";
import {Subscription} from "rxjs/Subscription";
import {ObservableUtils} from "../../../common/utils/ObservableUtils";
import {IncidentEventService} from "../../../common/services/incedentEvent/incident-event.service";
import {IncidentEventResponse} from "../../autolettura/model/IncidentEventResponse";


@Component({
  selector: 'app-user-contract',
  templateUrl: './userContract.component.html',
  styleUrls: ['./userContract.component.scss']
})

export class UserContractComponent implements OnInit {
  // table
  @ViewChild('myTable') myTable: any;
  //

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;
  userInfoSubscription: Subscription;

  contracts: Array<Contract>;

  contoRelax = false;
  dataLoaded = false;
  emailIsPresent: boolean;
  showModalWindowForUserWithEmail = false;
  showModalWindowForUserWithoutEmail = false;

  constructor(private contractService: ContractService, private pdfService: PdfService, private incidentEventService: IncidentEventService) {
    this.userInfoSubscription = this.userInfo.subscribe(userData => this.emailIsPresent = !!userData.email);

    this.contractService.getContractData().subscribe(contracts => {
      this.contracts = contracts;
      const setContoRelax = x => {
        R.ifElse(
          R.not(R.isNil(x['idContrattoPI'])),
          this.contoRelax = true
        );
      };
      if (this.contracts) {
        R.forEach(setContoRelax, this.contracts);
      }
      this.contracts = this.convert(this.contracts);
      this.dataLoaded = true;
    });
  }


  convert(array) {
    const map = {};
    for (let i = 0; i < array.length; i++) {
      const obj = array[i];
      obj.items = [];

      map[obj.id] = obj;

      const parent = obj.idContrattoPI || '-';
      if (!map[parent]) {
        map[parent] = {
          items: []
        };
      }
      map[parent].items.push(obj);
    }

    return map['-'].items;

  }


  ngOnInit() {
    if (this.dataLoaded) {
      this.dataLoaded = false;
      this.myTable.rowDetail.expandAllRows();
    }
  }

/*downloadContractPdf(contractId) {
    this.pdfService.downloadTariffTransparencyPDF(localStorage.getItem('clientId'), contractId);
  }*/

  downloadContractsPdf(fileName: string) {
    this.pdfService.downloadContractsPDF(fileName).subscribe(res => {
      const blob = new Blob([res], {type: "application/pdf"});
      const data = window.URL.createObjectURL(blob);
      window.open(data, '_blank');
    });
  }

  showModalWindowForUser(numberContract: number) {
    if (this.emailIsPresent) {
      this.incidentEventService.openIncidentEventForContractCopy(numberContract).subscribe((data: IncidentEventResponse) => {
        if (data && data.status === 'OK') {
          this.showModalWindowForUserWithEmail = true;
        }
      });
    } else {
      this.showModalWindowForUserWithoutEmail = true;
    }
  }

  hideModalWindow() {
    this.showModalWindowForUserWithEmail = false;
    this.showModalWindowForUserWithoutEmail = false;
  }

  showContractButtons(item): boolean {
    if (item && item.descTipoContratto) {
      const description = item.descTipoContratto.toLowerCase();
      return description.indexOf('adsl') > -1 || description.indexOf('voice') > -1 || description.indexOf('cps') > -1
        || description.indexOf('wlr') > -1 || description.indexOf('voip') > -1;
    }
    return false;
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.userInfoSubscription]);
  }

}
