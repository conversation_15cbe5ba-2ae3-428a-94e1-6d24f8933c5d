<div class="col-md-9">
  <div class="col-lg-12 page-title">
    <div class="title-image"></div>
    <div class="text font-weight-bold">MOBILE CARD</div>
  </div>
  <div class="app--user-mobile-card">

    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 mobile-card-border-style" *ngIf="showWriteCardBlock">
      <div class="font-weight-bold label-text text-center-block">Digita il codice della tua Mobile Card per accedere al
        voucher Mobile con cui potrai pagare le tue prossime fatture o ricariche MOBILE Optima
      </div>
      <div class="input-container" [formGroup]="formGroup">
        <input type="text" class="text-uppercase" placeholder="CODICE MOBILE CARD" formControlName="voucher">
        <button class="btn-conferma" (click)="checkCode()">CONFERMA</button>
      </div>
      <span class="text-danger" *ngIf="formGroup.controls['voucher'].hasError('required') &&
       (formGroup.controls['voucher'].dirty || formGroup.controls['voucher'].touched)">Campo Obbligatorio</span>

      <!--Block with already used voucher-->
      <div *ngIf="showAlreadyUsedCard">
        <hr>
        <div class="font-weight-bold label-text">LE TUE MOBILE CARD</div>
        <div class="totale-block">
          <div>
            <div class="label-text font-weight-bold">Totale</div>
            <div><i>Credito Mobile Card disponibile</i></div>
          </div>
          <div class="font-size-18 font-weight-bold">{{totalSumVouchers ? totalSumVouchers : 0}} €</div>
        </div>
      </div>

      <div *ngFor="let card of usedCardsInformation">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 short-items-mobile-card font-weight-bold"
             (click)="card.showGeneralInformation = !card.showGeneralInformation">
          Mobile Card n. {{card.codiceCoupon}} <img *ngIf="!card.showGeneralInformation"
                                                    src="/assets/img/icons/arrow-down_16x16.png"
                                                    alt="Arrow">
          <img *ngIf="card.showGeneralInformation" style="rotate: 180deg" src="/assets/img/icons/arrow-down_16x16.png"
               alt="Arrow">
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 details-mobile-card" *ngIf="card.showGeneralInformation">
          <div class="col-lg-4 col-md-4 col-sm-4 col-xs-7 margin-down">Importo</div>
          <div class="col-lg-8 col-md-8 col-sm-8 col-xs-5 font-weight-bold margin-down">{{card.importCoupon ? card.importCoupon : '0'}} €</div>
          <div class="col-lg-4 col-md-4 col-sm-4 col-xs-7 margin-down">Data attivazione</div>
          <div
            class="col-lg-8 col-md-8 col-sm-8 col-xs-5 font-weight-bold margin-down">{{card.dataAttivazione | date:'dd/MM/yyyy'}}</div>
          <div class="col-lg-4 col-md-4 col-sm-4 col-xs-7 margin-down">Credito residuo Mobile Card</div>
          <div class="col-lg-8 col-md-8 col-sm-8 col-xs-5 font-weight-bold margin-down">{{card.creditoResiduo ? card.creditoResiduo : '0'}} €
          </div>

          <div *ngIf="!card.showContractsInDetails"
               class="col-lg-12 col-md-12 col-sm-12 col-xs-12 font-weight-bold short-items-table"
               (click)="card.showContractsInDetails = !card.showContractsInDetails">
            Contratto e
            utenze associate <img src="/assets/img/icons/arrow-down_16x16.png"
                                  alt="Arrow">
          </div>

          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 border-table no-padding"
               *ngIf="card.showContractsInDetails">
            <div
              (click)="card.showContractsInDetails = !card.showContractsInDetails"
              class="col-lg-12 col-md-12 col-sm-12 col-xs-12 header-table border-bottom-table background-header border-header font-weight-bold short-items-table-contracts">
              Contratto e utenze associate <img style="rotate: 180deg" src="/assets/img/icons/arrow-down_16x16.png"
                                                alt="Arrow">
            </div>
            <div
              class="col-lg-4 col-md-4 col-sm-4 col-xs-4 border-right-table cell-style border-bottom-table background-header">
              Data stipula
            </div>
            <div
              class="col-lg-4 col-md-4 col-sm-4 col-xs-4 border-right-table cell-style border-bottom-table background-header">
              Indirizzo
            </div>
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 cell-style border-bottom-table background-header">
              SIM <img class="cell-image" src="assets/img/service-icons/icon_mobile_small.png" alt="mobile"></div>

            <div
              class="col-lg-4 col-md-4 col-sm-4 col-xs-4 cell-style ">{{card.dataStipula | date : 'dd/MM/yyyy'}}</div>
            <div
              class="col-lg-4 col-md-4 col-sm-4 col-xs-4 border-left-table border-right-table cell-style">{{card.indirizzo}}</div>
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 cell-style">{{card.sim}}</div>

          </div>

          <div *ngIf="!card.showAgreementsInDetails"
               class="col-lg-12 col-md-12 col-sm-12 col-xs-12 font-weight-bold short-items-table"
               (click)="card.showAgreementsInDetails = !card.showAgreementsInDetails">
            Storico Movimentazioni <img src="/assets/img/icons/arrow-down_16x16.png"
                                        alt="Arrow">
          </div>

          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 border-table no-padding"
               *ngIf="card.showAgreementsInDetails">
            <div
              (click)="card.showAgreementsInDetails = !card.showAgreementsInDetails"
              class="col-lg-12 col-md-12 col-sm-12 col-xs-12 header-table border-bottom-table background-header border-header font-weight-bold short-items-table-contracts">
              Storico Movimentazioni <img style="rotate: 180deg" src="/assets/img/icons/arrow-down_16x16.png"
                                          alt="Arrow">
            </div>
            <div *ngIf="card.numeroFattura"
              class="col-lg-2 col-md-6 col-sm-6 col-xs-6 border-right-table cell-style border-bottom-table background-header font-weight-bold">
              Fattura n.
            </div>
            <div *ngIf="card.numeroFattura"
              class="col-lg-3 col-md-6 col-sm-6 col-xs-6 border-right-table cell-style border-bottom-table background-header font-weight-bold">
              Importo Mobile Card in fattura
            </div>
            <div *ngIf="isMediumDevices && card.numeroFattura"
                 class="col-md-6 col-sm-6 col-xs-6 border-right-table cell-style border-bottom-table">{{card.numeroFattura}}</div>
            <div *ngIf="isMediumDevices && card.numeroFattura"
                 class="col-md-6 col-sm-6 col-xs-6 cell-style border-bottom-table">{{card.importoTipoCardInFattura ?
              card.importoTipoCardInFattura + ' €' : ''}}
              €
            </div>
            <div *ngIf="card.numeroFattura"
              class="col-lg-3 col-md-4 col-sm-4 col-xs-4 border-right-table cell-style border-bottom-table background-header font-weight-bold">
              Fattura di riferimento
            </div>
            <div *ngIf="card.numeroFattura"
              class="col-lg-3 col-md-4 col-sm-4 col-xs-4 cell-style border-bottom-table background-header font-weight-bold">
              Data emissione Fattura
            </div>
            <div *ngIf="card.numeroFattura"
              class="col-lg-1 col-md-4 col-sm-4 col-xs-4 border-left-table cell-style border-bottom-table background-header font-weight-bold">
              Scarica
            </div>
            <div *ngIf="!isMediumDevices && card.numeroFattura"
                 class="col-lg-2 col-md-2 col-sm-2 col-xs-2 border-right-table cell-style">{{card.numeroFattura}}</div>
            <div *ngIf="!isMediumDevices && card.numeroFattura"
                 class="col-lg-3 col-md-3 col-sm-3 col-xs-3 border-right-table cell-style">{{card.importoTipoCardInFattura ?
              card.importoTipoCardInFattura + ' €' : ''}}
            </div>
            <div *ngIf="card.numeroFattura"
              class="col-lg-3 col-md-4 col-sm-4 col-xs-4 border-right-table cell-style">{{card.fatturaRiferimento}}</div>
            <div *ngIf="card.numeroFattura"
              class="col-lg-3 col-md-4 col-sm-4 col-xs-4 cell-style">{{card.dataEmissione | date:'dd/MM/yyyy'}}</div>
            <div *ngIf="card.numeroFattura"
              class="col-lg-1 col-md-4 col-sm-4 col-xs-4 border-left-table cell-style font-weight-bold cell-button-download"
              (click)="card.numeroFattura ? downloadFile(card.downloadUrl) : ''">{{card.numeroFattura ? '↓ PDF' : ''}}
            </div>
          </div>

        </div>
      </div>


    </div>

    <!--Block for connect card with number or contract -->
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 mobile-card-border-style"
         *ngIf="showBlockForChooseNumber || showBlockForChooseContract">
      <div class="font-weight-bold header-text">La tua Mobile Card è pari a {{cardValue}} €</div>

      <div *ngIf="showBlockForChooseNumber">
<!--        <div class="label-text"><i>L’importo verrà trasferito sul credito residuo della
          tua SIM</i>
        </div>-->
        <hr>
        <div class="font-weight-bold label-text">Per utilizzare il voucher è necessario associarlo a un numero Optima
        </div>

        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 no-padding" [formGroup]="formGroupChooseNumber">
          <div class="input-number-container">
            <label for="msisdnId">Scegli il numero</label>
            <select id="msisdnId" class="form-control" formControlName="msisdnId">
              <option value=""></option>
              <option *ngFor="let record of contractRecords" [attr.value]="record.msisdnId">
                SIM {{record.msisdnId}}
              </option>
            </select>
          </div>
          <span class="text-danger show" *ngIf="formGroupChooseNumber.controls['msisdnId'].hasError('required') &&
             (formGroupChooseNumber.controls['msisdnId'].dirty || formGroupChooseNumber.controls['msisdnId'].touched)"
                style="margin-bottom: 10px;">Campo Obbligatorio</span>
          <button class="btn-conferma btn-prosegui" (click)="chooseNumber()">PROSEGUI</button>
        </div>
      </div>

      <div *ngIf="showBlockForChooseContract">
        <div class="label-text"><i>Potrai usufruire di questo importo per pagare le
          tue
          prossime fatture Optima relativamente alla quota parte della spesa Mobile, fino a svuotamento della carta</i>
        </div>
        <hr>
        <div class="font-weight-bold label-text">Per utilizzare il voucher è necessario associarlo a un contratto Optima
        </div>
        <div class="font-weight-bold header-text" style="margin-top: 15px" *ngIf="contracts.length === 0">Non ci sono contratti associabili
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding">
          <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 border-table no-padding font-weight-bold"
               *ngFor="let contract of contracts" [formGroup]="formGroupChooseContract">
            <div
              class="col-lg-12 col-md-12 col-sm-12 col-xs-12 header-table border-bottom-table background-header border-header">
              <input type="radio" formControlName="option" value="{{contract.idContratto}}" name="option">Contratto
              №{{contract.idGruppo}}
            </div>
            <div
              class="col-lg-4 col-md-4 col-sm-4 col-xs-4 border-right-table cell-style border-bottom-table background-header">
              Data stipula
            </div>
            <div
              class="col-lg-4 col-md-4 col-sm-4 col-xs-4 border-right-table cell-style border-bottom-table background-header">
              Indirizzo
            </div>
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 cell-style border-bottom-table background-header">
              SIM <img class="cell-image" src="assets/img/service-icons/icon_mobile_small.png" alt="mobile"></div>

            <div
              class="col-lg-4 col-md-4 col-sm-4 col-xs-4 cell-style min-height">{{contract.dataStipula | date : 'dd/MM/yyyy'}}</div>
            <div
              class="col-lg-4 col-md-4 col-sm-4 col-xs-4 border-left-table border-right-table cell-style min-height">{{contract.indirizzoFornitura}}</div>
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 cell-style min-height">{{contract.msisdn}}</div>
          </div>
        </div>
        <span class="text-danger show" *ngIf="formGroupChooseContract.controls['option'].hasError('required') &&
       (formGroupChooseContract.controls['option'].dirty || formGroupChooseContract.controls['option'].touched)"
              style="margin-bottom: 10px;">Campo Obbligatorio</span>
        <button *ngIf="contracts.length" class="btn-conferma btn-prosegui" (click)="chooseContract()">PROSEGUI</button>
      </div>
    </div>
  </div>
</div>
<div class="modal-div show" *ngIf="showErrorWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow()"></i>
    <img class="image modal-image" src="/assets/img/icons/Alert.png" alt="Alert">
    <div class="modal-text">Il codice inserito non è valido</div>
  </div>
</div>

<div class="modal-div show" *ngIf="showSuccessWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="reloadPage()"></i>
    <img class="image modal-image" src="/assets/img/icons/ok.png" alt="OK">
    <div class="modal-text" style="font-size: 30px"><b>Hai attivato la tua Mobile Card!</b></div>
    <div *ngIf="showBlockForChooseNumber" class="modal-text" style="font-size: 20px">IIl voucher sarà utilizzato per
      pagare la ricarica associata alla
      <b>SIM {{this.formGroupChooseNumber.value.msisdnId}}</b></div>
    <div *ngIf="showBlockForChooseContract" class="modal-text" style="font-size: 20px">Il voucher sarà utilizzato per
      pagare la tua prossima fattura Optima relativamente alla quota parte della spesa Mobile del tuo
      contratto<b> № {{this.chosenContract.idGruppo}}</b>
    </div>
  </div>
</div>
<app-error-window *ngIf="showGeneralErrorWindow" [onClose]="reloadPage"></app-error-window>
