<div class="container-fluid">
  <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 profile-layout">
    <div class="main">
     <!-- <div class="top">
        {{userSurname}} {{userName}}
      </div>-->
      <div class="buttons-group">
        <div routerLinkActive="active" class="link order-10" routerLink="/profile/data"
             (click)="switchOpenClose('/profile/data')">
          <span class='text left'>I tuoi dati</span>
          <span class='fa fa-angle-right arrow right'></span>
        </div>
        <div routerLinkActive="active" class="link order-20" routerLink="/profile/contract"
             (click)="switchOpenClose('/profile/contract')">
          <span class='text left'>I tuoi contratti</span>
          <span class='fa fa-angle-right arrow right'></span>
        </div>
        <div routerLinkActive="active" *ngIf="(hasActiveOffers | async) && (hasTutoInUnoActive | async)" class="link order-30"
             routerLink="/profile/tutto-in-uno" (click)="switchOpenClose('/profile/tutto-in-uno')" >
          <span class='text left'>Il tuo tutto-in-uno</span>
          <span class='fa fa-angle-right arrow right'></span>
        </div>
        <div routerLinkActive="active" class="link  order-40" routerLink="/profile/shipment"
             (click)="switchOpenClose('/profile/shipment')">
          <span class='text left'>Le tue spedizioni</span>
          <span class='fa fa-angle-right arrow right'></span>
        </div>
        <div *ngIf="hasActiveServices | async" routerLinkActive="active" class="link order-50"
             routerLink="/profile/comunicazioni" (click)="switchOpenClose('/profile/comunicazioni')">
          <span class='text left'>Le tue comunicazioni</span>
          <span class='fa fa-angle-right arrow right'></span>
        </div>
        <div [routerLinkActive]="'active'" *ngIf="hasInactiveServices | async"
             class="link order-60" routerLink="/profile/storico/utenze"
             (click)="switchOpenClose('/profile/storico/utenze')">
          <span class='text left'>Storico utenze</span>
          <span class='fa fa-angle-right arrow right'></span>
        </div>
        <div *ngIf="activeServices['ENERGIA'] || activeServices['GAS']" routerLinkActive="active" class="link  order-70" routerLink="/profile/card/energy"
             (click)="switchOpenClose('/profile/card/energy')">
          <span class='text left'>ENERGY CARD</span>
          <span class='fa fa-angle-right arrow right'></span>
        </div>
        <div *ngIf="activeServices['MOBILE']" routerLinkActive="active" class="link  order-80" routerLink="/profile/card/mobile"
             (click)="switchOpenClose('/card/mobile')">
          <span class='text left'>MOBILE CARD</span>
          <span class='fa fa-angle-right arrow right'></span>
        </div>
        <div *ngIf="isMobile" class="profile-mobile-layout order-{{mobileLayoutOrder}}">
          <router-outlet></router-outlet>
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="!isMobile" class="profile-desktop-layout">
    <router-outlet></router-outlet>
  </div>
</div>
<app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>
