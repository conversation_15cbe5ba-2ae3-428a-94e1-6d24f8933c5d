import {Component, HostListener, OnDestroy, OnInit, Pipe, PipeTransform, ViewChild} from '@angular/core';
import 'rxjs/add/observable/forkJoin';
import {select} from '@angular-redux/store';
import {Subscription} from 'rxjs/Subscription';
import {Observable} from 'rxjs/Observable';
import {OffersService} from '../../../../common/services/offers/offers.service';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';
import {NormalizeUtils} from '../../../../common/utils/NormalizeUtils';
import {UserData} from '../../../../common/model/userData.model';
import {ModalComponent} from '../../../../common/components/modal/modal.component';
import {
  allInOneChartConfiguration,
  bonusInitialeAnnotation,
  initialBonusLogEnricher,
  normalizeInitialBonusLog
} from '../../chart/chartConfiguration';
import {ChartBuilder} from '../../../../common/utils/chart-builder/chart-builder';
import {InitialBonusProgress} from '../../../../common/model/offers/InitialBonusProgress';
import {ServiceStatus} from '../../../../common/enum/ServiceStatus';
import {DialogModalActions} from '../../../../redux/dialogModal/actions';
import {IncidentEventService} from '../../../../common/services/incedentEvent/incident-event.service';
import {OtpService} from '../../../../common/services/otp/otp.service';
import {
  getChangePromoMeseRequest,
  getMeseModalData,
  getModalDataWithUnSaved,
  getProfileNegativeBalanceModal,
  getProfileRemodulationRequestModalData,
  infoModalAboutChronologicalAndConsecutiveMonths,
  infoModalAboutUnsavedPromoChangesModalData,
  infoModalAfterPromoMeseChange,
  profileNegativeBalanceModalWithNoPhoneAndEmail,
  successModalAfterPromoMeseChange
} from '../../config/config';
import {Offer} from '../../../../redux/model/Offer';
import {MonthModalActions} from '../../../../redux/monthModal/actions';
import {MonthEntity} from '../../../../common/model/monthModal/MonthEntity';
import {formatDateWithSlashesToJsDate} from '../../utils/dateUtils';
import {PromoMeseChangeStatus} from '../../../../common/model/offers/PromoMeseChangeStatusWrapper';
import {PromoMeseOff} from '../../../../common/model/offers/PromoMeseOff';
import {ChangePersonalDataResponse} from '../../../../common/model/ChangePersonalDataResponse';

@Pipe({name: 'groupBy'})
export class GroupByPipe implements PipeTransform {
  transform(array: Array<any>, field: string): Array<any> {
    array = array.filter(arrayElement => arrayElement[field] !== null);

    const groupedObj = array.reduce((prev, cur) => {
      if (!prev[cur[field]]) {
        prev[cur[field]] = [cur];
      } else {
        prev[cur[field]].push(cur);
      }
      return prev;
    }, {});
    return Object.keys(groupedObj).map(key => ({key, value: groupedObj[key]}));
  }
}


@Component({
  selector: 'app-user-tutto-in-uno',
  templateUrl: './user-tutto-in-uno.component.html',
  styleUrls: ['./user-tutto-in-uno.component.scss']
})
export class UserTuttoInUnoComponent implements OnInit, OnDestroy {

  clientOffersSubscription: Subscription;
  userInfoSubscription: Subscription;
  promoMeseSubscription: Subscription;
  incidentEventSubscription: Subscription;
  changePromoMeseSubscription: Subscription;

  clientBundlesMap = {};

  initialBonusProgress: InitialBonusProgress;

  chartConfigs: object;
  isChartExistMap: Map<number, boolean>;

  promoMeseOff: Map<number, Array<PromoMeseChangeStatus>>;
  oldPromoMese: Map<number, Array<PromoMeseOff>>;

  promoMeseOffChangeStatus = false;
  canRedirectWithUnSavedChanges = false;

  @select(['user', 'activeOffers'])
  clientActiveOffers: Observable<any>;

  @select(['user', 'hasActiveOffers'])
  hasActiveOffers: Observable<boolean>;

  @select(['user', 'userInfo'])
  private userInfo: Observable<UserData>;

  private accountType: string;

  @ViewChild(ModalComponent)
  appModal: ModalComponent;

  @ViewChild('infoModal')
  infoModal: ModalComponent;

  tariffInfo: string;

  userNumber: string;

  userEmail: string;

  light = false;

  displayTariffTable = false;

  displayMeseOffTableMap: Map<number, boolean>;

  meseOffButtonStyleMap: Map<number, string>;

  tariffButtonStyle = '';

  $stabilizedOffers: Observable<Array<Offer>>;
  $unstabilizedOffers: Observable<Array<Offer>>;

  groupedMobileOffer = null;

  constructor(private offersService: OffersService,
              private dialogModalActions: DialogModalActions,
              private monthModalActions: MonthModalActions,
              private incidentEventService: IncidentEventService,
              private otpService: OtpService) {
    this.userInfoSubscription = this.userInfo.subscribe(user => {
      if (user && user.cluster) {
        this.accountType = user.cluster.value;
        this.userNumber =
          user.mobileNumber && (user.mobileNumber.startsWith('3') || user.mobileNumber.startsWith('00393'))
            ? user.mobileNumber
            : user.phoneNumber && (user.phoneNumber.startsWith('3') || user.phoneNumber.startsWith('00393'))
            ? user.phoneNumber
            : null;
        this.userEmail = user.email;
      }
    });

    this.isChartExistMap = new Map<number, boolean>();

    this.$stabilizedOffers = this.clientActiveOffers.map(items => items.filter(value => value.offerType === 4));
    this.$unstabilizedOffers = this.clientActiveOffers.map(items => items.filter(value => value.offerType !== 4));
  }

  ngOnInit() {
    this.initComponent();
    this.getPromoMeseOffListAndInitPropertyMaps();
  }

  initComponent() {
    this.clientOffersSubscription = this.clientActiveOffers.flatMap((offers: any) => {
        return Observable.forkJoin(this.buildOffersAdditionalDataRequests(offers));
      }
    ).subscribe();
  }

  checkYourTariff(type, pod) {
    this.offersService.checkYourTariff(this.accountType, type, this.buildTariffCode(pod)).subscribe(response => {
      this.tariffInfo = response;
      this.appModal.showHideDialog();
    });
  }

  buildTariffCode(pod) {
    const {clientBundlesMap} = this;
    if (pod && clientBundlesMap && clientBundlesMap[pod]) {
      const bundlesList = clientBundlesMap[pod];
      const podDetail = bundlesList.length === 1 ? bundlesList[0] :
        bundlesList.find(bundle => bundle.stato.Codice === ServiceStatus.ATTIVABILE);
      if (podDetail) {
        const {listinominus, listinoplus} = podDetail;
        if (listinominus && listinominus.codice && listinoplus && listinoplus.codice) {
          return listinoplus.codice + listinominus.codice;
        }
      }
    }
    return null;
  }

  buildOffersAdditionalDataRequests(offers) {
    const observables = [];
    offers.forEach(value => {
      observables.push(this.offersService.loadAndamentoContoRelax(localStorage.getItem('clientId'),
        value.billingId).map(response => {
        this.initialBonusProgress = Object.assign({}, this.initialBonusProgress, {[value.billingId]: response});
        this.chartConfigs = Object.assign({}, this.chartConfigs, {[value.billingId]: this.buildChartConfig(response, value.billingId)});
      }));
      observables.push(this.offersService.governanceOfferClientBundleDetails(localStorage.getItem('clientId'),
        value.billingId).map(response => {
        this.clientBundlesMap = Object.assign({}, this.clientBundlesMap,
          NormalizeUtils.groupByExpression(response, item => item.pod ? item.pod : item.pdr));
      }));
    });
    return observables;
  }

  buildChartConfig(initialBonusProgress: InitialBonusProgress, billingId: number) {
    if (initialBonusProgress && initialBonusProgress.initialBonusLog.length) {
      const {currentInitialBonus, initialBonusLog} = initialBonusProgress;
      const initialBonusAnnotation = bonusInitialeAnnotation();
      initialBonusAnnotation['value'] = currentInitialBonus;
      const normalizedBonusesInilaleLogs = normalizeInitialBonusLog(initialBonusLog);
      this.isChartExistMap.set(billingId, true);
      return ChartBuilder.builder().withConfiguration(allInOneChartConfiguration())
        .withAnnotations([initialBonusAnnotation])
        .withEnricher(initialBonusLogEnricher(normalizedBonusesInilaleLogs))
        .build();
    }
    this.isChartExistMap.set(billingId, false);
    return null;
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.clientOffersSubscription, this.userInfoSubscription,
      this.promoMeseSubscription, this.incidentEventSubscription, this.changePromoMeseSubscription]);
  }

  switchDisplayTariffTable() {
    this.displayTariffTable = !this.displayTariffTable;
    this.tariffButtonStyle === 'active' ? this.tariffButtonStyle = '' : this.tariffButtonStyle = 'active';
  }

  switchDisplayMeseOffTable(idFatt: number) {
    this.displayMeseOffTableMap.set(idFatt, !this.displayMeseOffTableMap.get(idFatt));

    const currentMeseOffDisplayStyle = this.meseOffButtonStyleMap.get(idFatt);
    this.meseOffButtonStyleMap.set(idFatt, currentMeseOffDisplayStyle === 'active' ? '' : 'active');
  }

  openNegativeBalanceModal(idFattura: number, pagaBalance: number): void {
    this.dialogModalActions.showDialogModal(!this.userNumber && !this.userEmail
      ? profileNegativeBalanceModalWithNoPhoneAndEmail
      : getProfileNegativeBalanceModal(this.otpService,
        this.userNumber, this.userEmail, this.incidentEventService, String(idFattura), pagaBalance));
  }

  openRemodulationRequestModal(idFattura: string): void {
    this.dialogModalActions.showDialogModal(getProfileRemodulationRequestModalData(this.incidentEventService, idFattura));
  }

  confirmareButtonHandler(idFatt: number) {
    // const callback = this.confermareModalSubmit.bind(this);
    // const {mofidiedPromoMonthNames, unmofidiedPromoMonthNames} = this.getModifiedAndUnmodifiedMonthNames();
    // this.dialogModalActions.showDialogModal(getConfermareModalData(mofidiedPromoMonthNames, unmofidiedPromoMonthNames, callback));
    // Temporary direct request to backend
    if (this.isSelectedMonthsConsecutiveOrNotChronological(idFatt)) {
      this.dialogModalActions.showDialogModal(infoModalAboutChronologicalAndConsecutiveMonths);
    } else {
      this.confermareModalSubmit(idFatt);
    }
  }

  isSelectedMonthsConsecutiveOrNotChronological(idFatt: number): boolean {
    const promoForCheck = this.promoMeseOff.get(idFatt);
    for (let i = 0; i < promoForCheck.length - 1; i++) {
      const firstDateToCompare = formatDateWithSlashesToJsDate(promoForCheck[i].mese.dataInizioValidita);
      const secondDateToCompare = formatDateWithSlashesToJsDate(promoForCheck[i + 1].mese.dataInizioValidita);

      firstDateToCompare.setMonth(firstDateToCompare.getMonth() + 1);
      if ((firstDateToCompare.getMonth() === secondDateToCompare.getMonth()) && (firstDateToCompare.getFullYear() === secondDateToCompare.getFullYear())) {
        return true;
      }
      if (firstDateToCompare >= secondDateToCompare) {
        return true;
      }
    }
    return false;
  }

  getModifiedAndUnmodifiedMonthNames(idFatt: number) {
    const modifiedPromoMese: PromoMeseChangeStatus[] = this.promoMeseOff.get(idFatt).filter(promoMese => promoMese.isChanged);
    const mofidiedPromoMonthNames = modifiedPromoMese.map(filteredMese => this.getMonthNameByDate(filteredMese.mese.dataInizioValidita)).join(', ');
    const unmofidiedPromoMonthNames = modifiedPromoMese
      .map(modifiedMese => this.getMonthNameByDate(this.oldPromoMese.get(idFatt)[modifiedMese.meseIndex].dataInizioValidita)).join(', ');

    return {mofidiedPromoMonthNames, unmofidiedPromoMonthNames};
  }

  confermareModalSubmit(idFatt: number) {

    const modifiedPromoMese: Array<PromoMeseOff> = this.promoMeseOff.get(idFatt).map(promo => promo.mese);
    const {mofidiedPromoMonthNames, unmofidiedPromoMonthNames} = this.getModifiedAndUnmodifiedMonthNames(idFatt);

    this.offersService.checkIncidentEventPromoMeseChange().subscribe(isOpenChangePromoIncidentEvent => {

      if (isOpenChangePromoIncidentEvent) {
        this.dialogModalActions.showDialogModal(infoModalAfterPromoMeseChange);
      } else {

        this.incidentEventSubscription = this.incidentEventService.variazionePromoMese(unmofidiedPromoMonthNames, mofidiedPromoMonthNames)
          .subscribe(incidentEventResponse => {

            this.changePromoMeseSubscription = this.offersService.changePromoMeseRequest(getChangePromoMeseRequest(
              localStorage.getItem('clientId'), idFatt, incidentEventResponse.incidentId, modifiedPromoMese, this.oldPromoMese.get(idFatt)))
              .subscribe((changePromoResponse: ChangePersonalDataResponse) => {

                if (changePromoResponse.errorStatus && changePromoResponse.errorStatus.code === '400') {
                  this.dialogModalActions.showDialogModal(infoModalAfterPromoMeseChange);
                } else if (changePromoResponse.errorStatus && changePromoResponse.errorStatus.code === '200') {
                  this.dialogModalActions.showDialogModal(successModalAfterPromoMeseChange);
                }
                this.promoMeseOffChangeStatus = false;
                setTimeout(() => window.location.reload(), 3000);
              });
          });
      }
    });

  }

  callbackForSelectMonthModal(selectedMonth: MonthEntity, meseIndex: number) {
    this.promoMeseOffChangeStatus = true;
    this.canRedirectWithUnSavedChanges = false;

    const promoMeseChangeStatusWrapper = this.promoMeseOff.get(selectedMonth.idFatt)[meseIndex];
    promoMeseChangeStatusWrapper.isChanged = true;
    promoMeseChangeStatusWrapper.mese.dataInizioValidita = selectedMonth.firstDate;
    promoMeseChangeStatusWrapper.mese.dataFineValidita = selectedMonth.lastDate;


    this.monthModalActions.sendUnsavedStatusToSubscribers(getModalDataWithUnSaved(this.promoMeseOffChangeStatus));
    this.monthModalActions.hideMonthModal();
  }

  compareInferioreDateWithPreviousSelectedMeseDate(currentMeseIndex: number, idFatt: number): string {
    if (currentMeseIndex - 1 < 0) {
      return;
    } else {
      return this.promoMeseOff.get(idFatt)[currentMeseIndex - 1].mese.dataInizioValidita;
    }
  }

  openMeseModal(idFatt: number, dataLimiteInferiore: string, dataLimiteSuperiore: string, currentSelectedMeseDate: string, meseIndex: number) {
    const unclickableDateFrom = this.compareInferioreDateWithPreviousSelectedMeseDate(meseIndex, idFatt);

    const monthsToShow = this.fillRangeOfMonths(idFatt, dataLimiteInferiore, dataLimiteSuperiore, currentSelectedMeseDate, unclickableDateFrom);
    const callback = this.callbackForSelectMonthModal.bind(this);

    this.monthModalActions.showMonthModal(getMeseModalData(dataLimiteInferiore, dataLimiteSuperiore, monthsToShow,
      this.getMonthNameByDate(currentSelectedMeseDate), meseIndex, callback));
  }


  fillRangeOfMonths(idFatt: number, dataLimiteInferiore: string, dataLimiteSuperiore: string, selectedMeseDate: string, unclickableDateFrom: string): Array<MonthEntity> {
    const monthsRangeWithYear: MonthEntity[] = [];

    const from = formatDateWithSlashesToJsDate(dataLimiteInferiore);
    const to = formatDateWithSlashesToJsDate(dataLimiteSuperiore);
    const parsedUnclickableDateFrom = unclickableDateFrom ? formatDateWithSlashesToJsDate(unclickableDateFrom) : formatDateWithSlashesToJsDate('01/01/1980');

    const currentMonth = new Date().getMonth().valueOf() + 1;
    const currentYear = new Date().getFullYear().valueOf();
    const selectedMonth = formatDateWithSlashesToJsDate(selectedMeseDate).getMonth().valueOf() + 1;
    const selectedYear = formatDateWithSlashesToJsDate(selectedMeseDate).getFullYear();

    while (from <= to) {
      const month = new Date(from).getMonth().valueOf() + 1;
      const year = new Date(from).getFullYear();
      const lastDay = new Date(year, month + 1, 0).getDate();

      const {firstMonthDate, lastMonthDate} = month > 9 ? {
        firstMonthDate: `01/${month}/${year}`,
        lastMonthDate: `${lastDay}/${month}/${year}`
      } : {
        firstMonthDate: `01/0${month}/${year}`,
        lastMonthDate: `${lastDay}/0${month}/${year}`
      };

      const unclickableMonthFrom = parsedUnclickableDateFrom.getMonth().valueOf() + 1;
      const unclickableYearFrom = parsedUnclickableDateFrom.getFullYear().valueOf();

      monthsRangeWithYear.push({
        title: this.getMonthNameByDate(`1/${month}/${year}`),
        idFatt: idFatt,
        clickable: year > currentYear || (year >= currentYear && month > currentMonth),
        selected: selectedYear === year && selectedMonth === month,
        unableToSelect: year <= unclickableYearFrom && month <= unclickableMonthFrom,
        firstDate: firstMonthDate,
        lastDate: lastMonthDate,
      });
      from.setMonth(from.getMonth() + 1);
    }
    return monthsRangeWithYear;
  }

  allowRedirectAndShowDeactivateInfoModalWithUnSavedChanges(): Observable<boolean> | boolean {
    if (this.promoMeseOffChangeStatus && !this.canRedirectWithUnSavedChanges) {
      this.dialogModalActions.showDialogModal(infoModalAboutUnsavedPromoChangesModalData);
      this.canRedirectWithUnSavedChanges = true;
      return false;
    }

    return true;
  }

  groupBy(array, property) {
    return array.reduce((accumulator, object) => {
      const key = object[property];
      if (!accumulator[key]) {
        accumulator[key] = [];
      }
      accumulator[key].push(object);
      return accumulator;
    }, {});
  }

  getEmptyVoceAmount(activeOffer: Offer): number {
    return activeOffer.voce.filter(voceItem => voceItem.descrizione === null).length;
  }

  getEmptyMobileAmount(activeOffer: Offer): number {
    return activeOffer.mobile.filter(mobileItem => mobileItem.nomeOfferta === null).length;
  }

  getEmptyAssicurazioneAmount(activeOffer: Offer): number {
    return activeOffer.assicurazione.filter(item => item.tipoPolizza === null).length;
  }

  getLengthOfGroupedArray(array, property): number {
    if (array === undefined || array === null || array.filter(arrayElement => arrayElement[property] !== null).length === 0) {
      return 0;
    } else {
      array = array.filter(arrayElement => arrayElement[property] !== null);
      return Object.keys(this.groupBy(array, property)).length;
    }
  }

  getPromoMeseOffListAndInitPropertyMaps() {
    const clientId = localStorage.getItem('clientId');
    this.promoMeseSubscription = this.offersService.getPromoMeseOff(clientId).subscribe(promoMeseArr => {

      this.promoMeseOff = new Map<number, Array<PromoMeseChangeStatus>>(
        promoMeseArr.map(meseItem => [meseItem.idFatt,
          meseItem.promo.map((promoItem, promoIndex) => ({
            meseIndex: promoIndex,
            isChanged: false,
            mese: promoItem
          }) as PromoMeseChangeStatus)] as [number, Array<PromoMeseChangeStatus>]));

      this.oldPromoMese = new Map<number, Array<PromoMeseOff>>(
        promoMeseArr.map(meseItem => [meseItem.idFatt,
          meseItem.promo.map(promoItem => Object.assign({}, promoItem))] as [number, Array<PromoMeseOff>]));

      this.displayMeseOffTableMap = new Map<number, boolean>(promoMeseArr.map(meseItem => [meseItem.idFatt, false] as [number, boolean]));
      this.meseOffButtonStyleMap = new Map<number, string>(promoMeseArr.map(meseItem => [meseItem.idFatt, ''] as [number, string]));
    });
  }

  getMonthNameByDate(date: string): string {
    const monthNumber = Number(date.split('/')[1]);
    const year = date.split('/')[2];
    // 1 = Gennaio
    const monthNames: Array<string> = ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
      'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'];
    return monthNames[monthNumber - 1] + ' ' + year;
  }

  checkIfMeseIsModifiable(date: string): boolean {
    const meseMonth = Number(date.split('/')[1]);
    const meseYear = Number(date.split('/')[2]);
    const currentMonth = (new Date().getMonth().valueOf() + 1);
    const currentYear = new Date().getFullYear().valueOf();
    return meseYear > currentYear || (meseYear >= currentYear && meseMonth > currentMonth);
  }

  @HostListener('window:beforeunload', ['$event'])
  beforeUnloadHandler(event: Event) {
    if (!this.allowRedirectAndShowDeactivateInfoModalWithUnSavedChanges()) {
      event.preventDefault();
      return false;
    }
  }
}
