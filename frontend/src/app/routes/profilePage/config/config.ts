import {ProfileLayoutRoutes} from '../../../configuration/routes';
import {DialogModalEntity} from '../../../common/model/dialogModal/DialogModalEntity';
import {Observable} from 'rxjs/Observable';
import {OTP} from '../../../common/model/otp/OTP';
import {OtpService} from '../../../common/services/otp/otp.service';
import {IncidentEventService} from '../../../common/services/incedentEvent/incident-event.service';
import {IncidentEventResponse} from '../../autolettura/model/IncidentEventResponse';
import {MonthEntity} from '../../../common/model/monthModal/MonthEntity';
import {ChangePromoMeseRequest} from '../../../common/model/offers/ChangePromoMeseRequest';
import {PromoMeseOff} from '../../../common/model/offers/PromoMeseOff';
import {formatDateToStringMonthAndYearWithSlashes, formatDateWithSlashesToJsDate} from '../utils/dateUtils';
import {ChangeSilttamentoProssimaRequest} from '../../../common/model/offers/ChangeSilttamentoProssimaRequest';

export const profileMobileLayoutOrdersMap = {
  [ProfileLayoutRoutes.USER_DATA]: 11,
  // [ProfileLayoutRoutes.CONFIRM_PAYMENT]: 11,
  [ProfileLayoutRoutes.YOUR_CONTRACTS]: 21,
  [ProfileLayoutRoutes.ALL_IN_ONE]: 31,
  [ProfileLayoutRoutes.SHIPMENTS]: 41,
  [ProfileLayoutRoutes.COMMUNICATION]: 51,
  [ProfileLayoutRoutes.UTILITIES_HISTORY]: 61,
  [ProfileLayoutRoutes.ENERGY_CARD]: 71,
  [ProfileLayoutRoutes.MOBILE_CARD]: 81
};

export const profileNegativeBalanceModalWithNoPhoneAndEmail = {
  text: `Procedendo, il saldo negativo del Conto Relax ad
  oggi maturato, ti verrà addebitato nella prima fattura utile.

  Il valore dell’importo addebitato in fattura
  sarà contestualmente stornato dal
  Saldo del tuo Conto Relax.

  Se la prima fattura utile coincide con la fattura di
  chiusura del periodo contrattuale, il Saldo negativo del Conto
  Relax ad oggi maturato sarà contabilizzato nel saldo di fine anno.`,
  hasButtons: true,
  img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png',
  nextEntities: [
    {
      text: `Per procedere è necessario registrare un recapito mobile o un indirizzo email nella sezione Profilo/I tuoi dati.`,
      img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png'
    } as DialogModalEntity
  ]
} as DialogModalEntity;

function getNegativeBalanceModalData(
  otpService: OtpService,
  userNumber: String,
  userEmail: String,
  incidentEventService: IncidentEventService,
  idFattura: string,
  pagaBalance: number): DialogModalEntity {
  return {
    text: `Procedendo, il saldo negativo del Conto Relax ad
  oggi maturato, ti verrà addebitato nella prima fattura utile.
  Il valore dell’importo addebitato in fattura
  sarà contestualmente stornato dal
  Saldo del tuo Conto Relax.
  Se la prima fattura utile coincide con la fattura di
  chiusura del periodo contrattuale, il Saldo negativo del Conto
  Relax ad oggi maturato sarà contabilizzato nel saldo di fine anno.`,
    hasButtons: true,
    img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png',
    callbackToExecuteOnSubmit: (): Observable<any> => {
      return otpService.sendOTP();
    },
    nextEntities: [
      {
        text: `Per procedere inserisci il codice che abbiamo
        appena inoltrato al tuo numero/indirizzo email [${userNumber ? userNumber : userEmail}]`,
        hasFillField: true,
        hasButtons: true,
        callbackToExecuteOnSubmit: (enteredOTP): Observable<OTP> => {
          return otpService.checkOTP(enteredOTP);
        },
        callbackByValidationResult: () => {
          incidentEventService.balancePaymentRequest(0, idFattura, pagaBalance).subscribe();
        }
      } as DialogModalEntity,
      {
        title: `Abbiamo preso in carico la tua
        richiesta.`,
        text: `Riceverai l'addebito del Conto Relax
        negativo sulla prima fattura utile.`,
        img: '/assets/img/icons/ok.png'
      } as DialogModalEntity
    ]
  } as DialogModalEntity;
}

export {getNegativeBalanceModalData as getProfileNegativeBalanceModal};

function getRemodulationRequestModalData(
  incidentEventService: IncidentEventService,
  idFattura: string): DialogModalEntity {
  let incidentEventResponse: IncidentEventResponse = null;
  return {
    text: `Cliccando su PROCEDI inoltrerai
    una richiesta di modifica del tuo
    attuale canone Tutto-In-Uno in linea
    con i tuoi effettivi consumi`,
    hasButtons: true,
    callbackToExecuteOnSubmit: (): Observable<IncidentEventResponse> => {
      return incidentEventService.remodulationProfileRequest(0, idFattura).map(resp => {
        return incidentEventResponse = {
          message: 'Non è possibile procedere, in quanto per questo Tutto-In-Uno risulta già una ' +
            'richiesta di rimodulazione in corso',
          incidentId: resp.incidentId,
          status: resp.status,
          ticketNumber: resp.ticketNumber
        } as IncidentEventResponse;
      });
    },
    nextEntities: [
      {
        title: `Abbiamo inoltrato la tua richiesta al
        Servizio Clienti Optima.`,
        text: `Sarai ricontattato dal tuo Assistente
        Optima entro le possime 24h
        lavorative.`,
        img: '/assets/img/icons/ok.png',
      } as DialogModalEntity]
  } as DialogModalEntity;
}

export {getRemodulationRequestModalData as getProfileRemodulationRequestModalData};

export const debitRequestSucceedModalText: string = 'La tua richiesta di addebito ricorrente ' +
  'su carta di credito delle fatture Optima è stata presa in carico, riceverai entro ' +
  '48 ore un messaggio di conferma attivazione della nuova modalità di pagamento.';

function getConfermareModalData(
  updatedMonths: string,
  unmodifiedMonths: string,
  submitCallback: any): DialogModalEntity {
  return {
    title: 'Confermare',
    text: `Hai scelto di spostare il mese di erogazione dello sconto da [${unmodifiedMonths}] a [${updatedMonths}].`,
    hasButtons: true,
    customButtons: {
      cancel: 'ANNULA',
      accept: 'CONFERMA'
    },
    callbackToExecuteOnSubmit: submitCallback
  } as DialogModalEntity;
}

export {getConfermareModalData as getConfermareModalData};

function getMeseModalEntity(
  dataLimiteInferiore: string,
  dataLimiteSuperiore: string,
  monthToShow: Array<MonthEntity>,
  currentMese: string,
  meseIndex: number,
  submitCallback: any): DialogModalEntity {
  return {
    title: 'Mese modal',
    text: `Scegli il nuovo mese
        di applicazione dello sconto.
        [${currentMese}]`,
    hasButtons: true,
    data: monthToShow,
    currentMese: currentMese,
    currentMeseIndex: meseIndex,
    callbackToExecuteOnSubmit: submitCallback
  } as DialogModalEntity;
}

export {getMeseModalEntity as getMeseModalData};

function prepareChangePromoMeseRequest(
  clientId: string,
  idFatt: number,
  incidentId: string,
  updatedPromoMeseOff: PromoMeseOff[],
  oldPromoMeseOff: PromoMeseOff[]
): ChangePromoMeseRequest {

  const mese1New: PromoMeseOff = updatedPromoMeseOff[0];
  const mese2New: PromoMeseOff = updatedPromoMeseOff[1];
  const mese3New: PromoMeseOff = updatedPromoMeseOff[2];

  const mese1Old: PromoMeseOff = oldPromoMeseOff[0];
  const mese2Old: PromoMeseOff = oldPromoMeseOff[1];
  const mese3Old: PromoMeseOff = oldPromoMeseOff[2];

  const dateMese1Old = formatDateWithSlashesToJsDate(mese1Old.dataInizioValidita);
  const dateMese2Old = formatDateWithSlashesToJsDate(mese2Old.dataInizioValidita);
  const dateMese3Old = formatDateWithSlashesToJsDate(mese3Old.dataInizioValidita);

  const dateMese1New = formatDateWithSlashesToJsDate(mese1New.dataInizioValidita);
  const dateMese2New = formatDateWithSlashesToJsDate(mese2New.dataInizioValidita);
  const dateMese3New = formatDateWithSlashesToJsDate(mese3New.dataInizioValidita);

  return {
    customerId: clientId,
    incidentId: incidentId,
    change: {
      changeType: 400,
      idFatt: idFatt,
      mese1: formatDateToStringMonthAndYearWithSlashes(dateMese1Old),
      mese2: formatDateToStringMonthAndYearWithSlashes(dateMese2Old),
      mese3: formatDateToStringMonthAndYearWithSlashes(dateMese3Old),
      mese1Nuovo: formatDateToStringMonthAndYearWithSlashes(dateMese1New),
      mese2Nuovo: formatDateToStringMonthAndYearWithSlashes(dateMese2New),
      mese3Nuovo: formatDateToStringMonthAndYearWithSlashes(dateMese3New),
      mese1DataInizio: mese1Old.dataInizioValidita,
      mese2DataInizio: mese2Old.dataInizioValidita,
      mese3DataInizio: mese3Old.dataInizioValidita,
      mese1DataFine: mese1Old.dataFineValidita,
      mese2DataFine: mese2Old.dataFineValidita,
      mese3DataFine: mese3Old.dataFineValidita,
      codPromo: mese1Old.codPromo,
      servizi: 'VAdGECAm',
      attributes: JSON.stringify(mese1Old.promoAttributes),
      servizi1: mese1Old.servizi,
      servizi2: mese2Old.servizi,
      servizi3: mese3Old.servizi,
    }
  } as ChangePromoMeseRequest;
}

export {prepareChangePromoMeseRequest as getChangePromoMeseRequest};

function getUnSavedModalData(inUnsaved: boolean): DialogModalEntity {
  return {
    isUnsaved: inUnsaved
  } as DialogModalEntity;
}

export {getUnSavedModalData as getModalDataWithUnSaved};

export const infoModalAfterPromoMeseChange = {
  text: `Risulta una richiesta di modifica in corso. È necessario completare l’operazione prima di modificare altre date.`,
  img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png'
} as DialogModalEntity;

export const successModalAfterPromoMeseChange = {
  text: `La configurazione della promo è stata effettuata con successo.`,
  img: '/assets/img/icons/ok.png'
} as DialogModalEntity;

export const infoModalAboutUnsavedPromoChangesModalData = {
  text: 'Attenzione, se abbandoni la pagina senza cliccare sul tasto CONFERMA LA VARIAZIONE, le modifiche richieste non verranno salvate.',
  img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png'
} as DialogModalEntity;

export const infoModalAboutChronologicalAndConsecutiveMonths = {
  text: `Attenzione, per l’applicazione degli sconti i mesi  scelti devono essere in ordine cronologico e non consecutivi. Per favore,  modifica nuovamente i mesi e riprova`,
  img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png'
} as DialogModalEntity;


function prepareChangeDateScandezaRequest(
  idFatt: number,
): ChangeSilttamentoProssimaRequest {
  return {
    idFatt: idFatt,
  } as ChangeSilttamentoProssimaRequest;
}

export {prepareChangeDateScandezaRequest as getChangeDateScandezaRequest};

export const successModalAfterDateScandezaChange = {
  text: `La tua richiesta è stata inoltrata.La prossima fattura avrà una scadenza posticipata di 15 giorni.`,
  img: '/assets/img/icons/ok.png'
} as DialogModalEntity;

export const infoModalAfterDateScandezaChange = {
  text: `La richiesta non può essere gestita, in quanto le ultime tre fatture non risultano pagate con domiciliazione bancaria.`,
  img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png'
} as DialogModalEntity;

export const infoModalAboutUnsavedDateScandezaChangesModalData = {
  text: 'Hai già usufruito del servizio per 3 volte nell’anno contrattuale corrente. Non possono essere inserite più di 3 richieste nel corso dell’anno contrattuale.',
  img: '/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png'
} as DialogModalEntity;
