import ChartConfig, { DataSet } from '../../../common/utils/chart-builder/ChartConfig';
import { NormalizeUtils } from '../../../common/utils/NormalizeUtils';
import { InitialBonusLog } from '../../../common/model/offers/InitialBonusProgress';
import * as moment from 'moment';

moment.locale('it');
const dateFormat = 'MMM YYYY';
const months = moment.monthsShort();
const isMiddleDesktop = window.innerWidth < 1500;
const isMobile = window.innerWidth <= 991;

export const normalizeInitialBonusLog = (initialBonuses: Array<InitialBonusLog>) => {
  const sorted = initialBonuses.sort((item1, item2) => item1.date - item2.date);
  return NormalizeUtils.normalizeListWithExprssion(sorted, item => moment(item.date).format(dateFormat));
};

export const initialBonusLogEnricher = (initialBonusesLogs: object) => (chartConfig: ChartConfig) => {
  const initialBonusLogDataSet = contoRelux();
  const labels = [];
  let keys;
  if (initialBonusesLogs && (keys = Object.keys(initialBonusesLogs)).length) {
    let initialChatData = moment(keys[0], dateFormat);
    let lastAvailableDate;
    months.forEach(() => {
      const date = initialChatData.format(dateFormat);
      if (initialChatData.valueOf() <= initialBonusesLogs[keys[keys.length - 1]].date) {
        initialBonusLogDataSet.data.push(initialBonusesLogs[date] ? lastAvailableDate = initialBonusesLogs[date].total :
          lastAvailableDate);
      }
      labels.push(date);
      initialChatData = initialChatData.add(1, 'month');
    });
    chartConfig.data.labels = labels;
    chartConfig.data.datasets.push(initialBonusLogDataSet);
    const maxVal = Math.max.apply(Math, initialBonusLogDataSet.data.map(Math.abs));
    chartConfig.options.scales.yAxes[0].ticks.stepSize = Math.pow(10, (Math.round(maxVal).toString()).length - 1);
    const colours = initialBonusLogDataSet.data.map((value) => value < 0 ? '#e54c36' : 'green');
    initialBonusLogDataSet.pointBackgroundColor = colours;
    initialBonusLogDataSet.pointHoverBorderColor = colours;
    initialBonusLogDataSet.pointBorderColor = colours;
    return chartConfig;
  }
};

const mobileConfig = {
  fontSize: 10,
  labelFontSize: 9,
  xPadding: 1
};

const desktopMiddleConfig = {
  fontSize: 18,
  labelFontSize: 12,
  xPadding: 1
};

const desktopConfig = {
  fontSize: 18,
  labelFontSize: 16,
  xPadding: 6
};
const config = isMobile ? mobileConfig : isMiddleDesktop ? desktopMiddleConfig : desktopConfig;

export const bonusInitialeAnnotation = () => ({
  id: 'initialBonusTuto',
  type: 'line',
  mode: 'horizontal',
  scaleID: 'y-axis-0',
  value: null,
  borderColor: 'transparent',
  borderWidth: 0,
});

export const contoRelux = () => ({
  label: '',
  yAxisID: 'y-axis-0',
  data: [],
  borderColor: '#36749C',
  pointBackgroundColor: '#9a9b9a',
  tension: 0,
  borderWidth: 1,
  fill: false,
  offsetGridLines: true,
  spanGaps: true,
} as DataSet);

export const allInOneChartConfiguration = () => ({
  type: 'line',
  data: {
    labels: [],
    datasets: []
  },
  options: {
    layout: {
      padding: {
        bottom: 10
      }
    },
    legend: {display: false},
    scales: {
      xAxes: [{
        position: 'top',
        stacked: true,
        ticks: {
          autoSkip: false,
          fontColor: '#36749C',
          fontStyle: 600
        },
        gridLines: {
          display: true,
          zeroLineColor: '#e6e6e6',
          zeroLineWidth: 1,
        }
      }],
      yAxes: [{
        stacked: true,
        ticks: {
          display: true,
          autoSkip: false,
          stepSize: 10,
          callback: (label) => label !== 0 ? '' : '0€',
          fontColor: '#36749C',
          fontStyle: 600
        },
        gridLines: {
          display: true,
          zeroLineColor: '#36749C',
          zeroLineWidth: 1
        }
      }],
    },
    annotation: {
      drawTime: 'afterDatasetsDraw',
      annotations: [],
    },
    tooltips: {
      xPadding: config.xPadding,
      callbacks: {
        title: function (tooltipItem, data) {
          const value = data['datasets'][0]['data'][tooltipItem[0]['index']];
          this._chartInstance.controller.tooltip._model.titleFontColor = value > 0 ? 'green' : '#e54c36';
          return `€ ${value}`;
        },
        label: () => {
          return '';
        }
      },
      backgroundColor: 'transparent',
      titleFontSize: config.labelFontSize,
      titleFontColor: '#36749C',
      bodyFontColor: '#36749C',
      bodyFontSize: 12,
      displayColors: false
    }
  }
} as ChartConfig);

