<div class="col-md-11 titleBlock">
  <div class="icon">
  </div>
  <div class="title">
    LE TUE SIM
  </div>
</div>
<div class="app-m-mobile-layout mobile-layout clearfix">
  <div class="col-md-11 col-lg-11 col-xs-12 col-sm-12 no-padding">
    <!--<div>-->
      <div class="col-xl-6 col-lg-6 col-sm-12 col-xs-12 service-details">
        <div
          [ngClass]="{'col-xs-12 col-sm-11 col-lg-12 sim-details main':true, 'selectedRecord':selectedRecord }"><!--'active':selectedRecord,'noActive':!selectedRecord,-->
          <div *ngFor="let record of contractRecords; let i = index"
               [ngClass]="{'record-row':true,
               active:selectedRecord && selectedRecord.id===record.id,
               noActive:selectedRecord && record.id!==selectedRecord.id}"
               class="no-padding-inner-mobile">
            <div class="plainBlock" >
              <p >
                <b>SIM:</b>
                <span>{{record.msisdnId}}</span>
              </p>

                <p  class="no-padding">
                  <b>Offerta: </b>
                  <span >{{activeProductsMap[record.msisdnId]?.name}}</span>
                </p>

              <p  class="no-padding">
                <b>Stato: </b>
                <span>{{mobileStatusDecode(record)| titlecase}}</span>
              </p>
<!--              <p  class="no-padding">-->
<!--                <b class="sim-data-title">Data attivazione: </b>-->
<!--                <span class="sim-data-value"> {{record.createdOn | date:'dd/MM/yyyy'}} </span>-->
<!--              </p>-->
              <p class="no-padding">
                <b >Data attivazione: </b>
                <span >
                  <span> {{record.createdOn | date:'dd/MM/yyyy'}} </span>
                </span>
              </p>
              <p class="no-padding" *ngIf="disabledCostoRinnovo[record.msisdnId] ">
                <b >Costo rinnovo offerta: </b>
                <span >
                  {{activeProductsMap[record.msisdnId]?.renewalPrice?.toFixed(2)}} €/mese
                </span>
              </p>

              <p class="no-padding" *ngIf="disabledCostoRinnovo[record.msisdnId] && validityPeriods[record.msisdnId]">
                <b >Promozioni attive: </b>
                <span >
                  <span *ngIf="validityPeriods[record.msisdnId]"> {{validityPeriods[record.msisdnId]}} </span>
                </span>
              </p>

              <p class="no-padding" *ngIf="autoricaricaEnabled[record.msisdnId]">
                <b>Autoricarica: </b>
                <span>
                  <span>Attiva</span>
                </span>
              </p>

              <div *ngIf="hasDetails(record)"
                   [ngClass]="{button:true, active:selectedRecord && selectedRecord.id===record.id}"
                   (click)="showDetails(record, i)">
                {{selectedRecord && selectedRecord.id===record.id?'Nascondi':'Dettaglio'}}
              </div>
            </div>
            <ng-container *ngIf="checkid == i">
              <div class="visible-xs-block">
                <ng-container *ngTemplateOutlet="mobileDetails"></ng-container>
              </div>
              <div class="visible-xs-block" *ngIf="displayMobileTable">
                <ng-container *ngTemplateOutlet="mobileTable"></ng-container>
              </div>
              <div class="visible-xs-block" style="padding-left: 15px;">
                <ng-container *ngTemplateOutlet="mobileIcons"></ng-container>
              </div>
            </ng-container>
          </div>
        </div>
      </div>
      <div class="col-xl-6 col-lg-6 col-sm-12 col-xs-12 hidden-xs">
        <div class="row">
          <ng-container *ngTemplateOutlet="mobileDetails"></ng-container>
        </div>
      </div>
    <!--</div>-->
    <div class="row">
      <div class="col-xl-12 col-md-12 hidden-xs" *ngIf="displayMobileTable">
        <ng-container *ngTemplateOutlet="mobileTable"></ng-container>
      </div>
    </div>
  </div>
  <div class="col-xl-1 col-md-1 col-lg-1">
    <div class="hidden-xs">
      <ng-container *ngTemplateOutlet="mobileIcons"></ng-container>
    </div>
  </div>
</div>


<ng-template #mobileDetails>
  <div *ngIf="selectedRecord"
       class="col-sm-12 col-xs-12 block-flex block detail sim-details">
    <div class="row">
      <div class="traffic-details-title col-sm-12 col-xs-12 col-lg-12">Dettaglio traffico</div>
      <img *ngIf="selectedRecord?.fiveGEnabled" class="icon-5g" src="assets/img/icons/5G.png" alt="5G">
    </div>
    <div class="row">
      <div *ngIf="expiresOn"
           class="available-traffic-details-title col-md-12">Traffico disponibile fino al {{expiresOn | date :'dd/MM'}}
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12 col-xs-12 col-lg-12" [formGroup]="formGroup">
        <select class="form-control" formControlName="group" (change)="showGroupInfo()">
          <option *ngFor="let groupName of groupNamesInfo" [value]="groupName">{{groupName}}</option>
        </select>
      </div>
    </div>
    <div class="row">
      <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 tariff-details-control">
        <!--<div class="col-lg-3 col-md-3 col-xs-4 col-sm-3 service-buttons-group">
          <button *ngFor="let serviceKey of availableServices | keys"
                  [disabled]="(productOptionMap | keys).indexOf(serviceKey)<0"
                  [ngClass]="{'service-button':true, active: selectedService===serviceKey}"
                  (click)="selectService(serviceKey)">{{availableServices[serviceKey].name}}
          </button>
        </div>-->
        <div class="col-lg-8 col-xs-8 col-sm-8 col-md-8 chart-block no-padding" *ngFor="let key of doughnutChartsAllData">
          <div class="chart-label">
            <span class="col-lg-1 col-md-1 col-sm-1 col-xs-1 col-1 no-padding">
              <span *ngIf="key.serviceBalance.name==='VOICE_ON_NET'"
                    (click)="selectService(formGroup.get('group').value,'VOICE_NATIONAL', 'VOICE_NATIONAL')"
                    class="fa fa-caret-left"></span>
            </span>

            <div class="col-lg-9 col-md-9 col-sm-9 col-xs-9 col-9 no-padding chart-title">
              <span class="unlimited-title" *ngIf="key.serviceBalance.unlimited">Illimitati</span>
              <span *ngIf="!key.serviceBalance.unlimited">
              <span class="live-amount">{{key.serviceBalance.liveAmount}}</span>
                di {{key.serviceBalance.amount}} {{key.unit}} {{key.suffix}}
              </span>
            </div>

            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 col-1 no-padding">
              <span *ngIf="!key.serviceBalance.unlimited && key.serviceBalance.name==='VOICE_NATIONAL'
                && formGroup.get('group').value !== 'Roaming e UK'"
                (click)="selectService(formGroup.get('group').value,'VOICE_NATIONAL', 'VOICE_ON_NET')"
                class="fa fa-caret-right"></span>
            </div>

            <div *ngIf="key.unit === 'Min'">
              <i class="fa fa-phone" aria-hidden="true"></i>
            </div>
            <div *ngIf="key.unit === 'SMS'">
              <i class="fa fa-envelope" aria-hidden="true"></i>
            </div>
            <div *ngIf="key.unit === 'GB'">
              <img src="/assets/img/optimaIcons/arrows-reverse.svg" class="fa-arrow-reverse"/>
            </div>
          </div>
            <canvas #myChart="base-chart"
                  baseChart
                  [datasets]="key.chartData"
                  [colors]="colors"
                  [options]="options"
                  chartType="doughnut"></canvas>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12 col-xs-12 col-sm-12" *ngIf="isAvailable5GOffer()">
        <img src="assets/img/buttons/add_5G.png" alt="Add 5G" class="add-5g-button" (click)="navigateTo5G()">
      </div>
    </div>
    <div class="row">
      <div class="col-md-12 col-xs-12 col-sm-12">
        <div class="col-md-12 col-xs-12 col-sm-12 remaining-credit-block">
          <div class="remaining-credit-title">Credito residuo:</div>
          <div class="remaining-credit">{{balance}} €</div>
        </div>
        <a class="col-md-12 col-xs-12 col-sm-12 link piano-tariffario {{active}}" (click)="switchDisplayMobileTable()">
          <span class="piano-tariffario-text">Dettagli SIM</span>
        </a>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #mobileTable>
  <div *ngIf="selectedRecord" class="summary-sim-block app--mobile-layout-v app-padding-right-0">
    <div class="col-md-12 col-sm-12 col-xs-12 mobile-summary-table">
      <div class="col-md-12 col-sm-12 col-xs-12 summary-table">
        <div class="row border-info">
          <div class="col-lg-4 col-md-12 col-sm-12 col-xs-12" style="overflow-wrap: break-word;">
            <span class="info-title">SIM</span>
            <span class="info-value">ICCD {{selectedRecord.simId}}</span>
          </div>
          <div class="col-lg-4 col-md-12 col-sm-12 col-xs-12">
            <span class="info-title">NUMERO</span>
            <span class="info-value">{{selectedRecord.msisdnId}}</span>
          </div>
          <div class="col-lg-4 col-md-12 col-sm-12 col-xs-12">
            <span class="info-title">COD.PUK</span>
            <span class="info-value">{{selectedRecord.sim.puk1}}</span>
          </div>
        </div>
        <div class="row display-flex">
<!--          <div class="col-lg-4 col-sm-12 col-xs-12 table-column">
            <div class="row table-title" *ngIf="showTariffDescription">
              Piano Tariffario
            </div>
            <div class="row tariff-plan" *ngIf="showTariffDescription"
                 [innerHTML]="productDescription.description | safeHtml"></div>
          </div>-->
          <div class="col-lg-4 col-sm-12 col-xs-12 table-column"
               *ngIf="totalSecurityInfo || safeCallInfo || selectedRecord.fiveGEnabled">
            <div class="row table-title flex-title">
              SERVIZI E OPZIONI RICORRENTI
              <app-info name="mobile" style="margin-left: 10px">
                <info-button><i class="info-circle">i</i></info-button>
                <info-message>Il rinnovo di questi servizi/opzioni coincide con il rinnovo automatico del tuo pacchetto
                  mensile.
                </info-message>
              </app-info>
            </div>
            <div *ngIf="selectedRecord.fiveGEnabled">
              <div class="row font-weight-bold">
                5G
              </div>
              <div class="row">
                <b>Stato:</b> Attivo
              </div>
              <div class="row">
                <b>Data attivazione:</b> {{ active5GAddOn?.dataInizioValidita | date: 'dd/MM/yyyy' }}
              </div>
              <div class="modify-container">
                <div>
                  <div class="row">
                    <b>Costo mensile:</b> {{ active5GAddOn?.canoneMese }} €
                  </div>
                  <div class="row">
                    <b>Data rinnovo:</b> {{ renewalDate5G | date: 'dd/MM/yyyy' }}
                  </div>
                </div>
                <button class="modify-button" (click)="navigateTo5G()">MODIFICA</button>
              </div>
            </div>

            <div *ngIf="totalSecurityInfo">
              <div class="row font-weight-bold">
                Total Security
              </div>
              <div class="row">
                <b>Stato:</b> {{totalSecurityInfo.status}}
              </div>
              <div class="row" *ngIf="totalSecurityInfo.status === 'Attivo'">
                <b>Data attivazione:</b> {{totalSecurityInfo.additionalInfo.dateActivation | date: 'dd/MM/yyyy'}}
              </div>
              <div class="row" *ngIf="totalSecurityInfo.status === 'Attivo'">
                <b>Costo mensile:</b> {{totalSecurityInfo.additionalInfo.price}} €
              </div>
              <div class="row" *ngIf="totalSecurityInfo.status === 'Attivo'">
                <b>Data rinnovo:</b> {{totalSecurityInfo.additionalInfo.dateRenewal | date: 'dd/MM/yyyy'}}
              </div>
              <div class="row" *ngIf="totalSecurityInfo.status === 'Disattivo'">
                <b>Data disattivazione:</b> {{totalSecurityInfo.additionalInfo.dateDeactivation | date: 'dd/MM/yyyy'}}
              </div>
            </div>

            <div *ngIf="safeCallInfo">
              <div class="row font-weight-bold" style="padding-top: 20px">
                Safe Call
              </div>
              <div class="row">
                <b>Stato:</b> {{safeCallInfo.status}}
              </div>
              <div class="row" *ngIf="safeCallInfo.status === 'Attivo'">
                <b>Data attivazione:</b> {{safeCallInfo.additionalInfo.dateActivation | date: 'dd/MM/yyyy'}}
              </div>
              <div class="row" *ngIf="safeCallInfo.status === 'Attivo'">
                <b>Costo mensile:</b> {{safeCallInfo.additionalInfo.price}} €
              </div>
              <div class="row" *ngIf="safeCallInfo.status === 'Attivo'">
                <b>Data rinnovo:</b> {{safeCallInfo.additionalInfo.dateRenewal | date: 'dd/MM/yyyy'}}
              </div>
              <div class="row" *ngIf="safeCallInfo.status === 'Disattivo'">
                <b>Data disattivazione:</b> {{safeCallInfo.additionalInfo.dateDeactivation | date: 'dd/MM/yyyy'}}
              </div>
            </div>

<!--            <div class="row table-title" *ngIf="showTariffDescription">
              Piano Tariffario
            </div>
            <div class="row tariff-plan" *ngIf="showTariffDescription"
                 [innerHTML]="productDescription.description | safeHtml"></div>-->
          </div>

          <div class="col-lg-4 col-sm-12 col-xs-12 table-column" *ngIf="yourOptions.length>0">
            <div class="row table-title">
              Le tue opzioni
            </div>
            <div class="row tariff-plan"
                 *ngFor="let option of yourOptions">
              <div>{{option.name}}</div>
              <span *ngIf=" option.productMapping">

                 <span>{{option.productMapping.descrizioneProdotto }}</span>

              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #mobileIcons>
  <div class="row">
    <div *ngIf="selectedRecord" class="col-lg-12 col-sm-12 col-xs-12 navigation-block">
      <a class="icon ricarica"
         [routerLink]="['/faidate/servizi-attivi/mobile/ricarica/',selectedRecord.msisdnId]"></a>
      <a class="icon opzioni"
         [routerLink]="['/faidate/servizi-attivi/mobile/agguingi/',selectedRecord.msisdnId]"></a>
    </div>
  </div>
</ng-template>
