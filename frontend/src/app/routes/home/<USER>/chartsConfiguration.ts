import * as moment from 'moment';

import { GasPointAdjustment } from '../../../common/model/gas/GasPointAdjustment';
import { NormalizeUtils } from '../../../common/utils/NormalizeUtils';
import ChartConfig from '../../../common/utils/chart-builder/ChartConfig';
import {EnergyDetails, EnergyPointAdjustment} from '../../../common/model/energy/EnergyPointAdjustment';
import {EnergiaService} from '../../../common/services/energia/energia-service.service';

moment.locale('it');
const dateFormat = 'MMM YYYY';
const gasListNormalizationExpression = item => moment(item.inizioPeriodo).format(dateFormat);
const energyListNormalizationExpression = item => moment(item.dataLettura).format(dateFormat);
const months = moment.monthsShort();
let currentMonthAndYear;
let currentFullDate;
const ConsumiDelInCorsoMonth = moment().format(dateFormat);

export const chartConfiguration = () => ({
  type: 'bar',
  data: {
    labels: null,
    datasets: []
  },
  options: {
    onClick: onClickEvent,
    legend: {display: false},
    scales: {
      xAxes: [{
        position: 'top',
        stacked: true,
        ticks: {
          stepSize: 1,
          min: 0,
          autoSkip: false,
          fontColor: '#3f7ba2',
          fontStyle: 600,
          fontFamily: 'Italic',
          padding: 30
        },
        gridLines: {
          color: '#dedfe7',
          tickMarkLength: 0
        }
      }],
      yAxes: [{
        stacked: true,
        ticks: {
          min: 0,
          fontColor: '#62aae8',
          padding: 5
        },
        gridLines: {
          color: '#dedfe7',
          tickMarkLength: 15
        }
      }],
    },
    annotation: {
      events: ['mouseenter', 'mouseleave'],
      annotations: []
    },
    tooltips: {
      callbacks: {
        label: function (tooltipItem) {
          const scaleLabelConfig = this._chart.config.options.scales.yAxes[0].scaleLabel;
          if (scaleLabel) {
            return `${tooltipItem.yLabel} ${scaleLabelConfig.labelString}`;
          }
          return tooltipItem.yLabel;
        },
      }
    }
  }
} as ChartConfig);

function onClickEvent(event, array) {
  if ((array[0] !== undefined) && Array.isArray(array[0]._model.label) && (array[0]._model.label[1] + ' ' + array[0]._model.label[0]) === ConsumiDelInCorsoMonth) {
    currentMonthAndYear = '/' + (moment(array[0]._model.label[1], 'MMM').month() + 1) + '/' + moment(array[0]._model.label[0], 'MMMYYYY').year();
    EnergiaService.sendClickEventFromMonthsChart(array[0]._model.label[1] + array[0]._model.label[0]);
  }
}

function onClickEventDay(event, array) {
  if ((array[0] !== undefined)) {
    currentFullDate = array[0]._model.label + currentMonthAndYear;
    EnergiaService.sendClickEventFromDaysChart(array[0]._model.label);
  }
}

export const chartConfigurationByHours = () => ({
  type: 'bar',
  data: {
    labels: null,
    datasets: []
  },
  options: {
    title: {
      display: true,
      position: 'top',
      text: currentFullDate,
      fontColor: '#3f7ba2',
      fontStyle: 550,
      fontSize: 15
    },
    legend: {display: false},
    scales: {
      xAxes: [{
        stacked: true,
        ticks: {
          stepSize: 1,
          min: 0,
          autoSkip: false,
          fontColor: '#3f7ba2',
          fontStyle: 550,
          fontSize: 11,
          padding: 5
        },
        gridLines: {
          color: '#dedfe7'
        }
      }],
      yAxes: [{
        stacked: true,
        ticks: {
          min: 0,
          fontColor: '#62aae8',
          padding: 5
        },
        gridLines: {
          color: '#dedfe7',
          tickMarkLength: 15
        }
      }],
    },
    annotation: {
      events: ['mouseenter', 'mouseleave'],
      annotations: []
    },
    tooltips: {
      callbacks: {
        label: function (tooltipItem) {
          const scaleLabelConfig = this._chart.config.options.scales.yAxes[0].scaleLabel;
          if (scaleLabel) {
            return `${tooltipItem.yLabel} ${scaleLabelConfig.labelString}`;
          }
          return tooltipItem.yLabel;
        },
      }
    }
  }
} as ChartConfig);

export const chartConfigurationByDays = () => ({
  type: 'bar',
  data: {
    labels: null,
    datasets: []
  },
  options: {
    onClick: onClickEventDay,
    legend: {display: false},
    scales: {
      xAxes: [{
        barThickness: 13,
        stacked: true,
        ticks: {
          stepSize: 1,
          min: 0,
          autoSkip: false,
          fontColor: '#3f7ba2',
          padding: 7,
          fontFamily: 'Helvetica',
          fontStyle: 600,
          fontSize: 11,
          maxRotation: 0
        },
        gridLines: {
          color: '#dedfe7'
        }
      }],
      yAxes: [{
        stacked: true,
        ticks: {
          min: 0,
          fontColor: '#62aae8',
          padding: 5,
          maxTicksLimit: 5,
        },
        gridLines: {
          color: '#dedfe7',
          tickMarkLength: 15
        }
      }],
    },
    annotation: {
      events: ['mouseenter', 'mouseleave'],
      annotations: []
    },
    tooltips: {
      callbacks: {
        label: function (tooltipItem) {
          const scaleLabelConfig = this._chart.config.options.scales.yAxes[0].scaleLabel;
          if (scaleLabel && tooltipItem.yLabel !== 0) {
            return `${tooltipItem.yLabel} ${scaleLabelConfig.labelString}`;
          } else if (tooltipItem.yLabel === 0) {
            return 'Per questo giorno non abbiamo ricevuto il dato reale di consumo dal distributore locale';
          }
          return tooltipItem.yLabel;
        },
        title: function (tooltipItem) {
          return `${tooltipItem[0].xLabel}${currentMonthAndYear}`;
        },
      }
    }
  }
} as ChartConfig);

export const consumptionAnnotation = () => ({
  drawTime: 'afterDatasetsDraw',
  id: 'horizontal',
  type: 'line',
  mode: 'horizontal',
  scaleID: 'y-axis-0',
  value: 0,
  borderColor: '#b8b8b8',
  borderWidth: 2,
  label: {
    backgroundColor: '#b8b8b8',
    enabled: false,
    content: null
  },
  onMouseenter: function () {
    const element = this;
    const {yAxes} = element.chartInstance.options.scales;
    element.options.label.enabled = true;
    element.options.label.content = yAxes && yAxes.length && yAxes[0].scaleLabel ?
      `${element.options.value} ${yAxes[0].scaleLabel.labelString}` : element.options.value;
    element.chartInstance.update();
  },
  onMouseleave: function () {
    const element = this;
    element.options.label.enabled = false;
    element.chartInstance.update();
  },
});

export const consumo = () => ({
  label: '',
  data: [],
  borderColor: '#b1c8de',
  backgroundColor: '#b1c8de',
  pointBackgroundColor: '#b1c8de',
  tension: 0,
  fill: false,
  offsetGridLines: true
});

export const scaleLabel = () => ({
  display: true,
  labelString: '',
  fontColor: '#58a1e3',
  fontSize: 10
});

export const normalizeGasAdjustments = (adjustments: Array<GasPointAdjustment>) => {
  return NormalizeUtils.groupByExpression(adjustments, gasListNormalizationExpression);
};

export const normalizeEnergyAdjustments = (adjustments: Array<EnergyPointAdjustment>) => {
  const sorted = adjustments.sort((item1, item2) => item1.dataLettura - item2.dataLettura);
  return NormalizeUtils.groupByExpression(sorted, energyListNormalizationExpression);
};

export const normalizeEnergyDetailsByDays = (detailsByDays: Array<EnergyDetails>) => {
  const result = {};
  for (let i = 0; i < detailsByDays.length; i++) {
    let value = 0;
    value += detailsByDays[i].ora1;
    value += detailsByDays[i].ora2;
    value += detailsByDays[i].ora3;
    value += detailsByDays[i].ora4;
    value += detailsByDays[i].ora5;
    value += detailsByDays[i].ora6;
    value += detailsByDays[i].ora7;
    value += detailsByDays[i].ora8;
    value += detailsByDays[i].ora9;
    value += detailsByDays[i].ora10;
    value += detailsByDays[i].ora11;
    value += detailsByDays[i].ora12;
    value += detailsByDays[i].ora13;
    value += detailsByDays[i].ora14;
    value += detailsByDays[i].ora15;
    value += detailsByDays[i].ora16;
    value += detailsByDays[i].ora17;
    value += detailsByDays[i].ora18;
    value += detailsByDays[i].ora19;
    value += detailsByDays[i].ora20;
    value += detailsByDays[i].ora21;
    value += detailsByDays[i].ora22;
    value += detailsByDays[i].ora23;
    value += detailsByDays[i].ora24;
    result[i + 1] = value.toFixed(3);
  }
  return result;
};

export const normalizeEnergyDetailsByDaysForCurrentMonth = (detailsByDays: Array<EnergyDetails>) => {
  let result = 0;
  for (const item of detailsByDays) {
    let value = 0;
    value += item.ora1;
    value += item.ora2;
    value += item.ora3;
    value += item.ora4;
    value += item.ora5;
    value += item.ora6;
    value += item.ora7;
    value += item.ora8;
    value += item.ora9;
    value += item.ora10;
    value += item.ora11;
    value += item.ora12;
    value += item.ora13;
    value += item.ora14;
    value += item.ora15;
    value += item.ora16;
    value += item.ora17;
    value += item.ora18;
    value += item.ora19;
    value += item.ora20;
    value += item.ora21;
    value += item.ora22;
    value += item.ora23;
    value += item.ora24;
    result += value;
  }
  return result;
};

export const normalizeKeysEnergyDetailsByDays = (detailsByDays: Array<EnergyDetails>) => {
  const keys = Object.keys(detailsByDays);
  for (let i = 0; i < keys.length; i++) {
    keys[i] = String(i + 1);
  }
  return keys;
};

export const normalizeDetailsByHours = (details: Array<EnergyDetails>) => {
    return {
      '0': details[0].ora1,
      '1': details[0].ora2,
      '2': details[0].ora3,
      '3': details[0].ora4,
      '4': details[0].ora5,
      '5': details[0].ora6,
      '6': details[0].ora7,
      '7': details[0].ora8,
      '8': details[0].ora9,
      '9': details[0].ora10,
      '10': details[0].ora11,
      '11': details[0].ora12,
      '12': details[0].ora13,
      '13': details[0].ora14,
      '14': details[0].ora15,
      '15': details[0].ora16,
      '16': details[0].ora17,
      '17': details[0].ora18,
      '18': details[0].ora19,
      '19': details[0].ora20,
      '20': details[0].ora21,
      '21': details[0].ora22,
      '22': details[0].ora23,
      '23': details[0].ora24
    };
};

export const gasLabelsAndDatasetEnricher = (normalizedAdjustments: object) => (chartConfig) => {
  const consumoDataset = consumo();
  const labels = [];
  const keys = Object.keys(normalizedAdjustments);
  const currentYear = keys.length > 0 ? moment(keys[0], 'MMM YYYY').year() : new Date().getFullYear();
  months.forEach(key => {
    let consumoSum = 0;
    const date = `${key} ${currentYear}`;
    if (normalizedAdjustments[date]) {
      normalizedAdjustments[date].forEach(item => consumoSum += item.consumoAdeguato);
    }
    consumoDataset.data.push(consumoSum ? consumoSum.toFixed(2) : consumoSum);
    const newLabels = [`${currentYear}`, `${key}`];
    labels.push(newLabels);
  });

  chartConfig.data.labels = labels;
  chartConfig.data.datasets.push(consumoDataset);
  const axisLabel = scaleLabel();
  axisLabel.labelString = 'Smc';
  chartConfig.options.scales.yAxes[0].scaleLabel = axisLabel;
  return chartConfig;
};

export const energyLabelsAndDatasetEnricher = (normalizedAdjustments: object) => (chartConfig) => {
  const consumoDataset = consumo();
  const labels = [];
  const keys = Object.keys(normalizedAdjustments);
  const currentYear = keys.length > 0 ? moment(keys[0], 'MMM YYYY').year() : new Date().getFullYear();
  months.forEach(key => {
    const date = `${key} ${currentYear}`;
    let consumoSum = 0;
    if (normalizedAdjustments[date]) {
      normalizedAdjustments[date].forEach(item => consumoSum += item.kwh);
    }
    consumoDataset.data.push(consumoSum);
    const newLabels = [`${currentYear}`, `${key}`];
    labels.push(newLabels);
  });
  chartConfig.data.labels = labels;
  chartConfig.data.datasets.push(consumoDataset);
  const axisLabel = scaleLabel();
  axisLabel.labelString = 'kWh';
  chartConfig.options.scales.yAxes[0].scaleLabel = axisLabel;
  chartConfig.data.datasets[0].backgroundColor = chartConfig.data.labels.map((item) => {
    if (ConsumiDelInCorsoMonth === (item[1] + ' ' + item[0])) {
      return '#00a1ee';
    } else {
      return '#b1c8de';
    }
  });
  return chartConfig;
};

export const energyLablesDatasetEnricherByHours = (normalizedDetailsByHours: object) => (chartConfig) => {
  const consumoDataset = consumo();
  const labels = [];
  const keys = Object.keys(normalizedDetailsByHours);
  keys.forEach(key => {
    if (normalizedDetailsByHours[key]) {
      consumoDataset.data.push(normalizedDetailsByHours[key]);
    }
    labels.push(key);
  });
  chartConfig.data.labels = labels;
  chartConfig.data.datasets.push(consumoDataset);
  const axisLabel = scaleLabel();
  axisLabel.labelString = 'kWh';
  chartConfig.options.scales.yAxes[0].scaleLabel = axisLabel;
  return chartConfig;
};

export const energyLablesDatasetEnricherByDays = (normalizedDetailsByDays: object) => (chartConfig) => {
  const consumoDataset = consumo();
  const labels = [];
  for (let i = 0; i < moment(currentMonthAndYear, '/MM/YYYY').daysInMonth() ; i++) {
    labels.push(i + 1);
  }
  labels.forEach(key => {
    if (normalizedDetailsByDays[key]) {
      consumoDataset.data.push(normalizedDetailsByDays[key]);
    } else {
      consumoDataset.data.push(0);
    }
  });
  chartConfig.data.labels = labels;
  chartConfig.data.datasets.push(consumoDataset);
  const axisLabel = scaleLabel();
  axisLabel.labelString = 'kWh';
  chartConfig.options.scales.yAxes[0].scaleLabel = axisLabel;
  return chartConfig;
};

export const changeTitleInChartByHours = (day: number) => {
  currentFullDate = day + currentMonthAndYear;
};
