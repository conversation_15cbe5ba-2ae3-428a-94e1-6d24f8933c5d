@import "../../../../shared/styles/colors";


.amazon-prime-header {
  width: 100%;
  height: 10%;
  border: 1px solid $menu-border;
  border-radius: 5px;
  padding: 18px;
  margin-bottom: 10px;
  .header-label {
    color: $dark-blue;
    font-size: 18px;
    margin: 0;
  }
}

.amazon-prime-container {
  border: 1px solid $menu-border;
  border-radius: 5px;
  padding: 40px;
  .row {
    .icon-amazon {
      background: url("../../../../../assets/img/amazon/logo_optima_copia_2.png") no-repeat left;
      background-size: contain;
      float: left;
      width: 50%;
      height: 48px;
      margin-left: 10px;
    }
  }
}

.main-label {
  font-size: 30px;
  color: $dark-blue;
  margin-top: 15px;
}

.center-content {
  text-align: center;
}

.blue {
  color: #29A8E2;
}

.sub-label {
  font-weight: unset;
  font-size: 20px;
  color: $dark-blue;
}

.offer-text {
  font-size: 17px;
  color: $dark-blue;
  margin-top: 15px;
}

.offer-list {
  padding: 16px;
  font-size: 17px;
  color: $dark-blue;
}

.app-button {
  background: white;
  color: $dark-blue;
  width: 215px;
  font-size: 20px;
}

.terms {
  clear: both;
  margin-top: 30px;
  color: $dark-blue;
  padding-top: 20px;
}

.terms-label {
  display: block;
  margin: auto;
}

.unlinked {
  display: unset;
}

.terms-label-help {
  margin-top: 15px;
  font-weight: bold;
}

.amazon-banner {
  background: url("/../../../../../assets/img/amazon/amazon.png") no-repeat right;
  background-size: contain;
  width: 40%;
  height: 275px;
  position: absolute;
  top: 323px;
  margin: auto auto 10px 605px;
}

@media screen and(max-width: 1660px) {
  .amazon-banner {
    top: 323px;
    margin: auto auto 10px 565px;
  }
}

@media screen and(max-width: 1575px) {
  .amazon-banner {
    top: 340px;
    margin: auto auto 10px 550px;
    width: 35%;
  }
}

@media screen and(max-width: 1420px) {
  .amazon-banner {
    top: 350px;
    margin: auto auto 10px 500px;
    width: 35%;
  }
}

@media screen and(max-width: 1312px) {
  .amazon-banner {
    top: 400px;
    margin: auto auto 10px 470px;
    width: 35%;
  }
}

@media screen and(max-width: 1282px) {
  .amazon-banner {
    top: 440px;
    margin: auto auto 10px 440px;
    width: 35%;
  }
}

@media screen and(max-width: 1210px) {
  .amazon-banner {
    top: 480px;
    margin: auto auto 10px 400px;
    width: 35%;
  }
}

@media screen and(max-width: 1120px) {
  .amazon-banner {
    top: 480px;
    margin: auto auto 10px 360px;
  }
}

@media screen and(max-width: 991px) {
  .amazon-banner {
    background: url("/../../../../../assets/img/amazon/amazon.png") no-repeat center;
    background-size: contain;
    position: unset;
    margin: auto;
    width: 100%;
  }
  .app-button {
    float: none;
  }
}

@media screen and(max-width: 565px) {
  .amazon-prime-container{
    .row {
      .icon-amazon {
        background: url("../../../../../assets/img/amazon/logo_optima_copia_2.png") no-repeat left;
        background-size: contain;
        width: 100%;
        height: 100px;
        margin-left: 0;
      }
    }
  }
}
