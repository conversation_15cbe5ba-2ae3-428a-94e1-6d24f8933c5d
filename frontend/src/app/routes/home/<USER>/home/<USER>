@import '../../../../shared/styles/app/variables';
@import '../../../../shared/styles/app/media-queries';

body {
  overflow-y: hidden;
}

.home-container {
  margin: auto;
  max-width: $container-md;

  .home-logo {
    width: 240px;
  }

  .home-text {
    @media only screen and (max-width: $mq-tablet) {
      text-align: center;
    }
  }
}

.forDesktop {
  display: block;
}

.forMobile {
  display: none;
  margin-bottom: 15px;
  margin-top: 25px;
}

.mobile {
  display: none;
  float: left;
}

.item {
  height: 200px;
}

#myCarousel {
  height: 200px;
}

.carousel-indicators {
  top: 50% !important;
}

.carousel-indicators li {
  background-color: #b6cce3 !important;
}

.carousel-indicators .active {
  margin: 0;
  width: 15px;
  height: 15px;
  background-color: #e54c36 !important;
}

.main {
  padding-top: 30px;
}

.block {
  border: 1px solid #b6cce3;
  border-radius: 5px;
  padding: 10px 20px;
  background-color: white;
  h2 {
    color: #36749C;
    font-weight: bold;
    padding: 15px 0;
  }
  .mainText {
    height: 15px;
    color: black;
    font-size: 18px;
  }
  a {
    color: #36749d;
    font-size: 13px;
  }
}

.text {
  padding: 10px 20px;
}

.menu {
  border: 1px solid #b6cce3;
  border-radius: 5px;
  padding: 5px 10px;
  margin: 0 0 15px 0;
  .icon {
    font-size: 60px;
    line-height: 100px;
    float: left;
    margin-left: 0;
    color: #e54c36;
  }
  .text {
    float: left;
    color: #36749d;
    .mainText {
      line-height: 12px;
      font-size: 19px;
      font-weight: bold;
    }
    .subText {
      font-size: 14px;
    }
  }
}

.menuBlocks {
  background-color: white;
  a:hover {
    text-decoration: none;
  }
}

.menu:hover {
  background-color: #f0f5f9;
}

.icon {
  float: left;
  height: 70px;
  padding: 0 20px;
  text-align: center;
  margin-left: 20px;
}

.telegram {
  background: url("../../../../../assets/img/optimaIcons/telegram.png") no-repeat center;
  background-size: contain;
}

.facebook {
  background: url("../../../../../assets/img/optimaIcons/facebook.png") no-repeat center;
  background-size: contain;
}

.chatlive {
  background: url("../../../../../assets/img/optimaIcons/chatlife.png") no-repeat center;
  background-size: contain;
}

.notificationBlock {
  padding: 15px 0;
  line-height: 8px;
  .them {
    color: black;
  }
  .mes {
    color: #36749d;
  }
}

.btn-condomini {
  display: none;
  margin-top: 160px;
  width: 47%;
  height: 35px;
  background-color: white;
  color: #5489aa; //old:b6cce3
  border: 2px solid #5489aa; //old:b6cce3
  border-radius: 10px;
  font-size: 18px;
  padding-right: 5px;
  position: relative;
  @media screen and (max-width: 1200px) {
    font-size: 16px;
  }
  @media screen and (max-width: 1100px) {
    font-size: 15px;
  }
}

.btn-condomini-img {
  position: absolute;
  left: 2%;
  bottom: 24%;
  transform: rotate(90deg);
}

.video {
  margin: 10px 0;
}

.panel{
  box-shadow: none;
}
@media only screen and (max-width: 1600px) {

  .block {
    h2 {
      color: #e54c36;
      font-weight: bold;
      padding: 15px 0;
      font-size: 18px;
    }
    .mainText {
      height: 15px;
      color: black;
      font-size: 16px;
    }
    a {
      color: #36749d;
      font-size: 12px;
    }
  }

  .text {
    padding: 10px 20px;
  }

  .menu {
    border: 1px solid #b6cce3;
    border-radius: 5px;
    padding: 5px 10px;
    margin: 0 0 15px 0;
    .icon {
      font-size: 60px;
      line-height: 100px;
    }
    .text {
      .mainText {

        font-size: 15px;
      }
      .subText {
        font-size: 12px;
      }
    }
  }
  .icon {
    height: 60px;
    padding: 0 20px;
    margin-left: 20px;
  }
  .iframeText {
    font-size: 15px;
  }
}

@media only screen and (max-width: 1200px) {
  .block-menu {
    margin-bottom: 1%;
  }
}

@media only screen and (max-width: 980px) {
  .block-menu {
    margin-top: 0;
  }
}
@media only screen and (max-width: 991px){
  .container-fluid {
    padding: 0;
  }
  .forDesktop {
    display: none;
  }
  .forMobile {
    display: block;
  }
}
@media only screen and (max-width: 990px) {

  .block {
    h2 {
      color: #e54c36;
      font-weight: bold;
      padding: 15px 0;
      font-size: 16px;
    }
    .mainText {
      height: 4px;
      color: black;
      font-size: 14px;
    }
    a {
      color: #36749d;
      font-size: 10px;
    }
  }

  .text {
    padding: 10px 10px;
  }

  .menu {
    border: 1px solid #b6cce3;
    border-radius: 5px;
    padding: 5px 10px;
    margin: 0 0 10px 0;
    .icon {
      font-size: 45px;
      line-height: 110px;
      margin-left: -5px;
    }
    .text {
      .mainText {
        line-height: 15px;
        font-size: 15px;
      }
      .subText {
        font-size: 12px;
      }
    }
  }
  .icon {
    height: 50px;
    padding: 0 10px;
    margin-left: 10px;
  }
}

@media only screen and (max-width: 800px) {
  .block-menu {
    width: 100%;
  }

}

@media only screen and (max-width: 991px) {

  .mobile {
    display: block;
    width: 93%;
    margin: 15px auto 0 auto;
    padding: 0;
    border: 1px solid #b6cce3;
    border-radius: 5px;
  }
  .leftMenu {
    width: 100%;
    margin-bottom: 15px;
  }
  .menu {
    margin-top: 15px;
    text-align: center;
  }
  .block-menu {
    margin: auto;
    width: 90%;
    background: none;
    border: none;
    box-shadow: none;
  }
  .desktop {
    display: none;
  }
  .block {
    border-radius: 5px;
    padding: 10px 20px;
    h2 {
      color: #e54c36;
      padding: 15px 0;
    }
    .mainText {
      height: 15px;
      font-size: 18px;
    }
    a {
      font-size: 13px;
    }
  }

  .text {
    padding: 10px 20px;
  }

  .menu {
    .icon {
      font-size: 60px;
      line-height: 100px;
    }
    .text {
      .mainText {
        line-height: 12px;
        font-size: 19px;
      }
      .subText {
        font-size: 14px;
      }
    }
  }

  .icon {
    height: 70px;
    padding: 0 20px;
    margin-left: 20px;
  }

}

@media only screen and (max-width: 991px) {
  .block-menu {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
    width: 100%;
  }
}

@media only screen and (max-width: 991px) {
  .btn-condomini {
    display: block;

  }
}

@media only screen and (max-width: 767px) {
  .btn-condomini {
    width: 100%;
  }
}
