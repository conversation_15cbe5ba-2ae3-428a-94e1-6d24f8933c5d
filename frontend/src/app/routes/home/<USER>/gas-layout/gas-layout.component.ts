import {Component, OnDestroy} from '@angular/core';
import {HomeService} from '../home/<USER>';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {Subscription} from 'rxjs/Subscription';
import {UserData} from '../../../../common/model/userData.model';
import {Utility} from '../../../../common/model/services/userServices.model';
import {GasService} from '../../../../common/services/gas/gas.service';
import {PodDetail} from '../../../../common/model/gas/PodDetail';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';
import {GasType} from '../../../../common/enum/GasType';
import {ServicesActions} from '../../../../redux/services/actions';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';
import {
  chartConfiguration,
  gasLabelsAndDatasetEnricher,
  normalizeGasAdjustments
} from '../../charts/chartsConfiguration';
import {ChartBuilder} from '../../../../common/utils/chart-builder/chart-builder';
import {Offer} from '../../../../redux/model/Offer';
import {PaginatorBuilder} from '../../../../common/builders/PaginatorBuilder';
import {Paginator} from '../../../../common/utils/pagination/Paginator';
import * as _ from 'lodash';
import {GasPointAdjustment} from '../../../../common/model/gas/GasPointAdjustment';
import ChartConfig from '../../../../common/utils/chart-builder/ChartConfig';
import {EtichettaLabel} from "../../../../common/model/etichetta-label/etichetta-label.model";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {EtichettaLabelService} from "../../../../common/services/etichetta-label/etichetta-label.service";

@Component({
  selector: 'app-gas-layout',
  templateUrl: './gas-layout.component.html',
  styleUrls: ['./gas-layout.component.scss']
})
export class GasLayoutComponent implements OnDestroy {

  @select(['services'])
  services: Observable<ServiceStateModel>;

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;

  @select(['user', 'activeOffers'])
  private activeOffers;

  chartConfig: ChartConfig;

  checkid: number;

  sortedServices: Array<any> = [];

  userCluster: string;

  pdf: Array<any>;

  detail: Utility;

  podDetails: Array<PodDetail>;

  pdrAdditionalDetails = {};

  offers: Array<Offer>;

  hasAdjustments: number;

  consumptionAnnotation: number;

  serviceSubscription: Subscription;
  userInfoSubscription: Subscription;
  clientOffersSubscription: Subscription;

  chartPaginator: Paginator;

  etichettaLabelByUtNumberMap = new Map<string, EtichettaLabel>();
  etichettaFormGroups: Array<FormGroup> = [];

  constructor(private serviceActions: ServicesActions, private homeService: HomeService,
              private gasService: GasService, private formBuilder: FormBuilder,
              private etichettaLabelService: EtichettaLabelService) {
    this.userInfoSubscription = this.userInfo.subscribe(userInfo => {
      if (userInfo) {
        this.userCluster = userInfo.cluster.value;
      }
    });
    this.clientOffersSubscription = this.activeOffers.subscribe(activeOffers => {
      this.offers = activeOffers;
    });
    this.serviceSubscription = this.services.subscribe(userServices => {
      const {activeServices, servicesLoaded, gasPodDetailsLoading, gasPodDetailsLoaded, gasPodDetails} = userServices;
      this.pdrAdditionalDetails = gasPodDetails;
      this.sortedServices = activeServices[GasType.GAS];
      if (this.sortedServices) {
        this.sortedServices["utilities"].forEach(utility => {
          let formGroup = this.formBuilder.group({
            etichetta: [null, [Validators.minLength(3), Validators.maxLength(16)]]
          });
          this.etichettaFormGroups.push(formGroup);
          this.etichettaLabelService.getEtichettaLabelByPodUtNumber(utility.utNumber).subscribe(etichetta => {
            if (etichetta) {
              formGroup.controls['etichetta'].setValue(etichetta.description);
              this.etichettaLabelByUtNumberMap.set(utility.utNumber, etichetta);
            }
          });
        });
      }
      if (servicesLoaded && !gasPodDetailsLoaded && !gasPodDetailsLoading) {
        this.serviceActions.loadGasAdditionalPodDetails(activeServices[GasType.GAS]);
      }
    });
  }

  selectValue(utility, i) {
    this.checkid = i;
    this.pdf = this.setPDFList('GAS');
    this.detail = utility;
    const clientId = localStorage.getItem('clientId');
    this.gasService.getPodDetails(utility.utNumber, clientId).subscribe(podDetails => {
      this.podDetails = podDetails;
    });
    this.gasService.gasPointAdjustments(clientId, utility.utNumber).subscribe(adjustments => {
      const filteredAdjustments = adjustments.filter(item => item.letturaFatturata === 'si');
      this.hasAdjustments = filteredAdjustments && filteredAdjustments.length;
      this.buildChart(utility.utNumber, filteredAdjustments);
    });
  }

  buildChart(pod: string, adjustments: Array<GasPointAdjustment>) {
    const normalizedAdjustments = normalizeGasAdjustments(adjustments);
    if (normalizedAdjustments) {
      const adjustmentsKeys = Object.keys(normalizedAdjustments);
      this.chartPaginator = PaginatorBuilder.builder().yearlyPagination().forArray(adjustmentsKeys).withStep(12)
        .withOnChange((array = []) => {
          const builder = ChartBuilder.builder().withConfiguration(chartConfiguration())
            .withEnricher(gasLabelsAndDatasetEnricher(_.pick(normalizedAdjustments, array)));
          const found = this.offers.filter(item => item.gas &&
            item.gas.every(gas => gas.utenza === pod));
          if (found.length) {
            this.consumptionAnnotation = found[0].gas[0].mc;
          }
          this.chartConfig = builder.build();
        }).reversive().build();
    }
  }

  hide() {
    this.detail = null;
  }

  setPDFList(serviceName) {
    return this.homeService.getPDFList(serviceName, this.userCluster);
  }

  ngOnDestroy() {
    ObservableUtils.unsubscribeAll
    ([this.serviceSubscription, this.userInfoSubscription, this.clientOffersSubscription]);
  }

  changeEtichetta() {
    return undefined;
  }

  getEtichettaLabelByUtNumber(utNumber: string): EtichettaLabel {
    return this.etichettaLabelByUtNumberMap.get(utNumber);
  }

  checkForEditingFields(event: any, utNumber: string, description: string, etichettaLabel: EtichettaLabel) {
    if (event.action == 'accept') {
      if (!etichettaLabel) {
        etichettaLabel = new EtichettaLabel(utNumber);
      }
      etichettaLabel.description = description;
      this.etichettaLabelService.createOrUpdateEtichettaLabel(etichettaLabel).subscribe();
    }
  }

}
