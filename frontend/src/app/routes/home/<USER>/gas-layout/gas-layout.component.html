<div class="col-md-11 titleBlock">
  <div class="icon">
  </div>
  <div class="title">
    CONTROLLA STATO E DETTAGLIO DEI TUOI CONSUMI GAS
  </div>
</div>
<div class="app--gas-layout col-md-5 no-padding-mobile block main {{detail?'blockWithDetail':''}}">
  <div *ngIf="sortedServices">
    <div *ngFor="let utility of sortedServices['utilities']; let i = index" class="lineBlock">
      <div
        class="{{!detail?'subBlock':'subBlockWithDetails'}} plainBlock {{( detail && (utility.id == detail.id)) ?'active':'noActive'}}">

        <p class="smallLetter">
          <b>Stato:</b> {{utility.status  | titlecase}}</p>
        <p class="smallLetter">
          <b>PDR:</b> {{utility.utNumber}}</p>


        <p class="addition smallLetter"
           *ngIf="pdrAdditionalDetails[utility.utNumber]&&pdrAdditionalDetails[utility.utNumber].sedeOperativa.length">
          <b>Indirizzo: </b>
          {{pdrAdditionalDetails[utility.utNumber].sedeOperativa[0].indirizzo}},
          {{pdrAdditionalDetails[utility.utNumber].sedeOperativa[0].comune}},
          {{pdrAdditionalDetails[utility.utNumber].sedeOperativa[0].provincia}},
          {{pdrAdditionalDetails[utility.utNumber].sedeOperativa[0].cap}}</p>
        <div *ngIf="utility.status==='ATTIVATO'">
          <div *ngIf="!(detail && (utility.id == detail.id))"
               class="button"
               (click)="selectValue(utility, i)">Dettaglio
          </div>
          <div *ngIf="(detail && (utility.id == detail.id))" class="button nascondi" (click)="hide()">Nascondi</div>
        </div>
      </div>
      <ng-container *ngIf="checkid == i">
        <div class="visible-sm-block visible-xs-block">
          <ng-container *ngTemplateOutlet="gasDetails"></ng-container>
        </div>
      </ng-container>
    </div>
  </div>
</div>
<div class="hidden-sm hidden-xs">
  <ng-container *ngTemplateOutlet="gasDetails"></ng-container>
</div>
<ng-template #gasDetails>
  <div *ngIf="detail" class="col-md-6 block detail">
    <div class="col-md-12 no-padding-mobile">
      <div class="desktopView">
      <span class="detailRow">
        <p class="bigLetter">
          <b>PDR: {{detail.utNumber}}</b>
        </p>
        <b class="smallLetter">Stato: </b>{{detail.status | titlecase}}</span>

        <!--<span class="detailRow addition">{{sedeOperativa}}</span>-->
      </div>
      <span class="detailRow smallLetter">
        <b>Data Attivazione: </b>{{detail.startDate| date : "dd/MM/y"}}</span>
      <span *ngIf="podDetails && podDetails.length>0"
            class="detailRow smallLetter">
        <b>Tipologia d'uso: </b>
        {{podDetails[0].tipoPdrDescrizione}}
      </span>
      <div *ngIf="pdrAdditionalDetails[detail.utNumber]">
        <span
          *ngIf="pdrAdditionalDetails[detail.utNumber].sedeOperativa&& pdrAdditionalDetails[detail.utNumber].sedeOperativa.length>0"
          class="detailRow smallLetter">
          <b>Indirizzo fornitura: </b>
          {{pdrAdditionalDetails[detail.utNumber].sedeOperativa[0].indirizzo}},
          {{pdrAdditionalDetails[detail.utNumber].sedeOperativa[0].comune}},
          {{pdrAdditionalDetails[detail.utNumber].sedeOperativa[0].provincia}},
          {{pdrAdditionalDetails[detail.utNumber].sedeOperativa[0].cap}}
        </span>
        <span *ngIf="podDetails && podDetails.length>0 && podDetails[0].descrizioneContatore"
              class="detailRow smallLetter">
          <b>Tipo Contatore: </b>
          {{podDetails[0].descrizioneContatore}}
        </span>
      </div>
    </div>
  </div>
  <div class="col-md-1 icons" *ngIf="detail">

    <a class="icon-autolettura" [routerLink]="['/faidate/autolettura', 'gas']">
      <div class="icon autolettura"></div>
    </a>

    <section class="icon-selection" *ngIf="pdf">
      <button class="app--btn-dropdown no-hover" [matMenuTriggerFor]="menu">
        <i class="icon modifica"></i>
      </button>
      <mat-menu #menu="matMenu" xPosition="before" yPosition="below">
        <div class="mat-menu-style">
        <button mat-menu-item *ngIf="pdf.length===0">
          <span> No PDF </span>
        </button>
<!--        <button class="red" mat-menu-item *ngIf="pdf.length>0">-->
<!--          Variazioni e richieste-->
<!--        </button>-->
        <button class="menu-button odds-bg" mat-menu-item *ngFor="let pdfRow of pdf">
          <span class="icon-pdf-load"> </span>
          <a target='_blank' href="{{pdfRow.link}}">{{pdfRow.name}}</a>
        </button>
        </div>
      </mat-menu>
    </section>
  </div>

  <div *ngIf="detail" class="col-lg-11 col-md-11 service-chart-block">
    <div class="col-lg-5 col-md-5 clearfix chart-description-block">
      <div class="labels">
        <div class="row legend">
          <app-info>
            <info-button>
              <div class="legend-color tuoi-consumi"></div>
            </info-button>
            <info-message>Smc</info-message>
          </app-info>
          <span class="chart-dot-text special-font-consumi">I tuoi consumi</span>
        </div>
      </div>
      <app-info>
        <info-button><i class="info-circle">i</i></info-button>
        <info-message *ngIf="offers&&offers.length"> Il grafico riporta in rosso i consumi reali rilevati sul tuo
          contatore per i mesi di
          riferimento.
        </info-message>
        <info-message *ngIf="!offers||!offers.length">Il grafico riporta in rosso i consumi reali rilevati sul tuo
          contatore
          per i mesi di riferimento.
        </info-message>
      </app-info>
      <div *ngIf="offers&&offers.length&&consumptionAnnotation>-1" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding special-font">
        <span>Il consumo previsto dal tuo Tutto-In-Uno per questo punto è {{consumptionAnnotation}} smc/mese.</span>
      </div>
    </div>
    <div class="col-lg-7 col-md-7 service-chart clearfix">
      <div class="chart-position">
        <app-chart [config]="chartConfig"></app-chart>
      </div>
      <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1">
    <span *ngIf="chartPaginator&&chartPaginator.hasNext" class="fa fa-angle-left pagination-nav"
          [title]="chartPaginator.nextValue()"
          (click)="chartPaginator.next()"></span>
      </div>
      <div *ngIf="!hasAdjustments" class="col-lg-10 col-md-10 col-sm-10 col-xs-10 text-align-middle">Non risultano
        conguagli emessi.
      </div>
      <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 right">
    <span *ngIf="chartPaginator&&chartPaginator.hasPrevious" class="fa fa-angle-right pagination-nav right"
          [title]="chartPaginator.previousValue()"
          (click)="chartPaginator.previous()"></span>
      </div>
    </div>
  </div>
</ng-template>

