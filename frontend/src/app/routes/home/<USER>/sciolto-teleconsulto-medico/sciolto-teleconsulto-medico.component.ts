import {Component, OnInit} from '@angular/core';
import {IncidentEventService} from '../../../../common/services/incedentEvent/incident-event.service';
import {ScioltoService} from '../../../../common/services/sciolto/sciolto.service';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {UserData} from '../../../../common/model/userData.model';
import {OffersService} from '../../../../common/services/offers/offers.service';
import {ServiceResponseStatus} from '../../../../common/enum/ServiceResponseStatus';

@Component({
  selector: 'app-sciolto-teleconsulto-medico',
  templateUrl: './sciolto-teleconsulto-medico.component.html',
  styleUrls: ['./sciolto-teleconsulto-medico.component.scss']
})
export class ScioltoTeleconsultoMedicoComponent implements OnInit {

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;
  userCluster: string;
  price: number;
  promoTexts: string[] = [];
  withoutIncident: boolean;
  showModalWindowAboutAlreadyOpenIncident: boolean;
  showModalWindowAboutOpenNewIncident: boolean;
  codiceOfferta: string;

  constructor(private scioltoService: ScioltoService, private incidentEventService: IncidentEventService, private offerService: OffersService) {
    this.offerService.checkIncidentEventCrossSelling().subscribe(result => {
      this.withoutIncident = result;
    });
    this.userInfo.subscribe(userInfo => {
      if (userInfo) {
        this.userCluster = userInfo.cluster.value;
      }
    });
    this.scioltoService.getGeneralScioltoInformation().subscribe(generalInformation => {
      if (generalInformation.response[0]) {
        this.codiceOfferta = generalInformation.response[0].fields.Offerta;
        this.scioltoService.getScioltoInformation(generalInformation.response[0].fields.Offerta, this.userCluster).subscribe(information => {
          const fissoPrice = information.response[0].serviziVAS.find(item =>
            item.listini.some(value => value.nome === 'Teleconsulto Medico')
          );
          this.price = fissoPrice ? fissoPrice.listini[0].canone : null;
          const teleconsultoPromos = information.response[0].promo.filter(item =>
            item.Elements.some(value => value.NomeServizio === 'TELECONSULTO MEDICO')
          );
          const teleconsultoPromoOpzionali = information.response[0].promoOpzionali.filter(item =>
            item.Elements.some(value => value.NomeServizio === 'TELECONSULTO MEDICO')
          );
          this.promoTexts = [...teleconsultoPromos, ...teleconsultoPromoOpzionali].map(promo => promo.DescrizioneExt || promo.descrizioneExt);
        });
      }
    });
  }

  ngOnInit() {
  }

  hideModalWindow() {
    this.showModalWindowAboutAlreadyOpenIncident = false;
    this.showModalWindowAboutOpenNewIncident = false;
  }

  buttonClick() {
    if (this.withoutIncident) {
      this.incidentEventService.openIncidentEventForCrossSelling('TELECONSULTO MEDICO', this.codiceOfferta).subscribe(response => {
        if (response.status === ServiceResponseStatus.OK) {
          this.showModalWindowAboutOpenNewIncident = true;
          this.withoutIncident = false;
        }
      });
    } else {
      this.showModalWindowAboutAlreadyOpenIncident = true;
    }
  }
}
