import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AnswersQuestionsResultComponent } from './answers-questions-result.component';

describe('AnswersQuestionsResultComponent', () => {
  let component: AnswersQuestionsResultComponent;
  let fixture: ComponentFixture<AnswersQuestionsResultComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AnswersQuestionsResultComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AnswersQuestionsResultComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
