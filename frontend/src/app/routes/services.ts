import {ServicesNames} from '../common/enum/Service';

// const Luce = {
//   text: 'LUCE',
//   link: '/home/<USER>',
//   icon: 'icon-document',
//   active: false,
//   title: 'LUCE'
// };
// const Gas = {
//   text: 'GAS',
//   link: '/home/<USER>',
//   icon: 'icon-document',
//   active: false,
//   title: 'GAS'
// };
// const Fibra = {
//   text: 'FIBRA',
//   link: '/home/<USER>',
//   icon: 'icon-document',
//   active: false,
//   title: 'INTERNET'
// };
// const Fisso = {
//   text: 'FISSO',
//   link: '/home/<USER>',
//   icon: 'icon-document',
//   active: false,
//   title: 'FISSO'
// };
// const Mobile = {
//   text: 'MOBILE',
//   link: '/home/<USER>',
//   icon: 'icon-document',
//   active: false,
//   title: 'MOBILE'
// };


export const serviceMenu = Object.keys(ServicesNames).map(serviceKey => {
  return {
    text: ServicesNames[serviceKey].toUpperCase(),
    link: `/home/<USER>
    icon: 'icon-document',
    active: false,
    title: serviceKey,
    isScolitoPresent: false
  };
});
