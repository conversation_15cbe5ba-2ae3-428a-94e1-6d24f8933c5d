import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import {CommonModule as AngularCommonModule} from '@angular/common';

import {SharedModule} from '../../shared/shared.module';
import {FaiDaTeContainerComponent} from './containers/fai-da-te-containe/fai-da-te-container.component';
import {RouterModule, Routes} from '@angular/router';
import {CommonModule} from '../../common/common.module';
import {LayoutModule} from '../../layout/layout.module';
import {RouteService} from '../../layout/submenu/route.service';
import {ServiziAttiviComponent} from './components/servizi-attivi/servizi-attivi.component';
import {SegnalazioniComponent} from './components/segnalazioni/segnalazioni.component';
import {UserServicesService} from '../profilePage/userServices/userServices.service';
import {AutoletturaComponent} from './components/autolettura/autolettura.component';
import {HomeService} from '../home/<USER>/home/<USER>';
import {ActiveServicesLayoutComponent} from './containers/active-services-layout/active-services-layout.component';
import {MobileLayoutComponent} from './containers/mobile-layout/mobile-layout.component';
import {MobileModule} from '../mobile/mobile.module';
import {TopUpSimLayoutComponent} from '../mobile/containers/top-up-sim/top-up-sim-layout/top-up-sim-layout.component';
import {TopUpSimWithCreditCardLayoutComponent} from '../mobile/containers/top-up-sim/top-up-sim-with-credit-card-layout/top-up-sim-with-credit-card-layout.component';
import {TopUpSimSisalLayoutComponent} from '../mobile/containers/top-up-sim/top-up-sim-sisal-layout/top-up-sim-sisal-layout.component';
import {AddOptionsLayoutComponent} from '../mobile/containers/add-options-layout/add-options-layout.component';
import {SegnalazioniService} from './components/segnalazioni/segnalazioni.service';
import {OfferModificationLayoutComponent} from '../mobile/containers/offer-modification-layout/offer-modification-layout.component';
import {TrafficDetailsLayoutComponent} from '../mobile/containers/traffic-details-layout/traffic-details-layout.component';
import {EnergiaLayoutComponent} from './containers/energia-layout/energia-layout.component';
import {IncidentEventComponent} from '../autolettura/components/incident-event/incident-event.component';
import {AutoletturaModule} from '../autolettura/autolettura.module';
import {MatMenuModule} from '@angular/material';
import {ReactiveFormsModule} from '@angular/forms';
import {PopoverModule} from 'ngx-bootstrap';
import {GasLayoutComponent} from './containers/gas-layout/gas-layout.component';
import {VoiceLayoutComponent} from './containers/voice-layout/voice-layout.component';
import {FibraLayoutComponent} from './containers/fibra-layout/fibra-layout.component';
import {ConfigurazioneRouterComponent} from './containers/router-configuration-container/configurazione-router/configurazione-router.component';
import {RouterContainerComponent} from './containers/router-configuration-container/router-container/router-container.component';
import {HasVoipRouterComponent} from './containers/router-configuration-container/has-voip-router/has-voip-router.component';
import {NoVoipRouterComponent} from './containers/router-configuration-container/no-voip-router/no-voip-router.component';
import {VoipAdslrouterComponent} from './containers/router-configuration-container/voip-adslrouter/voip-adslrouter.component';
import {NoAdslrouterComponent} from './containers/router-configuration-container/no-adslrouter/no-adslrouter.component';
import {HasVoipRouterFibraComponent} from './containers/router-configuration-container/has-voip-router-fibra/has-voip-router-fibra.component';
import {ConfigurazioneRouterContainerComponent} from './components/configurazione-router-component/configurazione-router-container.component';
import {AggiornaComponent} from './containers/router-configuration-container/aggiorna-router/aggiorna .component';
import {ReContactLayoutComponent} from './containers/re-contact-layout/re-contact-layout.component';
import {RecontactService} from './services/recontact/recontact.service';
import {ResolutionCheckerGuard} from './utils/resolution-checker.guard';

import {AmazonPrimeLayoutComponent} from './containers/amazon-prime-layout/amazon-prime-layout.component';
import {DialogModalEntity} from '../../common/model/dialogModal/DialogModalEntity';

import {AttachmentService} from './services/attachment/attachment.service';
import {InviaSegnalazioniComponent} from './components/invia-segnalazioni/invia-segnalazioni.component';
import {ChartsModule} from 'ng2-charts';
import {RichiediPortabilitaComponent} from '../mobile/containers/richiedi-portabilita/richiedi-portabilita.component';
import {AddOptionsRichiediPortabilitaComponent} from '../mobile/containers/richiedi-portabilita/add-options-richiedi-portabilita/add-options-richiedi-portabilita.component';
import {MedicalTeleconsultationLayoutComponent} from './containers/medical-teleconsultation-layout/medical-teleconsultation-layout.component';
import {RichiediVisitaAgenteComponent} from './components/richiedi-visita-agente/richiedi-visita-agente.component';
import {RichiediVisitaAgenteService} from './services/richiedi-visita-agente/richiedi-visita-agente.service';
import {LoggerService} from '../../common/services/logger/logger.service';
import {TutelaLegaleLayoutComponent} from './containers/tutela-legale-layout/tutela-legale-layout.component';
import {AssistenzaH24LayoutComponent} from './containers/assistenza-h24-layout/assistenza-h24-layout.component';
import {TotalSecurityLayoutComponent} from './containers/total-security-layout/total-security-layout.component';
import {SafeCallLayoutComponent} from './containers/safe-call-layout/safe-call-layout.component';
import {AutoricaricaInformationComponent} from '../mobile/containers/top-up-sim/autoricarica-information/autoricarica-information.component';
import {Information5gLayoutComponent} from "../mobile/containers/information-5g-layout/information-5g-layout.component";

const routes: Routes = [
  {
    path: '', component: FaiDaTeContainerComponent, children: [
      {path: '', canActivate: [ResolutionCheckerGuard]},
      {
        path: 'servizi-attivi', component: ActiveServicesLayoutComponent, children: [
          {path: '', component: ServiziAttiviComponent},
          {
            path: 'mobile', component: MobileLayoutComponent, children: [
              {path: ':id', component: MobileLayoutComponent},
              {path: 'ricarica/:id', component: TopUpSimWithCreditCardLayoutComponent},
              {path: 'ricarica/:id/:amount/metodi', component: TopUpSimLayoutComponent},
              {path: 'ricarica/:id/:amount/metodi/sisal', component: TopUpSimSisalLayoutComponent},
              {path: 'ricarica/:id/autoricarica', component: TopUpSimLayoutComponent},
              {path: 'ricarica/:id/autoricarica/informazioni', component: AutoricaricaInformationComponent},
              {path: 'agguingi/:id', component: AddOptionsLayoutComponent},
              {path: 'modifica/:id', component: OfferModificationLayoutComponent},
              {path: 'dettaglio/:id', component: TrafficDetailsLayoutComponent},
              {path: 'portabilita/:id', component: RichiediPortabilitaComponent},
              {path: 'richiedi-portabilita/:id', component: AddOptionsRichiediPortabilitaComponent},
              {path: '5G/:id', component: Information5gLayoutComponent}]
          },

          {path: 'energia/:pod', component: EnergiaLayoutComponent},
          {path: 'gas/:pod', component: GasLayoutComponent},
          {path: 'wlr/:pod', component: VoiceLayoutComponent},
          {path: 'adsl/:pod', component: FibraLayoutComponent},
          {path: 'amazon-prime', component: AmazonPrimeLayoutComponent},
          {path: 'teleconsulto-medico', component: MedicalTeleconsultationLayoutComponent},
          {path: 'tutela-legale', component: TutelaLegaleLayoutComponent},
          {path: 'total-security', component: TotalSecurityLayoutComponent},
          {path: 'safe-call', component: SafeCallLayoutComponent},
          {path: 'assistenza-h24', component: AssistenzaH24LayoutComponent}]},
      {path: 'segnalazioni', component: SegnalazioniComponent},
      {path: 'segnalazioni/invia-segnalazioni', component: InviaSegnalazioniComponent},
      {path: 'autolettura', component: IncidentEventComponent},
      {path: 'autolettura/:type', component: IncidentEventComponent},
      {
        path: 'routerConfigurazione', component: ConfigurazioneRouterContainerComponent, children: [
          {path: 'configuraIlTuoRouter', component: ConfigurazioneRouterComponent},
          {path: 'aggiorna', component: AggiornaComponent}]
      },
      {path: 'recontact', component: ReContactLayoutComponent},
      {path: 'richiedi-visita-agente', component: RichiediVisitaAgenteComponent}
    ]
  }
];

@NgModule({
  imports: [
    SharedModule,
    AngularCommonModule,
    CommonModule,
    LayoutModule,
    RouterModule.forChild(routes),
    MobileModule,
    AutoletturaModule,
    MatMenuModule,
    ReactiveFormsModule,
    PopoverModule,
    ChartsModule
  ],
  declarations: [FaiDaTeContainerComponent, ServiziAttiviComponent, SegnalazioniComponent, AutoletturaComponent,
    ActiveServicesLayoutComponent, MobileLayoutComponent, EnergiaLayoutComponent, GasLayoutComponent,
    VoiceLayoutComponent, FibraLayoutComponent, ConfigurazioneRouterComponent, RouterContainerComponent,
    HasVoipRouterComponent, NoVoipRouterComponent, VoipAdslrouterComponent, NoAdslrouterComponent,
    HasVoipRouterFibraComponent, ConfigurazioneRouterContainerComponent, AggiornaComponent, ReContactLayoutComponent, AmazonPrimeLayoutComponent, InviaSegnalazioniComponent,
    MedicalTeleconsultationLayoutComponent, RichiediVisitaAgenteComponent, TutelaLegaleLayoutComponent, AssistenzaH24LayoutComponent, TotalSecurityLayoutComponent, SafeCallLayoutComponent],
  providers: [RouteService, UserServicesService, HomeService, SegnalazioniService, RecontactService, ResolutionCheckerGuard, DialogModalEntity, AttachmentService, RichiediVisitaAgenteService, LoggerService],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class FaiDaTeModule {
}
