import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import {RichiediVisitaAgenteRequest} from '../../model/RichiediVisitaAgenteModels';

@Injectable()
export class RichiediVisitaAgenteService {

  constructor(private httpClient: HttpClient) {
  }

  getInformationAboutAgent(request: RichiediVisitaAgenteRequest): Observable<any> {
    return this.httpClient.post('/api/business-agent/information', request);
  }
}
