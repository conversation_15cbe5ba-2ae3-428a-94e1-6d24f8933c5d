import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';

@Injectable()
export class AttachmentService {

  URL = '/api/segnalazione/attachment';
  URL_MULTI_ATTACHMENT = '/api/attachment';
  URL_WITHOUT_ATTACHMENT = '/api/segnalazione/incident';

  constructor(private http: HttpClient) {
  }

  sendAttachment(data: FormData) {
    return this.http.post(this.URL, data);
  }

  sendMultiAttachment(data: FormData) {
    return this.http.post(this.URL_MULTI_ATTACHMENT, data);
  }

  sendIncident(data: FormData) {
    return this.http.post(this.URL_WITHOUT_ATTACHMENT, data);
  }
}
