import {Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import 'rxjs/add/observable/forkJoin';
import {ActiveServiceStatus, InActiveServiceStatus} from '../../../../common/enum/ServiceStatus';
import {Router} from '@angular/router';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';
import {Subscription} from 'rxjs/Subscription';
import {ServicesActions} from '../../../../redux/services/actions';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';

@Component({
  selector: 'app-configurazione-router',
  templateUrl: './configurazione-router-container.component.html',
  styleUrls: ['./configurazione-router-container.component.scss']
})
export class ConfigurazioneRouterContainerComponent implements OnDestroy, OnInit {
  @select(['services'])
  services: Observable<ServiceStateModel>;
  hasActiveAdsl = false;
  hasDisactiveAdsl = false;
  serviceSubscription: Subscription;
  routerInfo = {};

  constructor(private serviceActions: ServicesActions, private router: Router) {

  }

  ngOnInit() {
    this.getData();
  }

  private getData() {
    this.serviceSubscription = this.services.subscribe(routerService => {
      const {routerInfoLoading, routerInfoLoaded, servicesLoaded, routerInfo, activeServices} = routerService;
      if (servicesLoaded) {  // quick fix should load only particular services
        Object.keys(activeServices).forEach(key => this.serviceActions.loadServiceDetails(key));
      }
      if (routerInfoLoaded) {
        this.setVariables(routerInfo);
      }
      if (!routerInfoLoaded && !routerInfoLoading) {
        this.serviceActions.loadRouterInfo(localStorage.getItem('clientId'));
      }
    });
  }

  setVariables(routerInfo) {
    if (routerInfo && routerInfo['LineeADSL']) {
      this.hasActiveAdsl = routerInfo['LineeADSL'].filter(linea =>
        !!ActiveServiceStatus[linea.stato.toUpperCase()]
      ).length > 0;
      this.hasDisactiveAdsl = routerInfo ['LineeADSL'].filter(linea =>
        !!InActiveServiceStatus[linea.stato.toUpperCase()]
      ).length > 0;
    }
    if (!this.hasActiveAdsl || this.hasDisactiveAdsl && !this.hasActiveAdsl) {
      this.router.navigate(['/faidate/routerConfigurazione/aggiorna']);
    }
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.serviceSubscription]);
  }
}

