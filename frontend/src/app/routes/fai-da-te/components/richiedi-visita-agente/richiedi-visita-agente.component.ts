import {Component, OnInit} from '@angular/core';
import {RichiediVisitaAgenteService} from '../../services/richiedi-visita-agente/richiedi-visita-agente.service';
import {RichiediVisitaAgenteRequest} from '../../model/RichiediVisitaAgenteModels';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {FormUtils} from '../../../../common/utils/FormUtils';
import EmailMessage from '../../../../common/model/EmailMessage';
import {EmailService} from '../../../../common/services/email/email.service';
import {LoggerService} from '../../../../common/services/logger/logger.service';

@Component({
  selector: 'app-richiedi-visita-agente',
  templateUrl: './richiedi-visita-agente.component.html',
  styleUrls: ['./richiedi-visita-agente.component.scss']
})
export class RichiediVisitaAgenteComponent implements OnInit {

  formGroup: FormGroup;
  agentName: string;

  showModalWindow: boolean;
  isAgentSpecialist: boolean;
  isNouvoModello: boolean;
  isNameAndSurnameExist: boolean;

  constructor(private service: RichiediVisitaAgenteService, private formBuilder: FormBuilder, private emailService: EmailService, private logger: LoggerService) {
    const request = new RichiediVisitaAgenteRequest();
    request.idcliente = localStorage.getItem('clientId');
    this.service.getInformationAboutAgent(request).subscribe(
      response => {
        for (const item of response) {
          if (item.Ruolo === 'Specialist' || item.Ruolo === 'Responsabile Specialist' || item.Ruolo === 'Coordinatore Specialist') {
            this.isAgentSpecialist = true;
            this.agentName = item.Nome + ' ' + item.Cognome;
          } else if (item.NomeTarget === 'Nuovo Modello') {
            this.agentName = item.Nome + ' ' + item.Cognome;
            this.isNouvoModello = true;
          } else if (item.Nome && item.Cognome) {
            this.agentName = item.Nome + ' ' + item.Cognome;
            this.isNameAndSurnameExist = true;
          }
        }
      });
    this.formGroup = this.buildForm();
  }

  ngOnInit() {
  }

  buildForm() {
    return this.formBuilder.group({
      questionTitle: new FormControl(1, Validators.required),
      description: new FormControl(null, Validators.required)
    });
  }

  checkForm() {
    if (!this.formGroup.valid || this.formGroup.value.questionTitle === 1) {
      FormUtils.setFormControlsAsTouched(this.formGroup);
    } else {
      const email = new EmailMessage();
      email.subject = 'Richiesta Visita agente da Area Clienti Web Codice Cliente ' + localStorage.getItem('clientId');
      if (this.isAgentSpecialist) {
        email.message = `Il cliente in oggetto, che risulta affidato a te, chiede di essere contattato per fissare appuntamento.\n` +
          `Descrizione richiesta: ${this.formGroup.value.questionTitle}.\nIl motivo richiesta:${this.formGroup.value.description}.\n` +
          `Contattare il cliente per fissare incontro. Grazie.`;
      } else if (this.isNouvoModello) {
        email.message = `Il cliente in oggetto chiede di essere contattato per fissare appuntamento con sale di riferimento ${this.agentName}.\n` +
          `Descrizione richiesta: ${this.formGroup.value.questionTitle}.\nIl motivo richiesta: ${this.formGroup.value.description}\n.` +
          `Fissare appuntamento in agenda sale. Grazie.`;
      } else if (this.isNameAndSurnameExist) {
        email.message = `Il cliente in oggetto chiede di essere contattato per fissare appuntamento con sale di riferimento ${this.agentName}.\n` +
          `Descrizione richiesta: ${this.formGroup.value.questionTitle}\nIl motivo richiesta: ${this.formGroup.value.description}.\n` +
          `Inviare mail al sale e a manager di riferimento per confermare/fissare incontro. Grazie.`;
      } else {
        email.message = `Il cliente in oggetto chiede di essere contattato per fissare appuntamento.\n` +
          `Descrizione richiesta: ${this.formGroup.value.questionTitle}.\nIl motivo richiesta: ${this.formGroup.value.description}.\n` +
          `Verificare se il cliente è associato ad un sale appartenente al target “Nuovo modello” e in questo caso fissare appuntamento col Sale di riferimento. ` +
          `Se non risulta alcun sale associato, associare sale e contattare il cliente per fissare incontro. Grazie.`;
        email.recipient = 'giaClienti';
      }
      this.emailService.sendRequestForBookVisitAgent(email).subscribe(
        response => {
          if (response.status === 200) {
            this.showModalWindow = true;
            this.formGroup.get('questionTitle').patchValue(1);
            this.formGroup.get('description').patchValue(null);
          }
        }
      );
    }
  }

  hideModalWindow() {
    this.showModalWindow = false;
  }

  logEvent(event?: any) {
    this.logger.logVisitAgentInformation(event);
  }
}
