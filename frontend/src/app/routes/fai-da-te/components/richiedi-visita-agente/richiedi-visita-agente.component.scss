@import "~app/routes/fai-da-te/styles/new-style-service-common";
@import "../../../../shared/styles/colors";

.mainBlock {
  background: white;
}

select {
  -webkit-appearance: none; /*Removes default chrome and safari style*/
  -moz-appearance: none;
  background: url('/../../../../../assets/img/icons/arrow-down_16x16.png') no-repeat right white;
  background-position-x: 96%;
}

.form-control {
  border: 1px solid #c8d9e9;
  border-radius: 5px;
  font-weight: bold;
  color: #36749CFF;
  cursor: pointer;
}

option {
  font-weight: bold;
}

.select-options {
  width: 49%;
}

input {
  border: 1px solid #B0C7DD;
  opacity: 1;
  border-radius: 5px;
  width: 49%;
  height: 87px;
  padding-bottom: 5%;
  padding-left: 1%;
}

.span-text {
  text-align: left;
  font: italic normal normal 13px/19px Lato;
  letter-spacing: 0;
  opacity: 1;
  padding-top: 5px;
  padding-left: 10px;
}

.button {
  color: white;
  border: 1px solid #9BC641;
  border-radius: 8px;
  background-color: $green;
  width: 24%;
  height: 40px;
  font-size: 15px;
  padding: 0;
}

img {
  margin-top: -17%;
  width: 30%;
  margin-left: 40%;
  margin-bottom: -2%;
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  min-height: 395px;
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 30px;
  border: 2px solid #B0C7DD;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 680px;
}

.display {
  display: block;
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 50px;
  top: 15px;
  color: #36749d;
  cursor: pointer;
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 64%;
  margin: auto;
  padding-top: 30px;
  font-size: 28px;
}

.modal-image {
  text-align: center;
  background-color: white;
  margin: 3% auto auto;
  width: 15%;
  display: block;
}

@media only screen and (max-width: 615px) {
  .button {
    font-size: 12px;
  }
}

@media only screen and (max-width: 465px) {
  .button {
    font-size: 10px;
  }
  img {
    margin-top: -11%;
    width: 44%;
    margin-left: 30%;
    margin-bottom: -2%;
  }
  .form-control {
    width: 160%;
  }
  input {
    width: 78%;
  }
}
