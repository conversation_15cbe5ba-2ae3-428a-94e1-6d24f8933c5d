<!--<div class="row app&#45;&#45;autolettura">-->
<!--    <div class="col-md-12">-->
<!--        <div class="row">-->
<!--            <div class="col-md-6">-->
<!--                <div class="panel app&#45;&#45;panel-block">-->
<!--                    <div class="panel-heading">-->
<!--                        <div class="app&#45;&#45;icon app&#45;&#45;panel-title-icon app&#45;&#45;icon-luce"></div>-->
<!--                        <div class="app&#45;&#45;panel-title">Autolettura LUCE</div>-->
<!--                    </div>-->
<!--                    <div class="panel-body app&#45;&#45;panel-body" style="height: 400px">-->
<!--                        <div [formGroup]="luceFormGroup">-->
<!--                            <div class="form-group app&#45;&#45;form-group app&#45;&#45;form-group-popup">-->
<!--                                <div class="input-group">-->
<!--                                    <select class="form-control" formControlName="contatore">-->
<!--                                        <option value=""></option>-->
<!--                                        <option *ngFor="let item of luceContatoreList" [attr.value]="item.value">-->
<!--                                            {{item.title}}-->
<!--                                        </option>-->
<!--                                    </select>-->
<!--                                    <span class="input-group-addon app&#45;&#45;input-group-addon-hide">-->
<!--                                        <em class="fa fa-info-circle"></em>-->
<!--                                    </span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="form-group app&#45;&#45;form-group app&#45;&#45;form-group-popup">-->
<!--                                <div class="input-group">-->
<!--                                    <label>Quale tipo di contatore possiedi?</label>-->
<!--                                    <select class="form-control" formControlName="contatoreType">-->
<!--                                        <option value=""></option>-->
<!--                                        <option *ngFor="let item of luceContatoreTypeList" [attr.value]="item.value">-->
<!--                                            {{item.title}}-->
<!--                                        </option>-->
<!--                                    </select>-->
<!--                                    <span class="input-group-addon" placement="left" triggers="mouseenter:mouseleave" [popover]="lucePopTemplate">-->
<!--                                        <em class="fa fa-info-circle"></em>-->
<!--                                    </span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <ng-container *ngIf="selectedLuceContatoreType == 'Meccanico'">-->
<!--                                <div class="form-group app&#45;&#45;form-group app&#45;&#45;form-group-popup">-->
<!--                                    <div class="input-group">-->
<!--                                        <input class="form-control" type="string" placeholder="LETTURA" />-->
<!--                                        <span class="input-group-addon app&#45;&#45;input-group-addon-hide">-->
<!--                                            <em class="fa fa-info-circle"></em>-->
<!--                                        </span>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </ng-container>-->
<!--                            <ng-container *ngIf="selectedLuceContatoreType == 'Elettronico'">-->
<!--                                <div class="form-group app&#45;&#45;form-group app&#45;&#45;form-group-popup">-->
<!--                                    <div class="input-group">-->
<!--                                        <input class="form-control" type="string" placeholder="F1" />-->
<!--                                        <span class="input-group-addon app&#45;&#45;input-group-addon-hide">-->
<!--                                            <em class="fa fa-info-circle"></em>-->
<!--                                        </span>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                                <div class="form-group app&#45;&#45;form-group app&#45;&#45;form-group-popup">-->
<!--                                    <div class="input-group">-->
<!--                                        <input class="form-control" type="string" placeholder="F2" />-->
<!--                                        <span class="input-group-addon app&#45;&#45;input-group-addon-hide">-->
<!--                                            <em class="fa fa-info-circle"></em>-->
<!--                                        </span>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                                <div class="form-group app&#45;&#45;form-group app&#45;&#45;form-group-popup">-->
<!--                                    <div class="input-group">-->
<!--                                        <input class="form-control" type="string" placeholder="F3" />-->
<!--                                        <span class="input-group-addon app&#45;&#45;input-group-addon-hide">-->
<!--                                            <em class="fa fa-info-circle"></em>-->
<!--                                        </span>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </ng-container>-->

<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="panel-footer app&#45;&#45;panel-footer">-->
<!--                        <button class="btn app&#45;&#45;btn-inserisci">-->
<!--                            <span> INSERISCI AUTOLETTURA </span>-->
<!--                        </button>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="col-md-6">-->
<!--                <div class="panel app&#45;&#45;panel-block">-->
<!--                    <div class="panel-heading">-->
<!--                        <div class="app&#45;&#45;icon app&#45;&#45;panel-title-icon app&#45;&#45;icon-gas"></div>-->
<!--                        <div class="app&#45;&#45;panel-title">Autolettura GAS</div>-->
<!--                    </div>-->
<!--                    <div class="panel-body app&#45;&#45;panel-body" style="height: 400px">-->
<!--                        <div [formGroup]="gasFormGroup">-->
<!--                            <div class="form-group app&#45;&#45;form-group app&#45;&#45;form-group-popup">-->
<!--                                <div class="input-group">-->
<!--                                    <select class="form-control" formControlName="contatore">-->
<!--                                        <option value=""></option>-->
<!--                                        <option *ngFor="let item of gasContatoreList" [attr.value]="item.value">-->
<!--                                            {{item.title}}-->
<!--                                        </option>-->
<!--                                    </select>-->
<!--                                    <span class="input-group-addon app&#45;&#45;input-group-addon-hide">-->
<!--                                        <em class="fa fa-info-circle"></em>-->
<!--                                    </span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="form-group app&#45;&#45;form-group app&#45;&#45;form-group-popup">-->
<!--                                <div class="input-group">-->
<!--                                    <label>Quale tipo di contatore possiedi?</label>-->
<!--                                    <select class="form-control" formControlName="contatoreType">-->
<!--                                        <option value=""></option>-->
<!--                                        <option *ngFor="let item of gasContatoreTypeList" [attr.value]="item.value">-->
<!--                                            {{item.title}}-->
<!--                                        </option>-->
<!--                                    </select>-->
<!--                                    <span #gasPopup class="input-group-addon" placement="left" triggers="mouseenter:mouseleave" [popover]="gasPopTemplate">-->
<!--                                        <em class="fa fa-info-circle"></em>-->
<!--                                    </span>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                            <ng-container *ngIf="selectedGasContatoreType != null">-->
<!--                                <div class="form-group app&#45;&#45;form-group app&#45;&#45;form-group-popup">-->
<!--                                    <div class="input-group">-->
<!--                                        <input class="form-control" type="string" placeholder="LETTURA" />-->
<!--                                        <span class="input-group-addon app&#45;&#45;input-group-addon-hide">-->
<!--                                            <em class="fa fa-info-circle"></em>-->
<!--                                        </span>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </ng-container>-->



<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="panel-footer app&#45;&#45;panel-footer">-->
<!--                        <button class="btn app&#45;&#45;btn-inserisci">-->
<!--                            <span> INSERISCI AUTOLETTURA </span>-->
<!--                        </button>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
<!--</div>-->

<!--<ng-template #lucePopTemplate>-->
<!--    <ng-container *ngIf="selectedLuceContatoreType == null || selectedLuceContatoreType == undefine">-->
<!--        Selezionare il tipo contatore.-->
<!--    </ng-container>-->
<!--    <ng-container *ngIf="selectedLuceContatoreType =='Elettronico'">-->
<!--        <div class="app&#45;&#45;popup-contatore-type">-->
<!--            <div>-->
<!--                <div class="app&#45;&#45;popup-icon app&#45;&#45;icon app&#45;&#45;icon-aut1"></div>-->
<!--                <div class="app&#45;&#45;popup-title">-->
<!--                    <h4>Contatore luce elettronico</h4>-->
<!--                    <span>Il consumo è visualizzabile per fasce orarie premendo l’apposito pulsante.</span>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="app&#45;&#45;popup-description">-->
<!--                <p>Premendo il pulsante appaiono in ordine: ID Cliente, poi la fascia oraria in atto, la potenza istantanea-->
<!--                    e la lettura dei totalizzatori di energia e potenza relativi per ogni fascia oraria.</p>-->
<!--                <p>Premere in sequenza il pulsante del contatore per cinque volte, ﬁnché compareranno sul display i consumi-->
<!--                    e la potenza.</p>-->
<!--            </div>-->
<!--        </div>-->
<!--    </ng-container>-->
<!--    <ng-container *ngIf="selectedLuceContatoreType =='Meccanico'">-->
<!--        <div class="app&#45;&#45;popup-contatore-type">-->
<!--            <div>-->
<!--                <div class="app&#45;&#45;popup-icon app&#45;&#45;icon app&#45;&#45;icon-aut2"></div>-->
<!--                <div class="app&#45;&#45;popup-title">-->
<!--                    <h4>luce meccanico</h4>-->
<!--                    La lettura compare in modo automatico sul display trasparente.-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="app&#45;&#45;popup-description">-->
<!--                Il valore da registrare è esclusivamente il numero segnato su fondo nero. I decimali su fondo rosso non devono essere comunicati.-->
<!--            </div>-->
<!--        </div>-->
<!--    </ng-container>-->
<!--</ng-template>-->


<!--<ng-template #gasPopTemplate>-->
<!--    <ng-container *ngIf="selectedGasContatoreType == null || selectedGasContatoreType == undefine">-->
<!--        Selezionare il tipo contatore.-->
<!--    </ng-container>-->
<!--    <ng-container *ngIf="selectedGasContatoreType =='Senza correttore'">-->
<!--        <div class="app&#45;&#45;popup-contatore-type">-->
<!--            <div>-->
<!--                <div class="app&#45;&#45;popup-icon app&#45;&#45;icon app&#45;&#45;icon-contatoreGas"></div>-->
<!--                <div class="app&#45;&#45;popup-title">-->
<!--                    <h4>Contatore luce elettronico</h4>-->
<!--                    <span>Il consumo è visualizzabile per fasce orarie premendo l’apposito pulsante.</span>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="app&#45;&#45;popup-description">-->
<!--                <p>Premendo il pulsante appaiono in ordine: ID Cliente, poi la fascia oraria in atto, la potenza istantanea-->
<!--                    e la lettura dei totalizzatori di energia e potenza relativi per ogni fascia oraria.</p>-->
<!--                <p>Premere in sequenza il pulsante del contatore per cinque volte, ﬁnché compareranno sul display i consumi-->
<!--                    e la potenza.</p>-->
<!--            </div>-->
<!--        </div>-->
<!--    </ng-container>-->
<!--    <ng-container *ngIf="selectedGasContatoreType =='Con correttore'">-->
<!--        <div class="app&#45;&#45;popup-contatore-type">-->
<!--            <div>-->
<!--                <div class="app&#45;&#45;popup-icon app&#45;&#45;icon app&#45;&#45;icon-contatoreGasDigit"></div>-->
<!--                <div class="app&#45;&#45;popup-title">-->
<!--                    <h4>luce meccanico</h4>-->
<!--                    <span>La lettura compare in modo automatico sul display trasparente.</span>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="app&#45;&#45;popup-description">-->
<!--                Il valore da registrare è esclusivamente il numero segnato su fondo nero. I decimali su fondo rosso non devono essere comunicati.-->
<!--            </div>-->
<!--        </div>-->
<!--    </ng-container>-->
<!--</ng-template>-->
