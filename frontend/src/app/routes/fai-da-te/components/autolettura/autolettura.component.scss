// .container-fluid {
//     padding: 5% 0 0 0;
//   }
  
//   .panel.panel-default {
//     width: 100%;
//     margin: 0 auto 5% auto;
//     min-height: 70vh;
//     font-size: 1.5em;
//   }
  
//   .row {
//     margin: auto;
//   }
//   @media only screen and (max-width: 1600px) {
  
//     .panel.panel-default {
//       font-size: 1.2em;
//     }
//   }
//   @media only screen and (max-width: 1200px) {
  
//     .panel.panel-default {
//       font-size: 1em;
//     }
//   }
//   @media only screen and (max-width: 800px) {
//     .container-fluid {
//       padding: 50px 0 0 0;
//     }
//     .panel.panel-default{
//       width:80%;
//       margin: 2% auto;
//     }
//     .panel-body{
//       padding: 20px;
//     }
//   }


// RESET PREVIOUS STYLES
.panel { box-shadow: none; border-radius: 0; }

.app--autolettura{
  .app--panel-block {
    border: 1px solid #B6CCE3;
    border-radius: 10px;
  }
  .app--panel-title-icon { display: inline-block; }
  .app--panel-title {
    display: inline-block;
    vertical-align: top;
    margin-top: 15px;
    margin-left: 10px;
    color: #e54c36;
    font-size: 20px;
    font-weight: 700;
  }

  .app--panel-body{
    border: 1px solid #B6CCE3;
    border-left: none;
    border-right: none;
    background-color: #F0F5F9;
  }

  .app--panel-footer {
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
  }
  
  .app--icon { height: 60px; width: 60px; }
  .app--icon-luce{ background: url("../../../../../assets/img/autolettura/luceAutt.png") no-repeat center; background-size: contain; }
  .app--icon-gas{ background: url("../../../../../assets/img/autolettura/gasAut.png") no-repeat center; background-size: contain; }
  .app--icon-aut1{ background: url("../../../../../assets/img/autolettura/aut1.jpg") no-repeat center; background-size: contain; }
  .app--icon-aut2{ background: url("../../../../../assets/img/autolettura/aut2.png") no-repeat center; background-size: contain; }
  .app--icon-contatoreGas{ background: url("../../../../../assets/img/autolettura/contatoreGas.jpg") no-repeat center; background-size: contain; }
  .app--icon-contatoreGasDigit{ background: url("../../../../../assets/img/autolettura/contatoreGasDigit.jpg") no-repeat center; background-size: contain; }

  .app--form-group { 
    label { font-size: 12px; color: #36749D;  }
    .form-control { border-radius: 5px; }
  }

  .app--btn-inserisci {
    display: block;
    margin: 0 auto;
    border: 2px solid #97B7CC;
    background-color: transparent;
    font-weight: 600;
    color: #336D94;
    padding: 5px 15px;
    border-radius: 7px;
  }

  .app--form-group-popup {
    .app--input-group-addon-hide { visibility: hidden; }
    .input-group-addon { padding: 0 10px; background-color: transparent; border: none; vertical-align: bottom; padding-bottom: 8px; }
    .input-group-addon .fa { font-size: 20px;color: #36749D; }
   }


   .app--popup-icon { display: inline-block; }
   .app--popup-title { display: inline-block; vertical-align: top; font-weight: 500px;
  }
}