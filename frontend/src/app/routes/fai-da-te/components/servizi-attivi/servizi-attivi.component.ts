import {Component, OnDestroy} from '@angular/core';
import {fromJS} from 'immutable';
import {UserServicesService} from '../../../profilePage/userServices/userServices.service';
import {OptimaIconUtils} from '../../../../common/utils/OptimaIconUtils';
import {ServiziAttiviService} from '../../../../common/services/servizi-attivi/servizi-attivi.service';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';
import {
  UserServicesObjects,
  UserServices,
  Utility, UserNewServicesObject
} from '../../../../common/model/services/userServices.model';
import {MobileRecordStatus} from '../../../../common/enum/MobileRecordStatus';
import {ServiceStatus} from '../../../../common/enum/ServiceStatus';
import {ServiceUtils} from '../../../../common/utils/ServiceUtils';
import {
  ServiziAttiviRouteService
} from '../../../../common/services/servizi-attivi-router/servizi-attivi-route.service';
import {ServicesActions} from '../../../../redux/services/actions';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';
import {Subscription} from 'rxjs/Subscription';

const allowedMobileStatuses = [MobileRecordStatus.ACTIVE, MobileRecordStatus.INITIACTIVE,
  MobileRecordStatus.SOFT_SUSPENSION, MobileRecordStatus.HARD_SUSPENSION, MobileRecordStatus.LOST_SIM];

@Component({
  selector: 'app-servizi-attivi',
  templateUrl: './servizi-attivi.component.html',
  styleUrls: ['./servizi-attivi.component.scss']
})
export class ServiziAttiviComponent implements OnDestroy {

  @select(['services'])
  serviceData: Observable<ServiceStateModel>;

  serviceUtils = ServiceUtils;
  activeServices = {};
  userServices: Array<UserServices> = [];
  serviziAggiuntiviServices: Array<UserServices> = [];
  utenza: Utility;
  pdf: Array<any>;
  lucePodDetails;
  gasPodDetails;
  adslPodDetails;
  fissoPodDetails;
  private userServicesSubscription: Subscription;

  constructor(private service: UserServicesService, private optimaIconUtils: OptimaIconUtils,
              private routeService: ServiziAttiviRouteService, private attiviService: ServiziAttiviService,
              private serviceActions: ServicesActions) {
    this.userServicesSubscription = this.serviceData.subscribe(userServices => {
      const {
        activeServices, services, lucePodDetails, gasPodDetails, adslPodDetails, fissoPodDetails,
        servicesLoaded
      } = userServices;
      if (servicesLoaded) {
        Object.keys(activeServices).forEach(key => this.serviceActions.loadServiceDetails(key));
      }
      this.activeServices = activeServices;
      this.lucePodDetails = lucePodDetails;
      this.gasPodDetails = gasPodDetails;
      this.adslPodDetails = adslPodDetails;
      this.fissoPodDetails = fissoPodDetails;
      this.userServices = fromJS(services).map(value => {
        return value.set('utilities', value.get('utilities').filter(
          i => i.get('status') === ServiceStatus.ATTIVATO ||
            i.get('status') === ServiceStatus.IN_ATTIVAZIONE ||
            i.get('status') === ServiceStatus.ATTIVO ||
            allowedMobileStatuses.indexOf(i.get('status')) > -1));
      }).filter(s => s.get('utilities') !== null && !s.get('utilities').isEmpty()
        && s.get('serviceName') !== 'AMAZON' && s.get('serviceName') !== 'Teleconsulto medico'
        && s.get('serviceName') !== 'TutelaLegale' && s.get('serviceName') !== 'Assistenza H24'
        && s.get('serviceName') !== 'TotalSecurity' && s.get('serviceName') !== 'SafeCall').toJS();

      this.serviziAggiuntiviServices = fromJS(services).map(value => {
        const updatedService = value.set('utilities', value.get('utilities').filter(
          i => i.get('status') === ServiceStatus.ATTIVATO ||
            i.get('status') === ServiceStatus.IN_ATTIVAZIONE ||
            i.get('status') === ServiceStatus.ATTIVO));
        const serviceName = updatedService.get('serviceName');
        return serviceName === 'AMAZON' ? updatedService.set('serviceName', 'Amazon Prime') :
          serviceName === 'TutelaLegale' ? updatedService.set('serviceName', 'Tutela Legale') :
            serviceName === 'TotalSecurity' ? updatedService.set('serviceName', 'Total Security') :
              serviceName === 'SafeCall' ? updatedService.set('serviceName', 'Safe Call') : updatedService;
      }).filter(serviceServizi => serviceServizi.get('utilities') !== null && !serviceServizi.get('utilities').isEmpty() &&
        (serviceServizi.get('serviceName') === 'Amazon Prime' || serviceServizi.get('serviceName') === 'Teleconsulto medico'
          || serviceServizi.get('serviceName') === 'Tutela Legale' || serviceServizi.get('serviceName') === 'Assistenza H24'
          || serviceServizi.get('serviceName') === 'Total Security' || serviceServizi.get('serviceName') === 'Safe Call')).toJS();

      if (this.serviziAggiuntiviServices.length > 0) {
        const serviziUtilities = this.serviziAggiuntiviServices.map(servicesServizi =>
          UserServicesObjects(servicesServizi.serviceName).utilities
        ).reduce((acc, val) => acc.concat(val), []);
        this.userServices.push(UserNewServicesObject());
        this.userServices.find(aggregatedService => aggregatedService.serviceName === 'SERVIZI').utilities.push(...serviziUtilities);
      }
    });
  }

  public redirect(serviceName, utNumber) {
    this.routeService.setRoute(serviceName, utNumber);
  }

  getCardStatus(service) {
    return this.service.getCardStatus(service);
  }

  serviceIcon(icon: string): string {
    return this.optimaIconUtils.getServiceIconByName(icon);
  }

  getName(name) {
    return this.attiviService.getNumberName(name);
  }

  setServiceName(serviceName) {
    switch (serviceName.toLowerCase()) {
      case 'adsl' : {
        return 'INTERNET';
      }
      case 'energia' : {
        return 'LUCE';
      }
      case 'servizi' : {
        return 'SERVIZI AGGIUNTIVI';
      }
      default : {
        return serviceName;
      }
    }
  }

  mobileStatusDecode(status) {
    return this.attiviService.mobileStatusDecode(status);
  }

  onDestroy() {
    ObservableUtils.unsubscribeAll([this.userServicesSubscription]);
  }

  shouldShowEntra(utility) {
    return utility && (utility.status === ServiceStatus.ATTIVATO
        || utility.status === ServiceStatus.IN_ATTIVAZIONE
        || utility.status === ServiceStatus.ATTIVO)
      || allowedMobileStatuses.indexOf(utility.status) > -1;
  }

  ngOnDestroy(): void {
  }
}
