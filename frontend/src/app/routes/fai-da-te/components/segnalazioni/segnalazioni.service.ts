import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { Signal } from './segnalazioni.model';


@Injectable()
export class SegnalazioniService {

  readonly url = 'api/segnalazione';

  constructor(private http: HttpClient) {
  }

  public getSignalData(): Observable<Signal[]> {
    const headers = new HttpHeaders({clientid: localStorage.getItem('clientId')});
    return this.http.get<Signal[]>(this.url, {headers: headers});
  }
}
