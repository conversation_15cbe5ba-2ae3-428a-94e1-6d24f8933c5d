export interface Signal {
  customerid: string;
  IncidentId: string;
  OwningUser: string;
  CaseOriginCode?: null;
  Title: string;
  AccountId: string;
  Description?: null;
  TicketNumber: string;
  IncidentStageCode: number;
  StateCode: number;
  StatusCode: number;
  Opt_triplettaid?: null;
  Opt_casopadreid?: null;
  Opt_CodaId?: null;
  Opt_AnagraficaCaratteristicaCasoIdId?: null;
  Opt_Tipoprodotto: number;
  Opt_Servizio: number;
  Opt_Figlioinorigine: number;
  Opt_EsitoChiusuraId: string;
  Opt_ServizioDescrizione: string;
  Opt_CasoPadreDescrizione?: null;
  Opt_CasoPadreTicketNumber?: null;
  Opt_TipoProdottoDescrizione: string;
  Opt_CaratterizzazioneDescrizione?: null;
  Opt_PropietarioDescrizione: string;
  Opt_OrigineCasoDescrizione: string;
  Opt_EsitoChiusuraDescrizione: string;
  Opt_Datacontrollo?: null;
  Opt_Utenze?: null;
  Opt_Fatture?: null;
  OueueTypeCode?: null;
  PrimaryUserId?: null;
  Opt_link_clienti: string;
  IncidentDetails?: null;
  Activities?: null;
  Opt_Clibloccato?: null;
  Opt_AnaDettaglioGuastoId?: null;
  Opt_dta_email?: null;
  Opt_AnaEsitoNocId?: null;
  CreatedByName: string;
  ModifiedByName: string;
  CreatedOn: string;
  ModifiedOn: string;
  CreatedBy: string;
  ModifiedBy: string;
  DeletionStateCode: number;
  DescrizioneTripletta: string;
}
