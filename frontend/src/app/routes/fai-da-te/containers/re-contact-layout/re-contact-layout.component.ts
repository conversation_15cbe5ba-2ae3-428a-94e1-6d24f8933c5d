import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { select } from '@angular-redux/store';
import { Observable } from 'rxjs/Observable';
import { UserData } from '../../../../common/model/userData.model';
import { ObservableUtils } from '../../../../common/utils/ObservableUtils';
import { Subscription } from 'rxjs/Subscription';
import { FormUtils } from '../../../../common/utils/FormUtils';
import Validator from '../../../../common/utils/Validator';
import {
  contactTimeConfirmWindowExpression,
  contractTimeConfirmWindowActions,
  createContactTimeOptions,
  recontactMessage, recontactRequestReasonOptions,
  timeZoneAnnotationMap
} from '../../config/config';
import { NotificationService } from '../../../../common/services/notification/notification.service';
import { RecontactRequest } from '../../model/RecontactRequest';
import { RecontactService } from '../../services/recontact/recontact.service';
import {IncidentEventService} from "../../../../common/services/incedentEvent/incident-event.service";
import * as moment from "moment";


@Component({
  selector: 'app-re-contact-layout',
  templateUrl: './re-contact-layout.component.html',
  styleUrls: ['./re-contact-layout.component.scss']
})
export class ReContactLayoutComponent implements OnInit, OnDestroy {

  formGroup: FormGroup;

  reasonOptions = Object.keys(recontactRequestReasonOptions);

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;

  contactTimeOptions = createContactTimeOptions();

  confirmWindowActions = contractTimeConfirmWindowActions;

  userInfoSubscription: Subscription;

  userData: UserData;

  activeButton: boolean = null;

  constructor(private formBuilder: FormBuilder, private recontactService: RecontactService,
              private notificationService: NotificationService, private incidentEventService : IncidentEventService) {
  }

  ngOnInit() {
    this.recontactService.checkIncidentEvent().subscribe(value => this.activeButton = value);
    this.formGroup = this.formBuilder.group({
      phoneNumber: [null, [Validators.required, Validators.minLength(8), Validators.maxLength(12), Validator.digits()]],
      contactTime: [null, [Validators.required, Validator.withExpression(contactTimeConfirmWindowExpression())]],
      reason: [null, [Validators.required]],
      message: [null, [Validators.required]],
    });
    this.userInfoSubscription = this.userInfo.subscribe(userData => {
      if (userData) {
        this.userData = userData;
        this.formGroup.controls['phoneNumber'].setValue(userData.phoneNumber);
      }
    });
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.userInfoSubscription]);
  }

  apply() {
    FormUtils.setFormControlsAsTouched(this.formGroup);
    const {formGroup, userData} = this;
    if (formGroup.valid) {
      const form = formGroup.value;
      const time = new Date(parseInt(form.contactTime, 10));
      const timeZoneId = timeZoneAnnotationMap[time.getHours()];
      const request = new RecontactRequest();
      request.customerId = localStorage.getItem('clientId');
      if(userData.firstName!=null && userData.firstName!=""){
        request.firstName = userData.firstName;
        request.lastName = userData.lastName;
      }else if(userData.lastName!=null && userData.lastName!=""){
        request.firstName = userData.lastName;
        request.lastName = userData.lastName;
      }else{
        request.firstName = userData.nameInInvoice;
        request.lastName = userData.nameInInvoice;
      }

      request.campaignId = timeZoneId;
      request.skillSet = {
        id: recontactRequestReasonOptions[form.reason],
        description: form.reason
      };
      request.message = form.message;
      request.phoneNumber = form.phoneNumber;
      this.recontactService.recontactRequest(request).subscribe(
        () => {
          this.notificationService.successMessage(recontactMessage.successRequest(request.phoneNumber), null, 20000);
          this.formGroup.reset();
          setTimeout(() => { window.location.reload(); }, 1000);
        }, () => {
          this.notificationService.errorMessage(recontactMessage.failedRequest);
          this.formGroup.reset();
        }
      );
    }
  }

  resetForm() {
    this.formGroup.reset({phoneNumber: this.userData.phoneNumber, reason: ''});
  }
}
