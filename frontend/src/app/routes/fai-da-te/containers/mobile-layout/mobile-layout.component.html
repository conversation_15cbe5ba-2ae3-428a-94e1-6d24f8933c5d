<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 mobile-layout-border">
<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding">
  <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12">
    <div class="icon icon-main-left col-lg-1 col-md-1 col-sm-6 col-xs-6">
    </div>
    <div class="title-main title-main-left col-lg-1 col-md-1 col-sm-6 col-xs-6">
      MOBILE
    </div>
  </div>
  <div class="col-lg-10 col-md-10 col-sm-12 col-xs-12 row-button-title">
    <button class="button navigation-button col-lg-3 col-md-4 col-sm-5 col-xs-12 row-button"
            [routerLinkActive]="['active']"
            [routerLink]="['/faidate/servizi-attivi/mobile/ricarica', selectedId?selectedId:'']">RICARICA SIM
    </button>
    <button class="button navigation-button col-lg-3 col-md-4 col-sm-5 col-xs-12 row-button"
            [routerLinkActive]="['active']"
            [routerLink]="['/faidate/servizi-attivi/mobile/agguingi', selectedId?selectedId:'']">AGGIUNGI OPZIONI
    </button>
    <button class="button navigation-button col-lg-3 col-md-4 col-sm-5 col-xs-12 row-button"
            [routerLinkActive]="['active']"
            [routerLink]="['/faidate/servizi-attivi/mobile/modifica', selectedId?selectedId:'']">MODIFICA OFFERTA
    </button>
    <button class="button navigation-button col-lg-3 col-md-4 col-sm-5 col-xs-12 row-button"
            [routerLinkActive]="['active']"
            [routerLink]="['/faidate/servizi-attivi/mobile/dettaglio', selectedId?selectedId:'']">DETTAGLIO TRAFFICO
    </button>

    <button class="button navigation-button col-lg-3 col-md-4 col-sm-5 col-xs-12 row-button"
            *ngIf="this.isRichiediPortabilitaVisible"
            [routerLinkActive]="['active']"
            [routerLink]="['/faidate/servizi-attivi/mobile/portabilita', selectedId?selectedId:'']">
      RICHIEDI PORTABILITÀ
    </button>
    <button class="button navigation-button col-lg-3 col-md-4 col-sm-5 col-xs-12 row-button five-g-button"
            *ngIf="this.is5GVisible"
            [routerLinkActive]="['active-5g']"
            [routerLink]="['/faidate/servizi-attivi/mobile/5G', selectedId?selectedId:'']">
      5G DI OPTIMA
    </button>
  </div>
</div>

<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding service-layout">
  <router-outlet></router-outlet>
</div>
<div class="col-lg-8 col-md-8 col-sm-8 col-xs-12 no-padding position-button-mobile">
  <button class="button" routerLink="/faidate/servizi-attivi">VAI ALL’ELENCO DEI TUOI SERVIZI</button>
</div>
</div>
