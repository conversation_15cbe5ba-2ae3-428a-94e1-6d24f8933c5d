@import "~app/shared/styles/colors";

.mobile-layout {
  border: 1px solid $menu-border;
  border-radius: 5px;
  min-height: 34vh;
  color: #36749d;
  background: rgba(180, 203, 225, 0.2);
}
.mobile-layout-border{
  border: 1px solid $menu-border;
  border-radius: 10px;
  min-height: 10vh;
  color: #36749d;
  padding-bottom: 2%;
  padding-top: 2%;
}
.mobile-layout-buttons {
  border: 1px solid $menu-border;
  border-radius: 5px;
  min-height: 10vh;
  color: #36749d;
  background: rgba(180, 203, 225, 0.2);
}
.live-amount {
  font-size: 19px;
  font-weight: bold;
  display: block
}
.icon {
    position: absolute;
    background: url("../../../../../assets/img/optima/Set_Icone_AreaClienti_SIM.png") no-repeat;
    width: 100px;
    background-size: contain;
    top: -10px;
    left: 26px;
  }

.icon-title{
  position: absolute;
    background: url("../../../../../assets/img/optima/Set_Icone_AreaClienti_ItuoiServizi.png") no-repeat;
    width: 100px;
    background-size: contain;
    top: -10px;
    left: 26px;
}
.title-main {
  clear: both;
  text-align: right;
  font-size: 22pt;
  font-weight: bold;
  color: #36749d;
  padding-top: 22px;
  padding-right: 35px;
}
.button{
  margin: 5px 5px 5px 5px;
}
.titleBlock {
  clear: both;
  text-align: center;
  font-size: 16pt;
  font-weight:bold;
  .title {
    color: #36749d;
  }
  .icon {
    position: absolute;
    background: url("../../../../../assets/img/optima/Set_Icone_AreaClienti_SIM.png") no-repeat;
    width: 80px;
    background-size: contain;
    top: -10px;
    left: 10px;
  }
  margin-bottom: 20px;
  border: 2px solid #b6cce3;
  border-radius: 5px;
  padding: 15px;
}
.icon {
  width: 100px;
  height: 71px;
  margin: 18px auto 0 auto;
  left: 38px;

}
.text-left{
  font-size: 13pt;
  color: #36749d;
  font-weight:bold;
  padding: 10px;
}
.text-select{
  font-size: 12pt;
  color: #36749d;
  font-weight:bold;
  margin-top: 10px;
}
.text-left-value{
  font-size: 13pt;
  color: #36749d;
}
.button {
  text-align: center;
  border: 1px solid $dark-blue;
  color: $dark-blue;
  padding: 7px 15px;
  border-radius: 10px;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: white;
}
.ricarica{
  width: 100%;
}
.chart-header {
  text-align: center;
}
.mobile-title {
  display: flex;
  align-items: center;
  margin-top: 2%;

  .icon-mobile {
    height: 70px;
    width: 70px;
  }

  .layout-label {
    color: $dark-blue;
    font-weight: bold;
    font-size: 22px;
  }
}

.header-mobile-block {
  margin-bottom: 35px;
  .ricarica-btn {
    margin: auto;
    width: 100%;
  }
}

.credito-block {
  width: 100%;
  .credito-text {
    padding-top: 3px;
    padding-bottom: 3px;
  }
  .credito-text-value {
    font-size: 20pt;
  }
}
.tariffario-btn {
  width: 100%;
}
.mobile-navigation, .navigation-group-inner {
  display: flex;
}

.service-layout {
  //border-bottom: 1px solid $menu-border;
  background-color: $menu-background;
  border: 1px solid #B0C7DD;
  border-radius: 10px;
  margin-bottom: 1%;
  margin-top: 2%;
  min-height: 37vh;
}

.navigation-group {
  padding: 0;
  margin-top: 2%;
  //min-height: 70px;
  display: flex;
  align-items: center;

  .navigation-button {
    margin-left: 1%;

    & :last-child {
      margin-right: 0;
    }
  }
}

.active {
  background-color: $dark-blue;
  color: #ffffff
}

.five-g-button {
  border-color: #9B1838;
  color: #9B1838;
}

.active-5g {
  color: white;
  background: transparent linear-gradient(270deg, #A0183A 0%, #831430 64%, #73112A 100%) 0 0;
}

.layout-footer {
  text-align: center;
  margin: 2% auto;
}
.white-layout {
  background-color:white;
}

@media only screen and (max-width: 1723px) {
  .navigation-group {
    .navigation-button {
      margin-top: 1%;
    }
  }
}

@media only screen and (max-width: 1305px) {
  .button {
    padding: 7px 7px;
  }
  .mobile-title {
    .layout-label {
      padding-left: 0;
    }
  }
}

@media only screen and (max-width: 1187px) {
  .button {
    font-size: 13px;
  }
  .mobile-title {
    padding-left: 0;
  }
  .mobile-title {
    .icon-mobile {
      height: 60px;
    }
  }
}

@media only screen and (max-width: 1120px) {
  .button {
    font-size: 13px;
  }
  .mobile-title {
    .icon-mobile {
      height: 55px;
    }

    .layout-label {
      padding-left: 0;
    }
  }
}
@media only screen and (max-width: 1550px) {
  .title-main {
    font-size: 19pt;
    font-weight: bold;
    color: #36749d;
    padding-top: 50px;
    padding-right: 16px;
  }
  .icon {
    width: 60px;
    height: 75px;
    margin: 40px auto 0 auto;
    left: 53px;
  }
}
@media only screen and (max-width: 1400px) {
  .icon {
    width: 60px;
    height: 75px;
    margin-top: 40px;
    left: 18px;
  }
}
@media only screen and (max-width: 1250px) {
  .title-main {
    font-size: 16pt;
    font-weight: bold;
    color: #36749d;
    padding-top: 30px;
    padding-right: 21px;
  }
  .icon {
    width: 45px;
    height: 71px;
    margin: 24px auto 0 auto;
    left: 23px;
  }
}

@media only screen and (max-width: 1100px) {
  .title-main {
    font-size: 14pt;
    font-weight: bold;
    color: #36749d;
    padding-top: 32px;
    padding-right: 16px;
  }
  .icon {
    width: 37px;
    height: 67px;
    margin: 30px auto 0 auto;
    left: 23px;
  }
}
@media only screen and (max-width: 1150px) {
 .button {
   font-size: 11px;
 }
}
@media only screen and (max-width: 991px) {
  .title-main {
    font-size: 17pt;
    font-weight: bold;
    color: #36749d;
    padding-top: 23px;
    padding-right: 406px;
  }
  .button {
    font-size: 14px;
  }
  .icon {
    width: 39px;
    height: 73px;
    margin: 15px auto 0 auto;
    left: 23px;
  }
}
@media only screen and (max-width: 1070px) {
  .mobile-title {
    padding-left: 0;
  }
}

@media only screen and (max-width: 991px) {
  .header-mobile-block {
    margin-bottom: 35px;
    .ricarica-btn {
      margin: 10px;
      width: 100%;
      text-align: center;
      margin-left: auto;
    }
  }
  .ricarica-btn {
    margin: auto;
    width: 100%;
    margin-top: 10px;
  }
  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .mobile-layout {
    color: #36749d;
    background: rgba(180, 203, 225, 0.2);
  }
  .mobile-title {
    padding-left: 15px;
  }
}
@media (min-width: 1200px){
  //     display: flex;
  //     justify-content: space-around;

  .ricarica-btn{
    width: 100%;
  }
  .title-main {
    font-size: 14pt;
    font-weight: bold;
    color: #36749d;
    padding-top: 32px;
    padding-right: 16px;
  }
  .icon {
    width: 37px;
    height: 67px;
    margin: 30px auto 0 auto;
    left: 23px;
  }
}
@media screen and (max-width: 815px) {
  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .button {
    font-size: 12px;
    margin: 12px;
    margin-left: auto;
  }
}

@media screen and (max-width: 767px) {

  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .mobile-layout {
    padding: 0;
    color: #36749d;
    background: rgba(180, 203, 225, 0.2);
  }
  .mobile-navigation, .navigation-group {
    display: block;
  }
  .mobile-title {
    background-color: #ffffff;
    margin-top: 0;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    padding: 10px 10px;
    border-bottom: 1px solid $menu-border;
  }
  .service-layout, .navigation-group,
  .layout-footer {
    background-color: #ffffff;
  }
  .navigation-group {
    padding-left: 15px;
    padding-right: 15px;

    .navigation-button {
      margin: 2% 0 0 0;
      width: 49.5%;
      font-size: 14px;
    }
  }
  .service-layout {
    margin-left: 0;
    margin-right: 0;
  }
}

@media screen and (max-width: 435px) {
  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .navigation-group {
    .navigation-button {
      font-size: 9px;
      width: 49%;
      padding: 7px 5px;
    }
  }
}
.tariff-details-control {
  .service-buttons-group {
    padding: 0;
    .service-button {
      border: 1px solid #36749d;
      width: 90px;
      height: 35px;
      background-color: white;
      border-radius: 5px;
      color: #36749d;
      margin-top: 15%;
      &.active {
        background-color: #36749d;
        color: #ffffff
      }
    }
  }
}
.no-padding {
  padding-right: 0 !important;
}
.chart-block {
  position: relative;
  width: 160px;
  height: 150px;
  float: inherit;
  .fa-caret-left, .fa-caret-right {
    font-size: 26px;
    line-height: 95px;
    cursor: pointer;
  }
  .fa-phone {
    margin-top: 100px;
    display: block;
    margin-right: 5px;
    font-size: 30px;
  }
  .fa-envelope {
    margin-top: 100px;
    display: block;
    margin-right: 5px;
    font-size: 27px;
  }
  .fa-arrow-reverse {
    margin-top: 43px;
    width: 40px;
    margin-left: -5px;
  }
  // .chart-title {
  //     font-size: 7px;
  //     margin-left: -10px;
  //     width: 50%;
  //     top: 5px;
  //   }

}
.chart-label {
  position: absolute;
  font-size: 14px;
  color: #36749d;
  text-align: center;
  top: 22%;
  left: 17%;
  width: 115px;
  .live-amount {
    font-size: 19px;
    font-weight: bold;
  }
}
.summary-sim-block {
  margin-top: 1%;
  .offer {
    margin-bottom: 5px;
  }
  .mobile-summary-table {
    border: 1px solid #b6cce3;
    border-radius: 5px;
    color: #36749d;
    background: rgba(180, 203, 225, 0.2);
  }
  .border-info {
    font-size: 18px;
    color: #36749d;
    border-bottom: 1px solid rgb(180, 203, 225);
    background: rgba(180, 203, 225, 0.2);
    .info-title {
      font-weight: bold;
      margin-right: 2%;
    }

  }
  .table-title {
    font-size: 18px;
    font-weight: bold;
    color: #36749d;
    margin-bottom: 10px;
  }
  .table-column {
    padding-left: 25px;
    border-right: 1px solid rgb(180, 203, 225);
    margin-left: 5px;
    &:last-child {
      border-right: none;
    }
  }
  &:last-child {
    border-right: none;
  }
  .info {
    font-size: 12px;
  }
  .summary-table {
    margin-top: 2%;
    margin-bottom: 2%;
    border: 1px solid rgb(180, 203, 225);
  }
}
@media only screen and (max-width: 1050px) {
  .chart-block {
    margin: 0;
  }
  .navigation-block {
    > a {
      display: inline-block;
    }
  }
  .service-buttons-group {
    margin-right: 8%;
  }
  .remaining-credit-title {
    font-size: 18px;
  }
  .remaining-credit {
    font-size: 22px;
  }
}

@media only screen and (max-width: 500px) and (max-device-width: 500px) {

  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .tariff-details-control .service-buttons-group .service-button {
    margin-top: 0;
    margin-left: 1%;
  }
  .block {
    height: 42vh;
  }
  .sim-details {
    height: 42vh;
  }
}

@media only screen and (max-width: 370px) {
  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .titleBlock {
    visibility: hidden;
    position: absolute;
  }
}

@media only screen and (min-width: 321px) and (max-width: 369px) {
  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .tariff-details-control {
    padding-left: 0;
    padding-right: 20px;
    .chart-block {
      margin-right: 0;
      margin-bottom: 20px;
      //left: 20%;
    }
  }
}

@media only screen and (max-width: 800px) and (max-device-width: 800px) {

  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .sim-details {
    margin-bottom: 10px;
    &.active {
      border-bottom-right-radius: 5px;
      border-top-right-radius: 5px;
    }
  }
  .chart-block {
    margin-right: 21%;
  }
  .block {
    height: auto;
  }
  .remaining-credit-title {
    font-size: 20px;
  }
  .remaining-credit {
    font-size: 24px;
  }
}

@media only screen and (max-width: 400px) and (max-device-width: 400px) {
  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .display-flex {
    display: block;
  }
}

/*@media only screen and (max-width: 400px) and (max-device-width: 400px) {
  .display-flex {
    display: block;
    .table-column {
      border-top: 1px solid rgb(180,203,225);
    }
    .table-column:first-child {
      border-top: none;
    }
  }
}*/
@media only screen and (max-width: 370px) {
  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .summary-sim-block {
    .mobile-summary-table {
      background: none;
    }
    .summary-table {
      border: none;
      .table-column {
        margin-top: 1%;
        margin-left: 0;
        border: 1px solid rgba(180, 203, 225, 0.3);
        .offer {
          padding: 0;
        }
        .table-title {
          font-size: 13px;
          margin-bottom: 0;
        }
        .tariff-plan {
          font-size: 14px;
        }
        .offer {
          font-size: 14px
        }
      }
    }
    border: none;
    .border-info {
      font-size: 14px;
    }
  }
  .remaining-credit-block {
    height: 50px;
    margin-top: 20px;
    .remaining-credit-title {
      font-size: 12px;
      margin-top: 15px;
    }
    .remaining-credit {
      font-size: 15px;
      margin-top: 12px;
    }
  }
  .piano-tariffario {
    max-width: 100%;
  }
  .tariff-details-control {
    display: inline-grid;
    justify-content: center;
    .chart-block {
      margin: 0 0 12px;
    }
  }

}

@media only screen and (min-width: 370px) and (max-width: 476px) {

  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .chart-block {
    position: relative;
    width: 87px;
    height: 91px;
    margin-right: 5px;

    .fa-arrow-reverse {
      margin-top: 57px;
      width: 25px;
      position: absolute;
      right: 73px;
    }
    .fa-envelope {
      margin-top: 60px;
      font-size: 19px;
      margin-left: -50px;
    }
    .fa-phone {
      font-size: 24px;
      margin-left: -45px;
      margin-top: 58px;
      display: block;
      float: left;
    }
    .fa-caret-right {
      position: absolute;
      font-size: 18px;
      margin-top: -16px;
      float: left;
      margin-left: -1px;
    }

    .fa-caret-left {
      position: absolute;
      font-size: 18px;
      margin-top: -16px;
      float: left;
      margin-left: -6px;
    }
  }

  .chart-title {
    font-size: 7px;
    margin-left: -10px;
    width: 50%;
    top: 5px;
  }

  .chart-label {
    font-size: 17px;
    font-weight: bold;
    .live-amount {
      font-size: 12px;
      display: block;
    }
  }

  .remaining-credit-block {
    height: 50px;
    max-width: 100%;
    margin-top: 10%;
    .remaining-credit-title {
      font-size: 15px;
      margin-top: 12px;
    }
    .remaining-credit {
      font-size: 17px;
      margin-top: 12px;
    }
  }

  .piano-tariffario {
    max-width: 100%;
  }

  .traffic-details-title {
    text-align: center;
  }
  .tariff-details-control {
    text-align: center;
    display: flex;
    justify-content: space-around;
  }
  .available-traffic-details-title {
    text-align: center;
  }
  .block-flex {
    overflow-y: visible;
  }

  .titleBlock {
    visibility: hidden;
    position: absolute;
  }
  .plainBlock {
    font-size: 12px;
    border-bottom: 1px solid rgba(180, 203, 225, 0.3);
  }
  .detail {
    border: none;
  }
  .summary-sim-block {
    .mobile-summary-table {
      background: none;
    }
    .summary-table {
      border: none;
      .table-column {
        margin-top: 1%;
        margin-left: 0;
        border: 1px solid rgba(180, 203, 225, 0.3);
        .offer {
          padding: 0;
        }
        .table-title {
          font-size: 13px;
          margin-bottom: 0;
        }
      }
    }
    border: none;

    .border-info {
      font-size: 14px;
      // display: flex;
    }
  }

  .display-flex {
    display: inline-table;
    font-size: 10px;
    .table-column {
      border: 1px solid rgba(180, 203, 225, 0.3);
    }
  }
  .table-title {
    font-size: 14px;
  }
  .tariff-plan {
    font-size: 14px;
  }
  .offer {
    font-size: 14px;
  }

  .selectedRecord {
    overflow: hidden;
  }
  .mobile-layout {
    float: left;
    width: 100%;
  }
  .plainBlock {
    padding-left: 15px;
    padding-right: 15px;
  }

}

@media only screen and (min-width: 477px) and (max-width: 767px) {

  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .chart-block {
    width: 123px;
    height: 139px;
    margin-right: 5px;
    .chart-label {
      position: absolute;
      font-size: 16px;
      display: block;
      color: #36749d;
      text-align: center;
      top: 22%;
      left: 7%;
      width: 115px;
      .live-amount {
        font-size: 17px;
        display: block;
      }
    }
    .fa-arrow-reverse {
      margin-top: 82px;
      width: 40px;
      left: 39px;
      position: absolute;
    }
    .fa-envelope {
      margin-top: 91px;
      display: block;
      margin-right: 5px;
      font-size: 27px;
    }
    .fa-phone {
      margin-top: 91px;
      display: block;
      right: 40px;
      font-size: 32px;
      position: absolute;
    }
  }
  .tariff-details-control {
    text-align: center;
    display: flex;
    justify-content: space-around;
  }
  .remaining-credit-block {
    margin-top: 10%;
  }
  .block-flex {
    overflow-y: visible;
  }

  .piano-tariffario {
    max-width: 100%;
  }

  .titleBlock {
    visibility: hidden;
    position: absolute;
  }
  .plainBlock {
    font-size: 12px;
    border-bottom: 1px solid rgba(180, 203, 225, 0.3);
  }
  .detail {
    border: none;
  }
  .summary-sim-block {
    .mobile-summary-table {
      background: none;
    }
    .summary-table {
      border: none;
      .table-column {
        margin-top: 1%;
        margin-left: 0;
        .offer {
          padding: 0;
        }
      }
    }
    border: none;

    .border-info {
      font-size: 14px;
      // display: flex;
    }
  }
  .display-flex {
    display: inline-table;
    font-size: 10px;
    .table-column {
      border: 1px solid rgba(180, 203, 225, 0.3);
    }
  }
  .table-title {
    font-size: 14px;
  }
  .tariff-plan {
    font-size: 14px;
  }
  .offer {
    font-size: 14px;
  }
  .selectedRecord {
    overflow: hidden;
  }
  .mobile-layout {
    float: left;
    width: 100%;
  }
  .plainBlock {
    padding-left: 15px;
    padding-right: 15px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .tariff-plan {
    font-size: 14px;
  }
  .offer {
    font-size: 14px
  }
  .chart-block {
    position: relative;
    width: 193px;
    height: 180px;
    margin-right: 5px;

    .fa-arrow-reverse {
      margin-top: 114px;
      width: 41px;
      position: absolute;
      left: 51px;
    }
    .fa-envelope {
      margin-top: 125px;
      font-size: 29px;
      margin-left: 22px;
    }
    .fa-phone {
      font-size: 36px;
      margin-left: 54px;
      margin-top: 121px;
      position: absolute;
    }
  }

  .block-flex {
    height: 250px;
  }

  .chart-title {
    font-size: 14px;
    margin-left: 10px;
  }
  .chart-label {
    font-size: 17px;
    font-weight: bold;
    .live-amount {
      font-size: 23px;
      display: block;
    }
  }
  .remaining-credit-block {
    height: 50px;
    max-width: 100%;
    margin-top: 20px;
  }

  .piano-tariffario {
    max-width: 100%;
  }

  .traffic-details-title {
    text-align: center;
  }
  .tariff-details-control {
    text-align: center;
    display: flex;
    justify-content: space-around;
  }
  .available-traffic-details-title {
    text-align: center;
  }
  .fa-caret-right {
    position: absolute;
    bottom: -30px;
    left: 122px;
  }

  .fa-caret-left {
    position: absolute;
    top: 13px;
    right: 3px;
  }
  .selectedRecord {
    display: contents;
  }
  .remaining-credit-block {
    margin-top: 10%;
  }
  .block-flex {
    overflow-y: visible;
  }

  .titleBlock {
    visibility: hidden;
    position: absolute;
  }

  .plainBlock {
    font-size: 12px;
    border-bottom: 1px solid rgba(180, 203, 225, 0.3);
  }
  .detail {
    border: none;
  }
  .summary-sim-block {
    .mobile-summary-table {
      background: none;
      border: none;
      margin-top: 2%;
    }
    .summary-table {
      border: none;
      background: none;
      padding: 0;
      .table-column {
        margin-top: 1%;
        margin-left: 0;
        .offer {
          padding: 0;
        }
      }
    }
    border: none;

    .border-info {
      font-size: 14px;
      //display: flex;
    }
  }

  .display-flex {
    display: block;
    font-size: 10px;
    .table-column {
      border: 1px solid rgba(180, 203, 225, 0.3);
    }
  }
  .table-title {
    font-size: 14px;
  }

  .selectedRecord {
    overflow: hidden;
  }
  .mobile-layout {
    float: left;
    width: 100%;
  }
  .plainBlock {
    padding-left: 15px;
    padding-right: 15px;
  }
  .sim-details {
    width: 100%;
  }
  .row:before {
    content: none;
  }
  .row:after {
    content: none;
  }

}

@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .tariff-details-control {
    display: flex;
    justify-content: space-around;
    text-align: center;
    left: 26%;
  }

}
@media only screen and (min-width: 1601px) and (max-width: 2500px) {
  .tariff-details-control {
    display: flex;
    justify-content: space-around;
    text-align: center;
    left: 28%;
  }
  .mobile-layout {
    border: 1px solid $menu-border;
    border-radius: 5px;
    min-height: 35vh;
    height: 38vh;
    color: #36749d;
    background: rgba(180, 203, 225, 0.2);
  }
  .white-layout {
    background-color:white;
    left: 10px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .mobile-layout {
    border: 1px solid $menu-border;
    border-radius: 5px;
    min-height: 25vh;
    color: #36749d;
    background: rgba(180, 203, 225, 0.2);
  }
  .text-left{
    font-size: 11pt;
    color: #36749d;
    font-weight:bold;

  }
  .text-left-value{
    font-size: 11pt;
    color: #36749d;

  }

  .white-layout {
    background-color:white;
    left: 5px;
  }
  .service-details {
    width: 50%;
  }
  .hidden-xs {
    width: 50%;
  }
  .col-md-12 {
    width: 100%;
  }
  .chart-block {
    position: relative;
    width: 83px;
    height: 90px;
    float: inherit;
    .fa-arrow-reverse {
      margin-top: 60px;
      width: 26px;
      position: absolute;
      left: 27px;
    }
    .fa-envelope {
      margin-top: 64px;
      display: block;
      margin-right: 2px;
      font-size: 19px;
    }
    .fa-phone {
      font-size: 23px;
      position: absolute;
      margin-top: 62px;
      margin-left: 28px;
    }
    .fa-caret-right {
      font-size: 19px;
      line-height: 76px;
      cursor: pointer;
      position: absolute;
    }
    .fa-caret-left {
      font-size: 19px;
      line-height: 77px;
      cursor: pointer;
      position: absolute;
    }
  }
  .chart-label {
    position: absolute;
    font-size: 9px;
    color: #36749d;
    text-align: center;
    top: 17%;
    left: 8%;
    width: 74px;
    .live-amount {
      font-size: 11px;
      display:block;
    }
  }
  .chart-title {
    top: 7pt;
  }

  .tariff-details-control {
    display: flex;
    justify-content: space-around;
    text-align: center;
    left: 25%;
  }

  .remaining-credit-block {
    margin-top: 7%;
    border: 1px solid #36749d;
    background-color: white;
    max-width: 100%;
    height: 50px;
    border-radius: 5px;
    .remaining-credit {
      font-size: 17px;
      color: #36749d;
      float: left;
      height: 100%;
      width: 32%;
      margin-top: 7.5px;
    }
    .remaining-credit-title {
      font-size: 17px;
      float: left;
      height: 100%;
      width: 68%;
      color: #36749d;
      margin-top: 7px;
    }

  }

  .piano-tariffario {
    max-width: 100%;
  }

  .available-traffic-details-title {
    font-size: 17px;
  }
  .sim-details {
    width: 100%;
  }

}

@media only screen and (min-width: 1200px) and (max-width: 1730px) {
  .chart-block {
    position: relative;
    width: 150px;
    height: 150px;
    float: inherit;

    .chart-title {
      margin-left: 2px;
      font-size: 11px;
      width: 62%;
      top: 4px;
      .live-amount {
        font-size: 26px;
        font-weight: bold;
        display: block;
      }
    }
    .fa-arrow-reverse {
      top: 99px;
      width: 36px;
      position: absolute;
      right: 46px;
      margin: auto;
    }
    .fa-envelope {
      top: 106px;
      right: 52px;
      font-size: 23px;
      position: absolute;
      margin: auto;
    }
    .fa-phone {
      margin: auto;
      top: 101px;
      right: 51px;
      font-size: 30px;
      position: absolute;
    }
    .fa-caret-right {
      font-size: 23px;
      line-height: 100px;
      cursor: pointer;
      margin-left: 18px;
    }
    .fa-caret-left {
      font-size: 23px;
      line-height: 100px;
      cursor: pointer;
      margin-left: -18px;
    }
  }

  .tariff-details-control {
    display: flex;
    justify-content: space-around;
  }

  .remaining-credit-block {
    margin-top: 7%;
    border: 1px solid #36749d;
    background-color: white;
    max-width: 100%;
    height: 50px;
    border-radius: 5px;
  }

  .block-flex {
    overflow-y: visible;
  }

  .piano-tariffario {
    max-width: 100%;
  }

}

@media only screen and (max-width: 320px) and (max-device-width: 320px) {
  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .chart-block {
    position: relative;
    width: 87px;
    height: 91px;
    margin-right: 0;
  }
}

@media only screen and (max-width: 768px) {
  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .remaining-credit-block {
    height: 50px;
    max-width: 100%;
  }
  .piano-tariffario{
    max-width: 100%;
  }
  .service-details {
    padding: 0;
  }
  .summary-sim-block {
    .mobile-summary-table {
      border: none;
      padding: 0;
    }
  }
  .record-row {
    &.active {
      background-color: #f0f5f9;
    }
  }
  .detail {
    border-radius: 5px;
  }
  .sim-details {
    border: none;
    overflow-y: auto;
    height: 100%;
  }
  .available-traffic-details-title {
    margin: 0;
  }
  .traffic-details-title {
    line-height: 25px;
  }
  .service-button {
    margin-bottom: 10px;
  }

}

@media only screen and (max-width: 400px) {
  .summary-sim-block {
    .table-column {
      border: none;
    }
  }
  .align-center-xs-sm{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .header-mobile-block {
    margin-bottom: 35px;
    .ricarica-btn {
      margin: 10px;
      width: 100%;
      text-align: center;
      margin-left: auto;
    }
  }
  .ricarica-btn {
    margin: auto;
    width: 100%;
    margin-top: 10px;
  }
}

@media only screen and (min-width: 768px) {
  .selectedRecord.sim-details {
    border-right: 0;
  }
  .service-details {
    padding-right: 0;
    padding-left: 0;
  }
}
@media only screen and (min-device-width: 768px) and (max-device-width: 990px) {
  .row-button-title{
    left: 7%;
  }
}
@media only screen and (min-device-width: 1438px) and (max-device-width: 2000px) {
  .row-button{
    width: 23%;
  }
  .icon-main-left{
    left: 15px;
    height: 50px;
  }
  .title-main-left{
    left: 50px;
  }
}
@media only screen and (min-device-width: 1217px) and (max-device-width: 1437px) {
  .row-button{
    width: 28%;
  }
  .title-main-left{
    left: 41px;
    top: 23px
  }
  .icon{
    width: 54px;
    height: 68px;
    margin: 41px auto 0 auto;
    left: 4px;
  }
}
@media only screen and (min-device-width: 1000px) and (max-device-width: 1216px) {
  .row-button{
    width: 31%;
  }
  .icon{
    width: 45px;
    height: 71px;
    margin: 39px auto 0 auto;
    left: 1px;
  }
  .title-main {
    font-size: 16pt;
    font-weight: bold;
    color: #36749d;
    padding-top: 45px;
    padding-left: 34px;
    padding-right: 29px;
  }
}
@media only screen and (max-width: 991px) {
  .title-main {
    font-size: 17pt;
    font-weight: bold;
    color: #36749d;
    padding-top: 43px;
    padding-right: 406px;
    left: 50%;
    margin-right: -50%;
  }
  .icon {
    width: 66%;
    left: 21%;
    margin-top: 21px;
    width: -2%;
    margin-bottom: 4px;
  }
}
@media only screen and (max-width: 700px){
  .position-button-mobile{
    width: 100%;
    text-align: center;
  }
}
