import {Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {MobileActions} from '../../../../redux/mobile/actions';
import {ContractRecord} from '../../../../common/model/mobile/contract-record/ContractRecord';
import {MobileService} from '../../../../common/services/mobile/mobile.service';
import {ServiziAttiviService} from '../../../../common/services/servizi-attivi/servizi-attivi.service';
import {ActiveServiceStatus} from '../../../../common/enum/ServiceStatus';
import {Subscription} from 'rxjs/Subscription';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {MobileState} from '../../../../redux/model/MobileState';
import MainProduct from '../../../../common/model/mobile/contract-record/MainProduct';
import Product from '../../../../common/model/mobile/contract-record/Product';
import AdditionalProduct from '../../../../common/model/mobile/contract-record/AdditionalProduct';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';
import {NormalizeUtils} from '../../../../common/utils/NormalizeUtils';
import SubscriptionDetails from '../../../../common/model/mobile/product-record/SubscriptionDetails';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {HttpClient} from '@angular/common/http';
import {OffersInAppService} from "../../../offers-in-apps/service/offers-in-app.service";

@Component({
  selector: 'app-mobile-layout',
  templateUrl: './mobile-layout.component.html',
  styleUrls: ['./mobile-layout.component.scss']
})
export class MobileLayoutComponent implements OnInit, OnDestroy {

  @select(['mobile'])
  mobileState: Observable<MobileState>;

  @select(['mobile', 'contractRecords'])
  contractRec: Observable<Array<ContractRecord>>;
  formGroup: FormGroup;
  validityPeriods = {};
  disabledCostoRinnovo = {};
  isRichiediPortabilitaVisible = false;
  productOptionMap: object;
  contractRecords: Array<ContractRecord> = [];
  selectedRecord: ContractRecord;
  simDetailRecords: any;
  activeProductsMap: object;
  active: string;
  mobileStateSubscription: Subscription;
  selectedId: any;
  is5GVisible: boolean;

  constructor(private mobileService: MobileService, private attivService: ServiziAttiviService, protected http: HttpClient,
              private mobileActions: MobileActions, private route: ActivatedRoute, protected fb: FormBuilder,
              private readonly offersInAppService: OffersInAppService) {
    this.mobileStateSubscription = this.mobileState.subscribe(state => {
      const {contractRecords, contractRecordsLoaded} = state;
      if (contractRecordsLoaded && contractRecords) {
        this.contractRecords = contractRecords.filter(r => ActiveServiceStatus[this.mobileStatusDecode(r)
          .toUpperCase()]);
        this.route.children[0].params.subscribe(params => {
          this.selectedId = params.id;
        });
        this.activeProductsMap = this.buildActiveProductsMap(contractRecords);
        this.selectedRecord = this.contractRecords.find(contractRecord => contractRecord.msisdnId === +this.selectedId);
        this.showDetails(this.selectedRecord);
      }
    });

    mobileActions.loadContractRecordsIfNotExist(localStorage.getItem('clientId'));

    this.formGroup = this.fb.group({
      msisdnId: [null, [Validators.required]]
    });
    this.formGroup.controls.msisdnId.valueChanges.flatMap((input: string) => {
      if (input && this.contractRec) {
        const msisdnId = parseInt(input, 10);
        return this.contractRec.map((items) => items.find(item => item.msisdnId === msisdnId));
      }
      return Observable.empty();
    })
      .subscribe((selectedRecord: ContractRecord) => {
        this.selectedRecord = selectedRecord;
        this.showDetails(selectedRecord, this.selectedId);
      });
    this.offersInAppService.getOffer5GData(localStorage.getItem('clientId')).subscribe(response => {
      this.is5GVisible = response.sim.some(sim => (sim.addOnAviable && sim.addOnAviable.length > 0) ||
        (sim.addOnActive && sim.addOnActive.length > 0));
    });
  }

  showDetails(contractRecord, index?: number) {
    if (index) {
      this.selectedRecord = null;
      this.selectedRecord = contractRecord;
    }
    const activeProduct = this.activeProductsMap[contractRecord.msisdnId];
    this.productOptionMap = NormalizeUtils.groupByExpression(activeProduct.productOptions,
      item => item.option.counter.name);
    this.mobileService.loadSimDetailRecord(contractRecord.msisdnId).subscribe((response: Array<SubscriptionDetails>) => {
      this.simDetailRecords = NormalizeUtils.normalizeListWithExprssion(response, item => item.groupName);
      for (const groupName in this.simDetailRecords) {
        if (this.simDetailRecords.hasOwnProperty(groupName)) {
          const group = this.simDetailRecords[groupName];
          this.simDetailRecords[groupName] = NormalizeUtils.normalizeListWithExprssion(group, item => item.counter.name);
        }
      }
      const {SMS_NATIONAL, VOICE_NATIONAL} = this.simDetailRecords['Nazionale'];
      if (SMS_NATIONAL && SMS_NATIONAL.activations || VOICE_NATIONAL && VOICE_NATIONAL.activations) {
        this.isRichiediPortabilitaVisible = true;
      }
    });
  }

  ngOnInit() {

  }

  mobileStatusDecode(record) {
    if (record && record.lastStatusChange && record.lastStatusChange.status) {
      return this.attivService.mobileStatusDecode(record.lastStatusChange.status.name);
    }
  }

  buildActiveProductsMap(contractRecord: ContractRecord[]) {
    const result = {};
    if (contractRecord) {
      contractRecord.forEach(item => {
        result[item.msisdnId] = this.getActiveProduct(item);
        if (result[item.msisdnId]['prId']) {
          this.mobileService.checkPeriod(localStorage.getItem('clientId'), result[item.msisdnId]['prId']).subscribe(period => {
            if (period !== null && period.text !== '') {
              this.validityPeriods[item.msisdnId] = period.text;
            }
            (period !== null && period.text !== null && item.descrizioneCanaleAcquisto === 'Canale Ag. Dirette')
              ? this.disabledCostoRinnovo[item.msisdnId] = false
              : this.disabledCostoRinnovo[item.msisdnId] = true;
          });
        }
      });
    }
    return result;
  }

  getActiveProduct(selectedRecord: ContractRecord): MainProduct | Product {
    const {mainProduct, additionalProducts} = selectedRecord;
    mainProduct.prId = selectedRecord.id;
    if (mainProduct.productMapping && mainProduct.productMapping.flagSoloDati || !additionalProducts
      || additionalProducts.length === 0) {
      return mainProduct;
    }
    return this.getActiveAdditionalProduct(additionalProducts);
  }

  getActiveAdditionalProduct(additionalProducts: Array<AdditionalProduct>): MainProduct | Product {
    const active = additionalProducts.find(item => item.active && item.product
      && item.product.renewalPeriod >= 1);
    if (active) {
      active.product.prId = active.id;
    }
    return active ? active.product : {} as Product;
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.mobileStateSubscription]);
  }
}
