import {Component, Input} from '@angular/core';

@Component({
  selector: 'app-no-adslrouter',
  templateUrl: './no-adslrouter.component.html',
  styleUrls: ['./no-adslrouter.component.scss']
})
export class NoAdslrouterComponent {
  @Input('username') username: string;
  @Input('password') password: string;

  constructor() {
  }

  title: string;

  @Input('type')
  set type(type: string) {
    this.title = type === 'FTTH' ? 'Connessione PPP over Ethernet sulla porta GbE WAN' :
      'Connessione PPP over Ethernet sulla linea VDSL';
  }
}
