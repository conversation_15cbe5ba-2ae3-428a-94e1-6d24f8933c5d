import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import {PodDetail} from '../../../../../common/model/fisso/PodDetail';

@Component({
  selector: 'app-has-voip-router',
  templateUrl: './has-voip-router.component.html',
  styleUrls: ['./has-voip-router.component.scss']
})
export class HasVoipRouterComponent implements OnInit, OnChanges  {
  @Input('username') username: string;
  @Input('password') password: string;
  @Input('voipData') voipData: Array<PodDetail>;
  constructor() {
  }

  ngOnInit() {
  }
  ngOnChanges(changes: SimpleChanges): void {
  }
}
