import {async, ComponentFixture, TestBed} from '@angular/core/testing';

import {HasVoipRouterComponent} from './has-voip-router.component';

describe('HasVoipRouterComponent', () => {
  let component: HasVoipRouterComponent;
  let fixture: ComponentFixture<HasVoipRouterComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ HasVoipRouterComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HasVoipRouterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
