import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import {PodDetail} from '../../../../../common/model/fisso/PodDetail';

@Component({
  selector: 'app-has-voip-router-fibra',
  templateUrl: './has-voip-router-fibra.component.html',
  styleUrls: ['./has-voip-router-fibra.component.scss']
})
export class HasVoipRouterFibraComponent implements OnInit, OnChanges  {
  @Input('username') username: string;
  @Input('password') password: string;
  @Input('vlanId') vlanId: Array<any>;
  @Input('voipData') voipData: Array<PodDetail>;
  constructor() { }

  ngOnInit() {
  }
  ngOnChanges(changes: SimpleChanges): void {
  }
}
