<form class="numero" *ngIf="allLinee && allLinee.length>1" name="myForm">
  <label for="router">Numero :</label>
  <select name="router" id="router" [(ngModel)]="selectedLinee" (change)="hasVoip()">
    <option *ngFor="let linee of allLinee" [ngValue]="linee">
      {{linee.number ? linee.number : linee.additionalNumber }}
    </option>
  </select>
</form>
<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 numero" *ngIf="allLinee && allLinee.length === 1"><b>Numero:</b>
  {{allLinee[0].number ? allLinee[0].number : allLinee[0].additionalNumber }}</div>
<app-router-container *ngIf="selectedLinee" [selectedLinee]="selectedLinee" [VOIP]="voipData" [vlanId]="vlanId">
</app-router-container>
