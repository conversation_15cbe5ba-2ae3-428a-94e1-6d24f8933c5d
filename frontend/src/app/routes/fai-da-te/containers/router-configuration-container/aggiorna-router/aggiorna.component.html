<div class="content" [formGroup]="formGroup">
  <p>In questa pagina è presente il manuale per procedere ad aggiornamento software del terminale, che ne
    consentirà l’uso su servizi di accesso ad Internet offerti da altri operatori.</p>

  <div class="col-xs-12 col-sm-12 col-md-12 control-block no-padding">
    <label class="label">Seleziona il modello del tuo Router</label>
  </div>
  <div class="col-xs-12 col-sm-6 col-md-4 control-block">
    <select class="app-select document-version" formControlName="documentVersion">
      <option [selected]="formGroup.controls['documentVersion'].value===null" value="">
      </option>
      <option *ngFor="let option of documentOptions"
              [selected]="formGroup.controls['documentVersion'].value===option" [value]="option">{{option}}</option>
    </select>
    <div class="text-danger errors-block"
         *ngIf="formGroup.controls['documentVersion'].hasError('required') && (formGroup.controls['documentVersion'].dirty || formGroup.controls['documentVersion'].touched)">
      Per favore, seleziona il modello del tuo router.
    </div>
  </div>

  <div class="info-block">
    <app-info class="router-info">
      <info-button><i class="info-circle">i</i></info-button>
      <info-message>
        <div class="router-title">Seleziona "Justec" se il tuo router è costi:</div>
        <div class="justec-router"></div>
        <div class="router-title">Seleziona "ADB" se il tuo router è costi:</div>
        <div class="adb-router"></div>
        <div class="router-title">Il modello del tuo router ADB è indicato sotto l'apparato</div>
      </info-message>
    </app-info>
  </div>
  <div *ngIf="formGroup.controls['documentVersion'].value" class="download-buttons">
    <div class="button col-xs-12 col-sm-6 col-md-3 scarica"
         (click)="downloadFile()"><i
      class="icon-pdf-load"></i>
      <span class="text">Scarica la guida</span></div>
    <div *ngIf="documents[formGroup.controls['documentVersion'].value]?.sig"
         class="button col-xs-12 col-sm-6 col-md-3 scarica"
         (click)="downloadSIGFile()">
      <span class="textScarica">Scarica firmware {{formGroup.controls['documentVersion'].value}}</span></div>
  </div>

</div>

