<div class="col-lg-12 col-md-12 page-title-style">
  <div class="title-image safe-call-picture"></div>
  <div class="title-text">SAFE CALL</div>
</div>

<div class="mainBlock">
  <div class="option-block" [formGroup]="formGroup">
    <label for="simList" class="no-margin">SIM associata:</label>
    <select id="simList" class="form-control" formControlName="number" (change)="switchNumber()">
      <option *ngFor="let number of listOfNumbers">
        SIM {{number}}
      </option>
    </select>
  </div>
  <div class="row">
    <div class="col-lg-12">
      <p><b>Stato:</b> {{safeCallInfo?.status}}</p>
    </div>
  </div>
  <div class="row" *ngIf="safeCallInfo?.status === 'Attivo'">
    <div class="col-lg-9">
      <p><b>Data Attivazione:</b> {{safeCallInfo?.additionalInfo.dateActivation | date : 'dd/MM/yyyy'}}</p>
    </div>
  </div>
  <div class="row" *ngIf="safeCallInfo?.status === 'Disattivo'">
    <div class="col-lg-9">
      <p><b>Data disattivazione:</b> {{safeCallInfo?.additionalInfo.dateDeactivation | date : 'dd/MM/yyyy'}}</p>
    </div>
  </div>
  <div class="row" *ngIf="safeCallInfo?.status === 'Attivo'">
    <div class="col-lg-12">
      <p><b>Costo:</b> {{safeCallInfo.additionalInfo.price}} €</p>
    </div>
  </div>
  <div class="row" *ngIf="safeCallInfo?.status === 'Attivo'">
    <div class="col-lg-9">
      <p><b>Data rinnovo:</b> {{safeCallInfo?.additionalInfo.dateRenewal | date : 'dd/MM/yyyy'}}</p>
    </div>
  </div>
</div>

<button routerLink='/faidate/servizi-attivi'>VAI ALL’ELENCO DEI TUOI SERVIZI</button>
