<div class="col-md-12 block-md-12 outBox">
  <div class="row app--header-block">
    <div class=" block-md-3 topBlock head" [ngClass]="[serviceIcon(serviceName), 'service-icon']"></div>
    <div class="serviceName">{{serviceName}}</div>
  </div>
  <div class="mainBlock">
    <div class="row">
      <div class="col-lg-12">
        <p>
          <b>Stato:</b> {{utenza.status}}</p>
        <p>
          <b>{{getName(serviceName)}} </b> {{utenza.utNumber}}</p>
      </div>
    </div>
    <div class="row ">
      <div class="col-lg-9">
        <p *ngIf="utenza.status==='ATTIVATO'">
          <b>Data Attivazione:</b> {{utenza.startDate | date : 'dd/MM/yyyy'}}</p>
        <p *ngIf="utenza.status==='IN_ATTIVAZIONE'">
          <b>Data Prevista Attivazione:</b> {{utenza.firstActivationDate | date : 'dd/MM/yyyy'}}</p>
        <div *ngIf="podDetails && podDetails[utenza.utNumber]">
          <p><b>Tipologia servizio: </b>{{podDetails[utenza.utNumber].tipoCarrier}}</p>
          <div
            *ngIf="podDetails[utenza.utNumber].variazioniLinea && podDetails[utenza.utNumber].variazioniLinea.length>0">
            <p *ngIf="podDetails[utenza.utNumber].variazioniLinea[0].linea"><b>Tipologia linea:
            </b>{{podDetails[utenza.utNumber].variazioniLinea[0].linea.descrizioneTipoLinea}}</p>
            <p *ngIf="podDetails[utenza.utNumber].variazioniLinea[0].sede"><b>Indirizzo fornitura:
            </b>{{podDetails[utenza.utNumber].variazioniLinea[0].sede.descrizioneSede}}</p>
            <p *ngIf="podDetails[utenza.utNumber].variazioniLinea[0].codiceMigrazioneOpt" class="detailRow">
              <b>Codice migrazione: </b>{{podDetails[utenza.utNumber].variazioniLinea[0].codiceMigrazioneOpt}}</p>
            <div class="detailRow" *ngIf="podDetails[utenza.utNumber].opzioni.length > 0"><b>Opzioni:</b>
              <p class="pod-details-option" *ngFor="let option of podDetails[utenza.utNumber].opzioni">-
                {{option.descrizioneOpzione}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="pdf" class="app-drop-down">
      <a mat-button class="dropdown dontShow" [matMenuTriggerFor]="menu" [ngClass]="{ 'disabled' :condition }">
        <div class="icon modifica"></div>
      </a>
      <mat-menu #menu="matMenu" xPosition="before" yPosition="below" [overlapTrigger]="false">
        <div class="mat-menu-style">
        <button mat-menu-item *ngIf="pdf.length===0">
          <span> No PDF </span>
        </button>
<!--        <button class="red" mat-menu-item *ngIf="pdf.length>0">-->
<!--          Variazioni e richieste-->
<!--        </button>-->
        <button mat-menu-item class="menu-button odds-bg" *ngFor="let pdfRow of pdf">
          <span class="icon-pdf-load"> </span>
          <a target='_blank' href="{{pdfRow.link}}">
            {{pdfRow.name}}</a>
        </button>
        </div>
      </mat-menu>
    </div>
  </div>
  <div class="serviziButton">
    <a class="elenco" routerLink='/faidate/servizi-attivi'>VAI ALL’ELENCO DEI TUOI SERVIZI</a>
  </div>
</div>
