import {NgModule} from '@angular/core';
import {OptimaYoungComponent} from './component/optima-young/optima-young.component';
import {RouterModule, Routes} from '@angular/router';
import {SharedModule} from '../../shared/shared.module';
import {CarouselModule} from 'angular-bootstrap-md';
import {OptimaYoungService} from './service/optima-young.service';
import {CommonModule} from '../../common/common.module';
import {OffersInAppService} from '../offers-in-apps/service/offers-in-app.service';

const routes: Routes = [
  {path: '', component: OptimaYoungComponent}
];

@NgModule({
  imports: [
    RouterModule.forChild(routes),
    CommonModule,
    SharedModule,
    CarouselModule
  ],
  declarations: [OptimaYoungComponent],
  exports: [
    RouterModule
  ],
  providers: [OptimaYoungService, OffersInAppService]
})
export class OptimaYoungModule {
}
