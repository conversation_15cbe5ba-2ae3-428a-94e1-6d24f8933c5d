.header {
  background: linear-gradient(270deg, #00D1FF 0%, #00B9E3 100%);
  box-shadow: 0 3px 6px #00000029;
  color: white;
  font-size: 25px;
  height: 55px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-family: Roboto, sans-serif;
}

.client-info {
  background-color: #EFEFEF;
  font-size: 11px;
  color: #606060;
  height: 30px;
  display: flex;
  align-items: center;
  gap: 5px;
  justify-content: flex-start;
  padding-left: 40px;
  margin-bottom: 20px;
}

.invite-banner-container {
  position: relative;
  display: flex;
  justify-content: center;

  .invite-banner {
    width: 85%;
    max-width: 600px;
  }

  .clickable-area-optima-young, .clickable-area-mobile {
    position: absolute;
    width: 30%;
    cursor: pointer;
    left: 35%;
  }

  .clickable-area-optima-young {
    top: 85%;
    height: 8%;
  }

  .clickable-area-mobile {
    top: 79%;
    height: 10%;
  }

  .clickable-area-5g {
    top: 75%;
    height: 15%;
    width: 30%;
    position: absolute;
    cursor: pointer;
    left: 15%;
  }
}

.banner-5g {
  margin-top: 20px;
}

.referral-link-header {
  background: transparent linear-gradient(90deg, #009EF2 0%, #5656E4 100%);
  color: white;
  height: 250px;
  border-radius: 0 0 40px 40px;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-evenly;
  padding: 20px 0;
  box-shadow: 0 3px 6px #00000029;

  .back-arrow {
    position: absolute;
    left: 25px;
    cursor: pointer;
    width: 15px;
    top: 42px;
    transform: rotate(270deg);
  }

  .title {
    font-size: 25px;
    font-weight: bold;
  }

  .agreement-link {
    font-size: 14px;
    text-decoration: underline;
    cursor: pointer;
  }

  hr {
    height: 2px;
    background-color: #2fbff2;
    margin: 20px 0;
    width: 85%;
    border: 0;
  }

  .select-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    label {
      font-weight: normal;
      margin-bottom: 12px;
    }

    img {
      width: 16px;
      margin-left: 5px;
      cursor: pointer;
      padding-bottom: 2px;
    }

    select {
      -webkit-appearance: none; /*Removes default chrome and safari style*/
      -moz-appearance: none;
      background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
      background-position-x: 95%;
      border-radius: 10px;
      border: 0;
      width: 300px;
      color: black;
      padding: 5px 15px;
      font-weight: normal;
      font-size: 20px;
    }
  }
}

.referral-link-layout {
  background-color: white;
  padding-bottom: 1px;
  min-height: 100%;
}

.invite-header {
  text-align: center;
  letter-spacing: 0.55px;
  color: #05C6F2;
  font-size: 20px;
  font-weight: bold;
  width: 85%;
  margin: 35px auto;
}

.referral-link-banner {
  width: 85%;
  max-width: 600px;
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  height: 300px;
  border-radius: 30px;
  margin: 20px auto;
  box-shadow: 0 0 5px 0 rgba(168, 166, 168, 1);

  &.special {
    height: 205px;
  }

  .img-gift {
    width: 120px;
  }

  .img-copy-code {
    width: 200px;
    cursor: pointer;
  }

  span {
    position: absolute;
    color: green;
    margin-top: 265px;
  }
}

.header-banner {
  color: black;
  font-weight: bold;
  font-size: 20px;
  letter-spacing: 1.14px;

  &.special {
    margin-top: 20px;
    text-align: center;
  }
}

.custom-line {
  border-top-color: #707070;
  margin: 40px 0;
}

.small {
  color: #05C6F2;
  font-size: 16px;
}

.stats-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  height: 300px;
  justify-content: space-evenly;

  .star {
    width: 40px;
    margin: -10px 0 10px;
  }

  .text {
    color: #05C6F2;
    font-size: 16px;
    cursor: pointer;
    margin-bottom: 15px;
  }

  .header-stats {
    color: black;
    font-weight: bold;
    font-size: 20px;
    text-align: center;
  }

  img {
    width: 80px;
    margin: 15px 0;
  }

  span {
    letter-spacing: 0.35px;
    color: black;
  }
}

.no-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 25px;
  padding-top: 25px;

  img {
    width: 120px;
  }

  .title {
    color: black;
    font-size: 25px;
    font-weight: bold;
  }

  .description {
    color: #05C6F2;
    text-align: center;
    font-size: 18px;
    width: 90%;
  }
}

.mobile-modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: white;
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.mobile-inner-modal-div {
  position: relative;
  padding: 20px 60px;
}

.back-button {
  position: absolute;
  left: 25px;
  cursor: pointer;
  width: 15px;
  padding-top: 7px;
}

.info-header {
  color: black;
  font-size: 25px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 50px;
}

.info-text {
  color: black;
  font-size: 20px;
  margin: 20px 0;
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 40px;
  top: 15px;
  color: #00b9e2;
  cursor: pointer;
}

.img-info {
  margin: 110px auto 50px;
  display: block;
  width: 70px;
}

.tags-container {
  display: flex;
  justify-content: space-between;
}

.img-download-voucher {
  width: 165px;
  display: block;
  margin: 25px auto;
  cursor: pointer;
}

.porta-un-amico {
  background: transparent linear-gradient(90deg, #009EF2 0%, #5656E4 100%);
  transform: translateY(0);
  transition: transform 1s ease;
}

.optima-young {
  background: transparent linear-gradient(270deg, #DF028A 0%, #FF9D00 100%);
  transform: translateY(0);
  transition: transform 1s ease;
}

.optima-young, .porta-un-amico {
  height: 80px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 25px;
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 1.14px;
  text-shadow: 0 3px 6px #00000057;
  cursor: pointer;
}

.logo-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.img-arrow {
  transform: rotate(180deg);
  height: 10px;
  transition: transform 1s ease;
}

.rotated {
  transform: rotate(0deg);
}

.img-optima-young {
  width: 80px;
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  backdrop-filter: blur(5px);
  overflow: hidden;
  display: none;
}

.inner-modal-div {
  min-height: 335px;
  margin: auto;
  top: 20vh;
  background: #f6f6f6;
  border-radius: 30px;
  border: 2px solid #b1c5df;
  position: relative;
  padding: 35px;
  width: 90%;
  font-size: 16px;
  color: black;
}

.code-container {
  background: white;
  border-radius: 10px;
  box-shadow: inset 0 0 4px 1px rgba(197, 180, 197, 1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 35px auto;
  height: 65px;
  letter-spacing: 2px;
  font-weight: bold;
  font-size: 20px;
  padding: 0 20px;

  img {
    width: 45px;
    cursor: pointer;
  }
}

.inviting-phrase {
  color: #05C6F2;
  margin: 20px 0;
  font-weight: bold;
}

.img-layout {
  width: 100%;
}

.text-layout {
  background-color: #FFBE2B;
  font-weight: bold;
  text-align: center;
  color: black;
  margin-bottom: 2px;
  padding-bottom: 30px;
  font-size: 16px;
  letter-spacing: 0.35px;
}

.buttons-layout {
  display: flex;
  background-color: #E0008A;
  justify-content: space-evenly;
  padding-bottom: 50px;
}

.amazon-button {
  width: 180px;
  cursor: pointer;
}

.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.7s forwards, 2s;
}

.modal-title {
  font-size: 20px;
  font-weight: bold;
  background: linear-gradient(90deg, #009EF2 0%, #5656E4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.image-container {
  position: relative;
  width: 100%;
}

.clickable-area,
.clickable-area-level-3 {
  position: absolute;
  width: 40%;

  &.active {
    cursor: pointer;
  }
}

.clickable-area {
  top: 61%;
  height: 12%;
  left: 30%;
}

.clickable-area-level-3 {
  top: 79%;
  height: 8%;
  left: 30%;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@media screen and (max-width: 400px) {
  .code-container {
    font-size: 15px;
    padding: 0 5px;

    img {
      width: 35px;
    }
  }
  .referral-link-banner {
    .header-banner {
      font-size: 18px;
    }
  }
  .referral-link-header {
    .title {
      font-size: 19px;
    }
  }
}
