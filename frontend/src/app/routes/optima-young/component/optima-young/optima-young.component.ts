import {Component, OnInit} from '@angular/core';
import {OptimaYoungService} from '../../service/optima-young.service';
import {CouponTypes, OptimaYoung, Subscription} from '../../models/OptimaYoungModels';
import {Observable} from 'rxjs/Observable';
import {select} from '@angular-redux/store';
import * as moment from 'moment';
import {UserData} from '../../../../common/model/userData.model';
import {UserDataService} from '../../../../common/services/user-data/userData.service';
import {OffersInAppService} from "../../../offers-in-apps/service/offers-in-app.service";


@Component({
  selector: 'app-optima-young',
  templateUrl: './optima-young.component.html',
  styleUrls: ['./optima-young.component.scss']
})
export class OptimaYoungComponent implements OnInit {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;
  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;
  userData: UserData;

  // modal windows
  showInfoBlock: boolean;
  showTermsModal: boolean;
  showInvalidCodeModal: boolean;
  showAmazonVoucherModal: boolean;
  showCopyCodeModal: boolean;
  copySuccess: boolean;

  // layouts
  showDefaultLayout = true;
  showNoInfo: boolean;
  showPortaITuoiLayout: boolean;
  showPortaUnAmico: boolean;
  showOptimaYoung: boolean;
  showOptimaYoungBanner: boolean;
  is5GoffersAvailable: boolean;

  generalInformation: OptimaYoung;
  selectedMobileIndex = 0;
  selectedMobile: Subscription;
  readonly CouponTypes = CouponTypes;
  fullName: string;
  modalContent = {
    title: '',
    code: '',
    validUntil: ''
  };

  constructor(private readonly optimaYoungService: OptimaYoungService, private readonly userDataService: UserDataService,
              private readonly offersInAppService: OffersInAppService) {
  }

  ngOnInit() {
    this.getInitialInfo();
  }

  getInitialInfo(): void {
    this.optimaYoungService.getInviteFriendsInfo().subscribe(response => {
      this.offersInAppService.getOffer5GData(localStorage.getItem('clientId')).subscribe(offers5G => {
        this.is5GoffersAvailable = offers5G.sim.some(sim => sim.addOnAviable && sim.addOnAviable.length > 0);
        this.generalInformation = response;
        this.showPropertyDefaultLayout();
      });
    });
    this.userDataService.getUserData().subscribe(userInfo => {
      this.fullName = userInfo.nameInInvoice.toLocaleUpperCase();
    });
  }

  showPropertyDefaultLayout(): void {
    if (!this.generalInformation) {
      this.showNoInfo = true;
      return;
    }
    if (this.generalInformation.subscriptions.find(info => info.isAvailableOptimaYoung)) {
      this.showOptimaYoungBanner = true;
    }
    this.updateSelectedMobile();
    const isFirstLevelComplete = this.checkIsRewardAvailable(CouponTypes.LEVEL_1);
    this.showPortaUnAmico = true;
    this.showOptimaYoung = isFirstLevelComplete;
  }

  updateSelectedMobile(): void {
    this.selectedMobile = this.generalInformation.subscriptions[this.selectedMobileIndex];
  }

  toggleProperty(propertyName: keyof this): void {
    this[propertyName] = !this[propertyName];
  }


  toggleLayouts(toDisable: keyof this, toEnable: keyof this): void {
    this[toDisable] = false;
    this[toEnable] = true;
  }

  checkIsInfoPresent(levelType: CouponTypes): boolean {
    return this.selectedMobile.coupons.some(coupon => coupon.couponType === levelType);
  }

  isActiveButton(levelType: CouponTypes): boolean {
    const coupon = this.selectedMobile.coupons.find(c => c.couponType === levelType);
    return coupon && coupon.numComplete !== coupon.numTarget;
  }

  checkIsRewardAvailable(levelType: CouponTypes): boolean {
    const couponInfo = this.selectedMobile.coupons.find(coupon => coupon.couponType === levelType);
    return couponInfo && couponInfo.numComplete === couponInfo.numTarget;
  }

  getRewardReceivedDate(levelType: CouponTypes): string {
    const foundCoupon = this.selectedMobile.coupons.find(coupon => coupon.couponType === levelType);
    if (foundCoupon && foundCoupon.numComplete === foundCoupon.numTarget) {
      return foundCoupon.awardDate;
    }
  }

  getImagePath(levelType: CouponTypes): string {
    const coupon = this.selectedMobile.coupons.find(c => c.couponType === levelType);
    if (!coupon) {
      const defaultImages = {
        [CouponTypes.LEVEL_2]: 'assets/img/optima-young/young/OY.png',
        [CouponTypes.LEVEL_3]: 'assets/img/optima-young/amazon/AMZ.png'
      };
      return defaultImages[levelType] || '';
    }
    const {numComplete, numTarget} = coupon;
    const config = {
      [CouponTypes.LEVEL_2]: {prefix: 'OY_', folder: 'optima-young/young'},
      [CouponTypes.LEVEL_3]: {prefix: 'AMZ_', folder: 'optima-young/amazon'}
    };
    const {prefix, folder} = config[levelType];
    const fileName = numComplete === numTarget ? `${prefix}win.png` : `${prefix}${numComplete}.png`;
    return `assets/img/${folder}/${fileName}`;
  }

  openModalWithCode(levelType: CouponTypes): void {
    const coupon = this.selectedMobile.coupons.find(c => c.couponType === levelType);
    if (!coupon || coupon.numComplete === coupon.numTarget) {
      return;
    }
    const today = moment();
    const isOfferExpired = today.isAfter(moment(coupon.expiryDate, 'DD/MM/YYYY'));
    if (isOfferExpired) {
      this.showInvalidCodeModal = true;
      return;
    }
    const config = {
      [CouponTypes.LEVEL_1]: 'AMICO',
      [CouponTypes.LEVEL_2]: 'YOUNG',
      [CouponTypes.LEVEL_3]: 'YOUNG PRO',
    };
    this.modalContent = {
      title: config[levelType],
      code: coupon.couponCode,
      validUntil: coupon.expiryDate
    };
    this.showCopyCodeModal = true;
  }

  copyCode(code: string): void {
    const hiddenTextArea = document.createElement('textarea');
    hiddenTextArea.value = code;
    document.body.appendChild(hiddenTextArea);
    hiddenTextArea.select();
    document.execCommand('copy');
    document.body.removeChild(hiddenTextArea);
    this.showCopyCodeModal = false;
    this.copySuccess = true;
    setTimeout(() => (this.copySuccess = false), 2000);
  }
}
