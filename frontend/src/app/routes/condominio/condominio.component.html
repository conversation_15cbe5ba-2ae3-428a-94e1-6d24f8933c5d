<app-header></app-header>
<app-spinner class="spinner" *ngIf="isShowSpinner"></app-spinner>
<div class="container">
  <div class="condomini">
    <h1 class="text-center">Benvenuto nella tua Area Riservata</h1>
    <hr>
    <div class="scarica-block">
      <h2>Rendicontazione annuale</h2>
      <div class="custom-input" [formGroup]="formGroupYearReport">
        <select id="yearInput" name="year" formControlName="year">
          <option *ngFor="let year of downloadableYears" value="{{year}}">{{year}}</option>
        </select>
        <img class="img-input" src="assets/img/icons/icon-calendar.png" alt="Custom Icon">
      </div>
      <img class="scarica-button" src="assets/img/icons/download-file-excel.png" alt="Download" (click)="downloadExcelFile()">
    </div>
    <div class="condomini-block">
    <div class="filter">
      <h2>I tuoi condomini:</h2>
      <button (click)="doFilter()" class="btn-filter">{{btnFilter}}</button>
    </div>
      <div *ngIf="nullInfo === 'Condomini non ancora creato'">
        <h1 class="error-info">{{nullInfo}}</h1>
      </div>
    <div class="block-row">
      <div class="block-column" *ngFor="let condominio of condominioList; let i = index" (click)="openNewUser(condominio.customerId)">
        <div *ngIf="isExistNotificationsForUser(condominio.customerId)" class="notification-bell">
          <app-client-notification [notificationList]="getClientNotificationsForClient(condominio.customerId)"></app-client-notification>
        </div>
        <img class="img-condominio" [style.margin-left]="isExistNotificationsForUser(condominio.customerId) ? '5px' : '20px'"
             src="assets/img/condominio/condominio0{{randomImage(i)}}.svg" alt="ColorHouseImage">
        <div class="condominioTextInfo">
          <p class="caption">{{condominio.name|uppercase}}</p>
          <p>Cliente: {{condominio.customerId}}</p>
          <p>CF: {{condominio.fiscalInfo}}</p>
          <p>{{condominio.legalAddress.address}}</p>
          <p>{{condominio.legalAddress.city}} ({{condominio.legalAddress.shortenedProvince}})</p>
        </div>
      </div>
    </div>
  </div>
  </div>
</div>
<app-footer></app-footer>
<div class="modal-div show" *ngIf="showNotFindFileWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <img class="image modal-image" src="/assets/img/icons/error.png" alt="Error">
    <div class="modal-text" *ngIf="this.formGroupYearReport.valid">File non trovato</div>
    <div class="modal-text" *ngIf="!this.formGroupYearReport.valid">È possibile scaricare i file dal 2022 al {{defaultYear}}</div>
  </div>
</div>
