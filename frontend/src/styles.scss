/* You can add global styles to this file, and also import other style files */
//== Vendor
@import '~font-awesome/css/font-awesome.css';
@import '~simple-line-icons/css/simple-line-icons.css';
@import '~weather-icons/css/weather-icons.css';
@import '~weather-icons/css/weather-icons-wind.css';
@import '~spinkit/css/spinkit.css';
@import '~loaders.css/loaders.css';
@import '~angular2-toaster/toaster.css';
@import '~ng2-dnd/bundles/style.css';
@import '~ag-grid/dist/styles/ag-grid.css';
@import '~ag-grid/dist/styles/theme-fresh.css';
@import '~fullcalendar/dist/fullcalendar.min.css';
@import '~codemirror/lib/codemirror.css';
@import '~sweetalert/dist/sweetalert.css';
//== Bootstrap
@import "./app/shared/styles/bootstrap.scss";
//== Application
@import "./app/shared/styles/app.scss";
@import '~@angular/material/prebuilt-themes/deeppurple-amber.css';
@import 'assets/fonts/styles.css';

body {
 overflow-y: auto;
}
/* width */
::-webkit-scrollbar {
  width: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #ffffff;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #36749C;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #ffffff;
}
