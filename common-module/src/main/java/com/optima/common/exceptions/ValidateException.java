package com.optima.common.exceptions;

import java.util.Map;

/**
 * Created on 27.04.17.
 */
public class ValidateException extends Exception {

    private Map<String, String> errors;

    public ValidateException(Map<String, String> errors) {
        this.errors = errors;
    }

    @Override
    public String getMessage() {
        return errors.toString();
    }

    public Map<String, String> getErrorsMap() {
        return errors;
    }
}
