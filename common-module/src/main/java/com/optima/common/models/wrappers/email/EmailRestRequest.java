package com.optima.common.models.wrappers.email;


import lombok.Data;

import java.util.List;

@Data
public class EmailRestRequest {

    private List<Address> destinations;
    private List<?> css = null;
    private List<?> ccns = null;
    private String objectText;
    private String bodyText;
    private String bodyContentType = "text/html";
    private Boolean isPec = false;
    private List<?> attachmentEnailStream = null;
}
