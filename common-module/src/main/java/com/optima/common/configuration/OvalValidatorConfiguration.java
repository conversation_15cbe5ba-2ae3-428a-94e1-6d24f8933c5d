package com.optima.common.configuration;


import com.optima.common.validators.OvalValidator;
import com.optima.common.validators.impl.DefaultOvalValidator;
import net.sf.oval.Validator;
import net.sf.oval.configuration.annotation.AnnotationsConfigurer;
import net.sf.oval.expression.ExpressionLanguageGroovyImpl;
import net.sf.oval.integration.spring.SpringCheckInitializationListener;
import net.sf.oval.integration.spring.SpringInjector;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ComponentScan(basePackageClasses = SpringInjector.class)
public class OvalValidatorConfiguration {

    @Bean
    protected Validator validator() {
        AnnotationsConfigurer myConfigurer = new AnnotationsConfigurer();
        myConfigurer.addCheckInitializationListener(SpringCheckInitializationListener.INSTANCE);
        Validator validator = new Validator(myConfigurer);
        validator.getExpressionLanguageRegistry().registerExpressionLanguage("groovy", new ExpressionLanguageGroovyImpl());
        return validator;
    }

    @Bean
    protected OvalValidator ovalValidator() {
        return new DefaultOvalValidator(this.validator());
    }
}
