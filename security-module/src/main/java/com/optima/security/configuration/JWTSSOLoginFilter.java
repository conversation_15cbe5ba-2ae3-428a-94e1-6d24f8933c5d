package com.optima.security.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.security.exceptions.JwtTokenException;
import com.optima.security.model.TokenValidation;
import com.optima.security.service.impl.TokenAuthenticationService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.FilterChain;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

public class JWTSSOLoginFilter extends AbstractAuthenticationProcessingFilter {

    private static final Logger logger = LogManager.getLogger(JWTSSOLoginFilter.class);

    @Autowired
    private TokenAuthenticationService tokenAuthenticationService;

    public JWTSSOLoginFilter(String url, AuthenticationManager authManager) {
        super(new AntPathRequestMatcher(url));
        setAuthenticationManager(authManager);
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest req, HttpServletResponse res)
            throws AuthenticationException, IOException {
        TokenValidation tokenValidation = new ObjectMapper()
                .readValue(req.getInputStream(), TokenValidation.class);
        if (tokenValidation.getToken() != null) {
            logger.info("{}: Api call: /api/validSSOToken", tokenValidation.getClientId());
            Set<GrantedAuthority> grantedAuthorities = new HashSet<>();
            grantedAuthorities.add(new SimpleGrantedAuthority("ROLE_USER"));
            return getAuthenticationManager().authenticate(
                    new UsernamePasswordAuthenticationToken(tokenValidation.getClientId(), tokenValidation.getToken(), grantedAuthorities));
        } else {
            throw new BadCredentialsException("Invalid X-Optima-Auth token");
        }

    }

    @Override
    protected void successfulAuthentication(HttpServletRequest req, HttpServletResponse res, FilterChain chain,
                                            Authentication auth) throws IOException {
        try {
            tokenAuthenticationService.addAuthentication(req, res, auth);
        } catch (JwtTokenException ignored) {
        }
    }
}
