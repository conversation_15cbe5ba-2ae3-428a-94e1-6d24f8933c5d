package com.optima.security.util;



import org.springframework.stereotype.Service;

import java.util.Random;
@Service
public class PasswordGenerator {
    private Random rnd = new Random();
    private static final String sequence = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";


    public String generate() {
        char[] password = new char[8];

         for (int i = 0; i < 8; i++)
        {
            password[i] =  sequence.charAt(rnd.nextInt(sequence.length()));

        }
        return  String.valueOf(password);
    }
}
