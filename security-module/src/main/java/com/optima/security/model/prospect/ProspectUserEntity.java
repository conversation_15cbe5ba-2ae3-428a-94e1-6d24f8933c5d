package com.optima.security.model.prospect;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;

@Setter
@Getter

@Entity
@Table(name = "AC_USER_PROSPECT_TABLE", schema = "dbo")
public class ProspectUserEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "AC_ID", unique = true,
            nullable = false)
    private Long id;

    @Column(name = "AC_CF", unique = true, nullable = false)
    private String CF;

    @Column(name = "AC_CONTRACT_ID", unique = true)
    private String contractId;

    @Column(name = "AC_PASSWORD", nullable = false)
    @JsonIgnore
    private String password;

    @Column(name = "AC_CREATION_DATE", nullable = false)
    @JsonIgnore
    private Timestamp creationDate;

    @Column(name = "AC_EMAIL", unique = true)
    private String email;

    @Column(name = "AC_TOKEN")
    @JsonIgnore
    private String token;

    @Column(name = "AC_LAST_ACCESS")
    @JsonIgnore
    private Timestamp lastAccess;

    @Column(name = "AC_PASSWORD_MODIFIED_DATE")
    @JsonIgnore
    private Timestamp passwordModifiedDate;


    public ProspectUserEntity(ProspectUserRegister user) {
        this.CF = user.getCF();
        this.email = user.getEmail();
        this.contractId = user.getContractId();
        this.creationDate = new Timestamp(System.currentTimeMillis());
    }

    public ProspectUserEntity() {

    }
}

