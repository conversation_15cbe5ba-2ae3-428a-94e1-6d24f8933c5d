package com.optima.security.model;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

@ConfigurationProperties(prefix = "optima.security")
public class SecurityConfig {

    private List<String> permittedResources;

    public List<String> getPermittedResources() {
        return permittedResources;
    }

    public void setPermittedResources(List<String> permittedResources) {
        this.permittedResources = permittedResources;
    }
}
