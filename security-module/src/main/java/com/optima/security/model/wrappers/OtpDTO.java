package com.optima.security.model.wrappers;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
public class OtpDTO {

    private Long clientId;

    private String otp;

    @Setter(AccessLevel.NONE)
    private Date expirationTime;

    private Boolean expired;

    public OtpDTO setExpirationTime(Date expirationTime) {
        this.expirationTime = expirationTime;
        this.expired = expirationTime == null || expirationTime.getTime() < System.currentTimeMillis();
        return this;
    }

}
