package com.optima.security.model.userData;

import lombok.Data;

@Data
public class Condominio {

    private Long customerId;
    private String name;
    private String fiscalInfo;
    private LegalAddress legalAddress;

    @Override
    public String toString() {
        return "Condominio{" +
                "customerId=" + customerId +
                ", name='" + name + '\'' +
                ", fiscalInfo='" + fiscalInfo + '\'' +
                ", legalAddress=" + legalAddress +
                '}';
    }


    @Data
    public static class LegalAddress {
        private String address;
        private String city;
        private String postalCode;
        private String country;
        private String province;
        private String shortenedProvince;
    }

}


