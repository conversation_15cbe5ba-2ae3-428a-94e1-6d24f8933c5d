package com.optima.security.model.newUserService;

public class ResponseNewUserService {
    private String message;
    private String username;
    private String password;
    private Boolean operationCorrectlyExecuted;

    public ResponseNewUserService(String message, String username, String password, Boolean operationCorrectlyExecuted) {
        this.message = message;
        this.username = username;
        this.password = password;
        this.operationCorrectlyExecuted = operationCorrectlyExecuted;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Boolean getOperationCorrectlyExecuted() {
        return operationCorrectlyExecuted;
    }

    public void setOperationCorrectlyExecuted(Boolean operationCorrectlyExecuted) {
        this.operationCorrectlyExecuted = operationCorrectlyExecuted;
    }
}
