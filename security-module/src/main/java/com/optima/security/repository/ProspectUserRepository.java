package com.optima.security.repository;

import com.optima.security.model.prospect.ProspectUserEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

public interface ProspectUserRepository extends JpaRepository<ProspectUserEntity, Long> {
    @Query("SELECT u FROM ProspectUserEntity u WHERE u.CF = :cf")
    ProspectUserEntity findByCF(@Param("cf") String cf);

    ProspectUserEntity findByEmail(String email);

    ProspectUserEntity findByToken(String token);

    @Transactional
    @Modifying
    @Query("UPDATE ProspectUserEntity u SET u.lastAccess = CURRENT_TIMESTAMP WHERE u.id = :id")
    void updateLastAccessDate(Long id);

    @Transactional
    @Modifying
    @Query("UPDATE ProspectUserEntity u SET u.token = :token WHERE u.id =:id")
    void insertToken(Long id, String token);

    @Transactional
    @Modifying
    @Query("UPDATE ProspectUserEntity u SET u.password = :password, u.passwordModifiedDate = CURRENT_TIMESTAMP " +
            "WHERE u.id = :id AND u.token = :token")
    void updateUserPassword(Long id, String password, String token);

    @Transactional
    @Modifying
    @Query("UPDATE ProspectUserEntity u SET u.token = NULL WHERE u.id = :id")
    void updateUserToken(Long id);
}
