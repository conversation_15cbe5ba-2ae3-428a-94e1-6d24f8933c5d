package com.optima.security.processors.impl;

import com.optima.security.constants.TokenType;
import com.optima.security.exceptions.JwtTokenException;
import com.optima.security.model.JwtToken;
import com.optima.security.model.User;
import com.optima.security.model.userData.UserData;
import com.optima.security.processors.TokenService;
import com.optima.security.service.AuthenticationService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Service;

import java.util.Date;

import static java.util.Collections.emptyList;

@Service
public class AdminTokenServiceImpl implements TokenService {
    private @Value("${restdata.urls.userdata}")
    String userDataUrl;
@Autowired
    private AuthenticationService authenticationService;
    @Value("${optima.security.token.prolonged.expiration-time:#{60*30*1000}}")
    private Long tokenExpirationTime;

    @Value("${optima.security.token.secret}")
    private String tokenSecret;


    @Override
    public AbstractAuthenticationToken process(Claims claims) {

        if (claims.getSubject() != null) {
            JwtToken jwtToken = new JwtToken();
            jwtToken.setUid(claims.getSubject());
            jwtToken.setTokenType(TokenType.ADMINPROLONGED);
            jwtToken.setExpirationTime(claims.getExpiration());
            return new UsernamePasswordAuthenticationToken(jwtToken, null, emptyList());
        }
        return null;
    }

    @Override
    public TokenType getProcessorType() {
        return TokenType.ADMINPROLONGED;
    }

    @Override
    public String buildToken(Object authentication) {
        return null;
    }

    @Override
    public String buildToken(Object authentication, String userId) throws JwtTokenException {
        if (authentication instanceof Authentication) {
            UserData userData = authenticationService.getUserData(userId, userDataUrl);
            User user = authenticationService.login(userId, userData.getPassword());

            String temp  = null;
            try{
                temp = Jwts.builder()
                        .setSubject(userId)
                        .claim("id", Long.parseLong(userId))
                        .claim("tokenType", TokenType.ADMINPROLONGED)
                    .claim("sName",user.getsName())
                    .claim("consumer", user.getConsumer())
                    .claim("billingType", user.getBillingType())
                    .claim("billingAddress", user.getBillingAddress())
                    .claim("sedeLegale", user.getSedeLegale())
                    .claim("referenceNumber", user.getReferenceNumber())
                        .setExpiration(new Date(System.currentTimeMillis() + this.tokenExpirationTime))
                        .signWith(SignatureAlgorithm.HS512, this.tokenSecret)
                        .compact();
            } catch (Exception e){
                temp = Jwts.builder()
                        .setSubject(userId)
                        .claim("id", Long.parseLong(userId))
                        .claim("tokenType", TokenType.ADMINPROLONGED)
                        .setExpiration(new Date(System.currentTimeMillis() + this.tokenExpirationTime))
                        .signWith(SignatureAlgorithm.HS512, this.tokenSecret)
                        .compact();
            }
            return temp;
        }
        return null;
    }

}
