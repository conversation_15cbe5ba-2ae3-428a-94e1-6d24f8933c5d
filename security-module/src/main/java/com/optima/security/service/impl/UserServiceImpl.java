package com.optima.security.service.impl;

import com.optima.security.repository.dao.UserDao;
import com.optima.security.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserServiceImpl implements UserService {
    private final UserDao userDao;

    @Autowired
    public UserServiceImpl(UserDao userDao) {
        this.userDao = userDao;
    }

    @Override
    public void updateLastAccess(Long id) {
        userDao.insertDate(id);
    }
}
