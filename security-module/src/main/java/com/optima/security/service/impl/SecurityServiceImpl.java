package com.optima.security.service.impl;

import com.optima.security.model.AuthenticationToken;
import com.optima.security.service.SecurityService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@Service
public class SecurityServiceImpl implements SecurityService {

    private static final Logger logger = LogManager.getLogger(SecurityService.class);

    private static AuthenticationToken authenticationToken;

    @Value("${restdata.token}")
    private String token;

    @Value("${restdata.urls.token}")
    private String tokenUrl;

    private final RestTemplate restTemplate;

    public SecurityServiceImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }


    @Override
    public AuthenticationToken getToken() {
        if (this.isTokenExpired(authenticationToken)) {
            synchronized (this) {
                if (this.isTokenExpired(authenticationToken)) {
                    logger.info("Services access token has been expired or doesn't exist. Getting new access token...");
                    return getNewToken();
                }
            }
        }
        return authenticationToken;
    }

    @Override
    public AuthenticationToken getNewToken() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("Authorization", token);
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("grant_type", "client_credentials");
        HttpEntity<?> entity = new HttpEntity<Object>(body, headers);
        authenticationToken = restTemplate.postForObject(tokenUrl, entity, AuthenticationToken.class);
        if (authenticationToken != null) {
            logger.info("Services access token has been obtained. Expires in {} seconds.", authenticationToken.getExpiresIn());
        }
        return authenticationToken;
    }

    /**
     * This method is static to prevent circular dependency with RestTemplate. authenticationToken should be moved te interface
     * and implement something like "state logic".
     */
    public static void clear() {
        logger.info("Removing old token...");
        if (authenticationToken != null) {
            logger.info("Old token expiration time {}, token validity time is {} seconds",
                    authenticationToken.getExpiration(), authenticationToken.getExpiresIn());
        }
        authenticationToken = null;
    }

    private boolean isTokenExpired(AuthenticationToken authenticationToken) {
        if (authenticationToken != null && authenticationToken.getExpiration() != null) {
            return authenticationToken.getExpiration() < System.currentTimeMillis();
        }
        return true;
    }
}
