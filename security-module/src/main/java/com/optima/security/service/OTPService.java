package com.optima.security.service;

import com.optima.security.model.OTPRepositoryModel;
import com.optima.security.model.OTPResponse;
import com.optima.security.model.wrappers.OtpDTO;
import org.springframework.http.ResponseEntity;

public interface OTPService {

    String generateOTP(int len);

    OTPRepositoryModel addOtpForUser(Long userId, String otp, String obtainedBy, String ipAddress);

    Boolean checkOtpForUser(Long serId);

    void removeOtpById(Long userId);

    ResponseEntity<OTPResponse> sendOTP(Long userId, String ipAddress);

    void deleteOTPWithExpiredTokens();

    ResponseEntity<OtpDTO> loadByClientIdAndOtp(Long clientId, String otp);

    void updateExpiredPasswords();
}
