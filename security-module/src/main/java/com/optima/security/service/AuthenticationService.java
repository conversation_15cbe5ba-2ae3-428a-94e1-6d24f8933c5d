package com.optima.security.service;


import com.optima.security.model.User;
import com.optima.security.model.userData.PaymentData;
import com.optima.security.model.userData.UserData;
import org.springframework.http.ResponseEntity;

public interface AuthenticationService {

    User login(String username, String password);

    User loginFake(String username, String password);

    UserData getUserData(String clientId, String url);

    PaymentData getUserPaymentData(String clientId, String url);

    ResponseEntity<Boolean> checkIfUserAlreadyRestoredPassword(String clientId, String password);

    ResponseEntity<?> checkSSOToken(String token, String clientId);
}
