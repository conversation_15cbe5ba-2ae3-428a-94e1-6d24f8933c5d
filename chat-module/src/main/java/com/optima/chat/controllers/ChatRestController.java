package com.optima.chat.controllers;

import com.optima.android.service.CustomerFirebaseService;
import com.optima.chat.exceptions.ChatOperationException;
import com.optima.chat.exceptions.ChatValidationException;
import com.optima.chat.exceptions.SecurityCodeException;
import com.optima.chat.models.ClosingChatRequest;
import com.optima.chat.models.MobileChatMessageSendingRequest;
import com.optima.chat.service.*;
import com.optima.chat.wsdl.comms.*;
import com.optima.chat.wsdl.comms.CIDateTime;
import com.optima.chat.wsdl.customer.CIContactWriteType;
import com.optima.chat.wsdl.customer.ReadCustomer;
import com.optima.chat.wsdl.customer.RequestTextChat;
import com.optima.chat.wsdl.customer.RequestTextChatResponse;
import com.optima.chat.wsdl.skill.CISkillsetReadType;
import com.optima.chat.wsdl.skill.GetSkillsetByName;
import com.optima.chat.wsdl.utility.*;
import com.optima.security.model.userData.UserData;
import com.optima.security.service.AuthenticationService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping("/api/chat")
public class ChatRestController {


    private final UtilityService utilityService;

    private final CustomerService customerService;

    private final SkillSetService skillSetService;

    private final CommsService commsService;

    private final ChatService chatService;

    private final ChatMobileService chatMobileService;

    private final CustomerFirebaseService customerFirebaseService;

    private final AuthenticationService authenticationService;

    private static final Logger logger = LogManager.getLogger(ChatRestController.class);

    private  Timer timer = new Timer();

    private @Value("${restdata.urls.userdata}")
    String userDataUrl;

    public ChatRestController(UtilityService utilityService, CustomerService customerService,
                              SkillSetService skillSetService, CommsService commsService,
                              ChatService chatService, ChatMobileService chatMobileService, CustomerFirebaseService customerFirebaseService, AuthenticationService authenticationService) {
        this.utilityService = utilityService;
        this.customerService = customerService;
        this.skillSetService = skillSetService;
        this.commsService = commsService;
        this.chatService = chatService;
        this.chatMobileService = chatMobileService;
        this.customerFirebaseService = customerFirebaseService;
        this.authenticationService = authenticationService;
    }

    @GetMapping(value = "/anonymous/session/key", produces = MediaType.APPLICATION_JSON_VALUE)
    public AnonymousLoginResult getAnonymousSessionKeyResponse() {
        return utilityService.getAnonymousSessionKey().getGetAnonymousSessionKeyResult();
    }

    @PostMapping(value = "/update/customer/id", produces = MediaType.APPLICATION_JSON_VALUE)
    public GetAndUpdateAnonymousCustomerIDResponse getAndUpdateAnonymousCustomerID(
            @RequestBody GetAndUpdateAnonymousCustomerID getAndUpdateAnonymousCustomerID, HttpServletRequest httpServletRequest) {
        GetAndUpdateAnonymousCustomerIDResponse andUpdateAnonymousCustomerID = utilityService.getAndUpdateAnonymousCustomerID(getAndUpdateAnonymousCustomerID);
        httpServletRequest.getSession().setAttribute("customerId", andUpdateAnonymousCustomerID.getGetAndUpdateAnonymousCustomerIDResult());
        return andUpdateAnonymousCustomerID;
    }

    @PostMapping(value = "/read/skillset", produces = MediaType.APPLICATION_JSON_VALUE)
    public CISkillsetReadType readSkillSetByName(@RequestBody GetSkillsetByName skillSetByName) {
        return skillSetService.getSkillsetByNameResponse(skillSetByName).getGetSkillsetByNameResult();
    }


    @PostMapping(value = "/setup/chat", produces = MediaType.APPLICATION_JSON_VALUE)
    public RequestTextChatResponse setupTextChat(@RequestBody RequestTextChat requestTextChat, HttpServletRequest httpServletRequest) {
        requestTextChat.setCustID((Long) httpServletRequest.getSession().getAttribute("customerId"));
        RequestTextChatResponse requestTextChatResponse = chatService.requestTextChat(requestTextChat);
        httpServletRequest.getSession().setAttribute("securityCode", requestTextChatResponse.getRequestTextChatResult());
        return requestTextChatResponse;
    }

    @PostMapping(value = "/update/alive/time", produces = MediaType.APPLICATION_JSON_VALUE)
    public CIDateTime updateAliveTime(@RequestBody UpdateAliveTimeAndUpdateIsTyping updateAliveTimeAndUpdateIsTyping, HttpServletRequest httpServletRequest) {
        updateAliveTimeAndUpdateIsTyping.setContactID((Long) httpServletRequest.getSession().getAttribute("securityCode"));
        return commsService.updateAliveTimeAndUpdateIsTyping(updateAliveTimeAndUpdateIsTyping).getUpdateAliveTimeAndUpdateIsTypingResult();
    }

    @PostMapping(value = "/get/comfort/message", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<CICOnHoldMessages> getOnHoldComfortMessages(@RequestBody GetContactOnHoldMessages updateAliveTimeAndUpdateIsTyping,
                                                            HttpServletRequest httpServletRequest) throws SecurityCodeException {
        Object securityCode = httpServletRequest.getSession().getAttribute("securityCode");
        if (securityCode != null) {
            updateAliveTimeAndUpdateIsTyping.setContactId((Long) securityCode);
            CIMultipleOnHoldMessages getContactOnHoldMessagesResult
                    = commsService.getContactOnHoldMessages(updateAliveTimeAndUpdateIsTyping).getGetContactOnHoldMessagesResult();
            return getContactOnHoldMessagesResult.getListOfOnHoldMessages() != null ?
                    getContactOnHoldMessagesResult.getListOfOnHoldMessages().getCICOnHoldMessages() : null;
        }
        throw new SecurityCodeException("No security code has been found.");
    }

    @PostMapping(value = "/get/history", produces = MediaType.APPLICATION_JSON_VALUE)
    public CIMultipleChatMessageReadType getHistory(@RequestBody ReadChatMessage readChatMessage,
                                                    HttpServletRequest httpServletRequest) throws SecurityCodeException {
        Object securityCode = httpServletRequest.getSession().getAttribute("securityCode");
        if (securityCode != null) {
            readChatMessage.setContactID((Long) securityCode);
            ReadChatMessageResponse readChatMessageResponse = commsService.readChatMessage(readChatMessage);
            if (readChatMessageResponse.getReadChatMessageResult() != null &&
                    readChatMessageResponse.getReadChatMessageResult().getListOfChatMessages() != null &&
                    readChatMessageResponse.getReadChatMessageResult().getListOfChatMessages().getCIChatMessageReadType() != null) {
                readChatMessageResponse.getReadChatMessageResult().getListOfChatMessages().getCIChatMessageReadType().forEach(message -> {
                    logger.info("message! {} --- {} --- {} " , message.getChatMessageType(), message.getChatMessage(), message.getHiddenMessage());
                    if (message.getChatMessageType() == CIChatMessageType.SESSION_DISCONNECTED_BY_AGENT) {
                        logger.info("Session has been disconnected by agent. Message {}", message);
                    }
                });
            }
            return commsService.readChatMessage(readChatMessage).getReadChatMessageResult();
        }
        throw new SecurityCodeException("No security code has been found.");
    }

    @PostMapping(value = "/get/hold/urls", produces = MediaType.APPLICATION_JSON_VALUE)
    public CIMultipleOnHoldURLReadType getWebOnHoldURLs(@RequestBody GetWebOnHoldURLs request) {
        return commsService.getWebOnHoldURLsResponse(request).getGetWebOnHoldURLsResult();
    }

    @PostMapping(value = "/leave/queue", produces = MediaType.APPLICATION_JSON_VALUE)
    public AbandonQueuingWebCommsContactResponse removeChatContactFromWaitingQueue(@RequestBody ClosingChatRequest request) {
        return commsService.removeChatContactFromWaitingQueue(request);
    }

    @PostMapping(value = "/send/message", produces = MediaType.APPLICATION_JSON_VALUE)
    public WriteChatMessageResponse sendChatMessage(@RequestBody WriteChatMessage message) {
        return commsService.sendMessage(message);
    }

    @PostMapping(value = "/customer/info", produces = MediaType.APPLICATION_JSON_VALUE)
    public com.optima.chat.wsdl.customer.CICustomerReadType getCustomerInfo(@RequestBody ReadCustomer readCustomer, HttpServletRequest httpServletRequest) {
        Object customerId = httpServletRequest.getSession().getAttribute("customerId");
        if (customerId != null) {
            readCustomer.setId((Long) httpServletRequest.getSession().getAttribute("customerId"));
            return customerService.getCustomerUserNameById(readCustomer).getReadCustomerResult();
        }
        throw new ChatValidationException("No client id has been found.");
    }

    @PostMapping(value = "/close/chat", produces = MediaType.APPLICATION_JSON_VALUE)
    public CustomerLogoffByContactIDResponse closeTextChat(@RequestBody CustomerLogoffByContactID request,
                                                           HttpServletRequest httpServletRequest) throws SecurityCodeException {
        Object securityCode = httpServletRequest.getSession().getAttribute("securityCode");
        if (securityCode != null) {
            request.setContactId((Long) securityCode);
            return utilityService.closeTextChat(request);
        }
        throw new SecurityCodeException("No security code has been found.");
    }

    @PostMapping(value = "/mobile/initChat", produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, Object> initChatMobile(@RequestBody MobileChatMessageSendingRequest data,
                                                    HttpServletRequest httpServletRequest) throws SecurityCodeException {
        logger.info("MOBILE: Preparing init chat for user {}", data.getCustomerId());
        logger.info("HTTP SESSION(INIT CHAT): is session new? - {}", httpServletRequest.getSession().isNew());
        if (!httpServletRequest.getSession().isNew()) {
            logger.info("HTTP SESSION(INIT CHAT): JSESSION ID - {}", httpServletRequest.getCookies()[0].getValue());
        }

        String customerId = data.getCustomerId();
        UserData userData = authenticationService.getUserData(customerId, userDataUrl);
        /* anonymous/session/key */
        AnonymousLoginResult loginResult = utilityService.getAnonymousSessionKey().getGetAnonymousSessionKeyResult();
        /* update/customer/id */
        setVariableForUpdateCustomerIdMethod(userData, loginResult, httpServletRequest);
        // read/skillset
        GetSkillsetByName skillsetByName = setVariableForReadSkillset(loginResult);
        CISkillsetReadType ciSkillsetReadType =
                skillSetService.getSkillsetByNameResponse(skillsetByName).getGetSkillsetByNameResult();
        /* setup/chat */
        setVariableForSetupChat(loginResult, ciSkillsetReadType, httpServletRequest);

        Map<String, Object> result = new HashMap<>();
        result.put("loginResult", loginResult);
        return  result;
    }


    @PostMapping(value = "/mobile/readFromSupport", produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, Object> readMessageFromSupport(@RequestBody(required = false) ReadChatMessage readChatMessage,
                                               HttpServletRequest httpServletRequest) throws SecurityCodeException {

        logger.info("MOBILE: Preparing to read messages from the agent");
        logger.info("HTTP SESSION(READ FROM SUPPORT): is session new? - {}", httpServletRequest.getSession().isNew());
        if (!httpServletRequest.getSession().isNew()) {
            logger.info("HTTP SESSION(READ FROM SUPPORT): JSESSION ID - {}", httpServletRequest.getCookies()[0].getValue());
        }
        if (readChatMessage == null) {
            cancelTimer();
            return  null;
        }
        else {
            this.timer = new Timer();

            Map<String, Object> response = new HashMap<>();
            String customerId = httpServletRequest.getSession().getAttribute("customerId").toString();
            String userId = httpServletRequest.getSession().getAttribute("userId").toString();
            logger.info("customerID obtained {}", customerId);
            String sessionKey = httpServletRequest.getSession().getAttribute("sessionKey").toString();
            logger.info("sessionKey obtained {}", sessionKey);
            Object securityCode = httpServletRequest.getSession().getAttribute("securityCode");
            logger.info("securityCode obtained {}", securityCode);


            if (securityCode != null) {
                readChatMessage.setContactID((Long) securityCode);
                timer.schedule(new TimerTask() {
                    int messagesArrayLength = 0;

                    @Override
                    public void run() {
                        ReadChatMessageResponse readChatMessageResponse = commsService.readChatMessage(readChatMessage);
                        logger.info("MOBILE: Waiting message from agent....");
                        if (readChatMessageResponse.getReadChatMessageResult() != null &&
                                readChatMessageResponse.getReadChatMessageResult().getListOfChatMessages() != null &&
                                readChatMessageResponse.getReadChatMessageResult().getListOfChatMessages().getCIChatMessageReadType() != null) {
                            List<CIChatMessageReadType> ciChatMessageReadType = readChatMessageResponse.getReadChatMessageResult().getListOfChatMessages().getCIChatMessageReadType();
                            if (messagesArrayLength < ciChatMessageReadType.size()) {
                                messagesArrayLength = ciChatMessageReadType.size();
                                if (ciChatMessageReadType.get(messagesArrayLength - 1).getChatMessageType() == CIChatMessageType.CHAT_MESSAGE_FROM_AGENT ) {
                                    chatMobileService.sendMessageToMobileDevice(ciChatMessageReadType.get(messagesArrayLength-1).getChatMessage(), userId);
                                    logger.info("MOBILE: Message from agent - {}",  ciChatMessageReadType.get(messagesArrayLength-1).getChatMessage());
                                }
                                if (ciChatMessageReadType.get(messagesArrayLength - 1).getChatMessageType() == CIChatMessageType.SESSION_DISCONNECTED_BY_AGENT) {
                                    chatMobileService.sendMessageToMobileDevice("session closed", userId);
                                    logger.info("MOBILE: Session closed by agent.");
                                    cancelTimer();
                                    executeCloseChat(securityCode, sessionKey, customerId);
                                }
                                response.put("messagesList", readChatMessageResponse.getReadChatMessageResult().getListOfChatMessages());
                            }
                        }
                    }
                }, 0, 1000);

                return response;
            }
            throw new SecurityCodeException("No security code has been found.");
        }

    }

    @PostMapping(value = "/mobile/sendToSupport", produces = MediaType.APPLICATION_JSON_VALUE)
    public WriteChatMessageResponse sendToSupport(@RequestBody WriteChatMessage message, HttpServletRequest httpServletRequest)throws SecurityCodeException  {
        WriteChatMessageResponse  response = null;
        logger.info("MOBILE: Send message to support from mobile device {}", message.getMessage());
        logger.info("HTTP SESSION(SEND TO SUPPORT): is session new? - {}", httpServletRequest.getSession().isNew());
        if (!httpServletRequest.getSession().isNew()) {
            logger.info("HTTP SESSION(SEND TO SUPPORT): JSESSION ID - {}", httpServletRequest.getCookies()[0].getValue());
        }
        Object securityCode = httpServletRequest.getSession().getAttribute("securityCode");
        message.setContactID((Long)securityCode);
        try{
            response = commsService.sendMessage(message);
        }catch (Exception e){
            MobileChatMessageSendingRequest req = new MobileChatMessageSendingRequest();
            req.setCustomerId((String)httpServletRequest.getSession().getAttribute("customerId"));
            initChatMobile(req, httpServletRequest);
            response = commsService.sendMessage(message);
        }
        return response;
    }

    @PostMapping(value = "/mobile/closeChat", produces = MediaType.APPLICATION_JSON_VALUE)
    private CustomerLogoffByContactIDResponse closeMobileTextChat(HttpServletRequest httpServletRequest) throws SecurityCodeException {
        Object sessionKey = httpServletRequest.getSession().getAttribute("sessionKey");
        if (sessionKey != null) {
            Object securityCode = httpServletRequest.getSession().getAttribute("securityCode");
            Object customerId = httpServletRequest.getSession().getAttribute("securityCode");
            if (securityCode != null && (Long) securityCode > 0) {
                executeCloseChat(securityCode, sessionKey.toString(), customerId.toString());
            } else {
                // if !agentAccepted.getValue()
                ClosingChatRequest closingChatRequest = new ClosingChatRequest();
                closingChatRequest.setContactID((Long) securityCode);
                closingChatRequest.setSessionKey((String) sessionKey);
                closingChatRequest.setClosureComment("");
                commsService.removeChatContactFromWaitingQueue(closingChatRequest);
            }
        }
        throw new SecurityCodeException("No session key has been found.");
    }

    private CustomerLogoffByContactIDResponse executeCloseChat(Object securityCode, String sessionKey, String customerId) {
        ReadChatMessage readChatMessage = new ReadChatMessage();
        readChatMessage.setContactID((Long) securityCode);
        readChatMessage.setSessionKey(sessionKey);
        readChatMessage.setLastReadTime(new CIDateTime());
//                readChatMessage.setIsWriting(true);
        ReadChatMessageResponse readChatMessageResponse = commsService.readChatMessage(readChatMessage);
        if (readChatMessageResponse.getReadChatMessageResult() != null &&
                readChatMessageResponse.getReadChatMessageResult().getListOfChatMessages() != null &&
                readChatMessageResponse.getReadChatMessageResult().getListOfChatMessages().getCIChatMessageReadType() != null) {

            WriteChatMessage chatMessage = new WriteChatMessage();
            chatMessage.setSessionKey(sessionKey);
            chatMessage.setChatMessageType(CIChatMessageType.SESSION_DISCONNECTED_BY_CUSTOMER);
            chatMessage.setMessage("Session Disconnected");
            chatMessage.setContactID((Long) securityCode);
            commsService.sendMessage(chatMessage);

            if (customerId != null) {
                ReadCustomer readCustomer = new ReadCustomer();
                readCustomer.setId(Long.valueOf(customerId));
                readCustomer.setSessionKey(sessionKey);
                // call 'api/chat/customer/info'
                com.optima.chat.wsdl.customer.CICustomerReadType ciCustomerReadType =
                        customerService.getCustomerUserNameById(readCustomer).getReadCustomerResult();

                CustomerLogoffByContactID request = new CustomerLogoffByContactID();
                request.setContactId((Long) securityCode);
                request.setSessionKey(sessionKey);
                request.setUsername(ciCustomerReadType.getUsername());
                return utilityService.closeTextChat(request);
            }
        }
        throw new ChatOperationException("Error while closing chat session");
    }

    private void cancelTimer() {
        timer.cancel();
        timer.purge();
    }

    private void setVariableForUpdateCustomerIdMethod(UserData userData, AnonymousLoginResult loginResult,
                                                      HttpServletRequest httpServletRequest) {
        GetAndUpdateAnonymousCustomerID anonymousCustomerID = new GetAndUpdateAnonymousCustomerID();
        anonymousCustomerID.setLoginResult(loginResult);
        anonymousCustomerID.setEmailAddress(userData.getEmail());
        anonymousCustomerID.setPhoneNumber(userData.getPhoneNumber());

        CICustomerReadType cICustomerReadType = new CICustomerReadType();
        cICustomerReadType.setFirstName(userData.getFirstName());
        cICustomerReadType.setLastName(userData.getLastName());
        cICustomerReadType.setUsername(userData.getName());
        anonymousCustomerID.setThisCustomer(cICustomerReadType);

        httpServletRequest.getSession().setAttribute("userId", userData.getId());

        GetAndUpdateAnonymousCustomerIDResponse andUpdateAnonymousCustomerID = utilityService.getAndUpdateAnonymousCustomerID(anonymousCustomerID);
        httpServletRequest.getSession().setAttribute("customerId", andUpdateAnonymousCustomerID.getGetAndUpdateAnonymousCustomerIDResult());
    }

    private void setVariableForSetupChat(AnonymousLoginResult loginResult, CISkillsetReadType ciSkillsetReadType,
                                         HttpServletRequest httpServletRequest) {
        RequestTextChat requestTextChat = new RequestTextChat();
        requestTextChat.setCustID((Long) httpServletRequest.getSession().getAttribute("customerId"));

        CIContactWriteType ciContactWriteType = new CIContactWriteType();
        ciContactWriteType.setSkillsetID(ciSkillsetReadType.getId());
        ciContactWriteType.setCallbackTime(new com.optima.chat.wsdl.customer.CIDateTime());
        ciContactWriteType.setTimezone((short) 999);
        //also need set customFields
        requestTextChat.setNewContact(ciContactWriteType);
        requestTextChat.setSessionKey(loginResult.getSessionKey());

        RequestTextChatResponse requestTextChatResponse = chatService.requestTextChat(requestTextChat);
        httpServletRequest.getSession().setAttribute("securityCode", requestTextChatResponse.getRequestTextChatResult());
        httpServletRequest.getSession().setAttribute("sessionKey", loginResult.getSessionKey());
    }

    private GetSkillsetByName setVariableForReadSkillset(AnonymousLoginResult loginResult) {
        GetSkillsetByName getSkillsetByName = new GetSkillsetByName();
        getSkillsetByName.setSessionKey(loginResult.getSessionKey());
        getSkillsetByName.setSkillsetName("WC_Informativo_app");
        return getSkillsetByName;
    }
}
