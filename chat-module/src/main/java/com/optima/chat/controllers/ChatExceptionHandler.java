package com.optima.chat.controllers;

import com.optima.chat.exceptions.ChatValidationException;
import com.optima.chat.exceptions.SecurityCodeException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@ControllerAdvice
public class ChatExceptionHandler {

    private static final Logger logger = LogManager.getLogger(ChatExceptionHandler.class);

    @ExceptionHandler(value = SecurityCodeException.class)
    protected ResponseEntity handleSecurityCodeException(SecurityCodeException e) {
        logger.error("Error while trying to obtain security code. {}", e);
        return new ResponseEntity(HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = ChatValidationException.class)
    protected ResponseEntity handleChatValidationException(ChatValidationException e) {
        logger.info("Validation exception. {}", e);
        return new ResponseEntity(HttpStatus.BAD_REQUEST);
    }

}
