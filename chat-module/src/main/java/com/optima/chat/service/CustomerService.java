package com.optima.chat.service;

import com.optima.chat.wsdl.customer.ReadCustomer;
import com.optima.chat.wsdl.customer.ReadCustomerResponse;
import com.optima.chat.wsdl.customer.RequestTextChat;
import com.optima.chat.wsdl.customer.RequestTextChatResponse;

public interface CustomerService {

    RequestTextChatResponse requestTextChat(RequestTextChat requestTextChat);

    ReadCustomerResponse getCustomerUserNameById(ReadCustomer readCustomer);

}
