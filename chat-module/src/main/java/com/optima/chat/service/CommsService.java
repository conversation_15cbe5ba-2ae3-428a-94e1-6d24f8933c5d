package com.optima.chat.service;

import com.optima.chat.models.ClosingChatRequest;
import com.optima.chat.wsdl.comms.*;

public interface CommsService {

    UpdateAliveTimeAndUpdateIsTypingResponse updateAliveTimeAndUpdateIsTyping(UpdateAliveTimeAndUpdateIsTyping updateAliveTimeAndUpdateIsTyping);

    GetContactOnHoldMessagesResponse getContactOnHoldMessages(GetContactOnHoldMessages contactOnHoldMessages);

    ReadChatMessageResponse readChatMessage(ReadChatMessage readChatMessage);

    GetWebOnHoldURLsResponse getWebOnHoldURLsResponse(GetWebOnHoldURLs getWebOnHoldURLs);

    AbandonQueuingWebCommsContactResponse removeChatContactFromWaitingQueue(ClosingChatRequest request);

    WriteChatMessageResponse sendMessage(WriteChatMessage message);


}
