package com.optima.chat.service.impl;

import com.optima.chat.service.CustomerService;
import com.optima.chat.wsdl.customer.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.client.core.SoapActionCallback;

public class CustomerServiceImpl extends WebServiceGatewaySupport implements CustomerService {

    @Value("${optima.chat.soap.ci.customer.ws.url}")
    private String cICustomerWs;

    public RequestTextChatResponse requestTextChat(RequestTextChat requestTextChat) {
        requestTextChat.getNewContact().setPriority(CIContactPriority.PRIORITY_3_MEDIUM_HIGH);
        return (RequestTextChatResponse) getWebServiceTemplate()
                .marshalSendAndReceive(this.cICustomerWs,
                        requestTextChat, new SoapActionCallback("http://webservices.ci.ccmm.applications.nortel.com/RequestTextChat"));
    }

    @Override
    public ReadCustomerResponse getCustomerUserNameById(ReadCustomer readCustomer) {
        return (ReadCustomerResponse) getWebServiceTemplate()
                .marshalSendAndReceive(this.cICustomerWs,
                        readCustomer, new SoapActionCallback("http://webservices.ci.ccmm.applications.nortel.com/ReadCustomer"));
    }

}
